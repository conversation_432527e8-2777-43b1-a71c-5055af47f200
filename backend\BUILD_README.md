# GeoStream Integration 构建说明

## 概述
本文档说明如何构建包含GDB解析功能的GeoStream Integration可执行文件。

## 新增功能
- **GDB文件解析**: 支持读取Esri Geodatabase文件
- **多种解析引擎**: 
  - Fiona (推荐，轻量级)
  - GDAL/OGR (功能完整)
- **坐标系识别**: 自动识别和简化显示坐标系信息
- **字段信息展示**: 智能显示图层字段信息
- **分页支持**: 大量图层的分页显示

## 依赖库

### 必需依赖
```bash
pip install flask flask-cors redis psutil fiona
```

### 可选依赖 (增强功能)
```bash
pip install GDAL shapely
```

## 构建方法

### 方法1: 使用批处理文件 (推荐)
```bash
# 双击运行或在命令行执行
build.bat
```

### 方法2: 完整构建
```bash
python build.py
```

### 方法3: 快速构建
```bash
python build_simple.py
```

## 构建选项说明

### 完整构建 (build.py)
- 包含依赖检查
- 详细的错误报告
- 完整的库支持
- 运行时环境设置

### 快速构建 (build_simple.py)
- 简化的构建过程
- 基础功能支持
- 更快的构建速度
- 适合开发测试

## 构建输出

构建成功后，会在 `dist/` 目录中生成可执行文件：
- `GeoStream Integration.exe` (完整构建)
- `GeoStream_Integration.exe` (快速构建)

## 故障排除

### 常见问题

1. **GDAL库安装失败**
   ```bash
   # Windows用户可以使用conda
   conda install -c conda-forge gdal
   
   # 或使用预编译包
   pip install GDAL --find-links https://www.lfd.uci.edu/~gohlke/pythonlibs/
   ```

2. **Fiona安装失败**
   ```bash
   # 使用conda安装
   conda install -c conda-forge fiona
   ```

3. **构建文件过大**
   - 使用快速构建选项
   - 排除不必要的库
   - 启用UPX压缩 (移除 --noupx 选项)

4. **运行时错误**
   - 检查runtime_hooks.py是否正确设置
   - 确保GDAL数据文件被正确包含

### 环境变量
构建的可执行文件会自动设置以下环境变量：
- `GDAL_DATA`: GDAL数据文件路径
- `PROJ_LIB`: PROJ库数据路径
- `FIONA_ENV`: Fiona环境设置

## 测试构建结果

1. 运行生成的exe文件
2. 上传GDB压缩包测试
3. 检查图层解析是否正常
4. 验证坐标系识别功能

## 版本信息

- 支持的GDB版本: 9.x, 10.x
- 支持的坐标系: EPSG, CGCS2000, Beijing54, Xian80, WGS84
- 支持的几何类型: Point, Line, Polygon

## 联系支持

如果遇到构建问题，请检查：
1. Python版本 (推荐3.8+)
2. 依赖库版本兼容性
3. 系统环境变量设置
4. 防病毒软件干扰

from flask import Flask, request, jsonify, send_file, send_from_directory, Response, make_response
from flask_cors import CORS
import sqlite3
import os
import hashlib
import psutil
import win32api
import win32file
from parse_fmw import parse_fmw_parameters
import json
import subprocess
import shutil
import zipfile
import rarfile  # 替换 unrar
from datetime import datetime
import xml.etree.ElementTree as ET
from config_reader import read_config
import logging
import random
import requests
import time
from config import SERVER_CONFIG, CORS_CONFIG, FRONTEND_CONFIG
from redis_utils import RedisManager
from redis_config import REDIS_CONFIG, REDIS_KEYS, REDIS_EXPIRE
import sys
import threading
import atexit
import signal
import redis
import win32gui
import win32con
import urllib.parse
import re
from urllib.parse import quote
from PIL import Image, ImageDraw, ImageFont
import io
import string
import base64
from pypinyin import lazy_pinyin, Style
import socket
from flask import abort
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
import tempfile
from license_utils import load_license, is_time_valid
from datetime import datetime


# 获取当前文件所在目录
if getattr(sys, 'frozen', False):
    # 如果是打包后的exe，使用exe所在目录
    current_dir = os.path.dirname(sys.executable)
else:
    # 在开发环境下，使用当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

# 构建静态文件夹的绝对路径（当前目录下的dist目录）
static_folder = os.path.join(current_dir, 'dist')

# 构建其他目录的绝对路径
TEMP_DIR = os.path.join(current_dir, 'temp')
MODELS_DIR = os.path.join(current_dir, 'models')
REQUIREMENT_DIR = os.path.join(current_dir, 'requirement_submissions')
REDIS_DIR = os.path.join(current_dir, 'redis')

# 验证码存储字典，用于临时存储验证码和登录失败次数
captcha_store = {}
login_fail_count = {}

# 确保必要的目录存在
for dir_path in [TEMP_DIR, MODELS_DIR, REQUIREMENT_DIR]:
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)

print(f"静态文件夹路径: {static_folder}")
print(f"静态文件夹是否存在: {os.path.exists(static_folder)}")
print(f"index.html是否存在: {os.path.exists(os.path.join(static_folder, 'index.html'))}")

# 创建Flask应用
app = Flask(__name__,
            static_folder=static_folder,  # 使用绝对路径指定前端打包后的静态文件目录
            static_url_path='')  # 静态文件URL路径为根路径

# 配置 UPLOAD_FOLDER
app.config['UPLOAD_FOLDER'] = REQUIREMENT_DIR  # 使用已定义的REQUIREMENT_DIR作为上传目录

# 配置文件上传大小限制为10GB
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024

# 配置 CORS
CORS(app, resources={
    r"/api/*": CORS_CONFIG,
    r"/gsi/api/*": CORS_CONFIG
})

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(current_dir, 'app.log'))
    ]
)
logger = logging.getLogger(__name__)

# 全局任务状态字典，用于存储GDB解析任务的进度
task_status = {}

def generate_captcha():
    """生成验证码图片和文本"""
    # 生成4位随机验证码
    captcha_text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    
    # 创建图片
    width, height = 120, 40
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # 添加干扰线
    for i in range(5):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)
        draw.line([(x1, y1), (x2, y2)], fill='gray', width=1)
    
    # 添加干扰点
    for i in range(20):
        x = random.randint(0, width)
        y = random.randint(0, height)
        draw.point((x, y), fill='gray')
    
    # 绘制验证码文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果找不到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 计算文字位置，使其居中
    text_bbox = draw.textbbox((0, 0), captcha_text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # 绘制文字，添加随机颜色
    colors = ['black', 'darkblue', 'darkgreen', 'darkred', 'darkmagenta']
    for i, char in enumerate(captcha_text):
        color = random.choice(colors)
        char_x = x + i * (text_width // 4)
        char_y = y + random.randint(-2, 2)  # 添加轻微的垂直偏移
        draw.text((char_x, char_y), char, fill=color, font=font)
    
    # 转换为base64
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return captcha_text, img_str

def get_login_fail_count(username):
    """获取用户登录失败次数"""
    return login_fail_count.get(username, 0)

def increment_login_fail_count(username):
    """增加用户登录失败次数"""
    if username not in login_fail_count:
        login_fail_count[username] = 0
    login_fail_count[username] += 1
    return login_fail_count[username]

def reset_login_fail_count(username):
    """重置用户登录失败次数"""
    if username in login_fail_count:
        del login_fail_count[username]
    if username in captcha_store:
        del captcha_store[username]

def add_gsi_routes():
    """为所有API路由添加GSI版本"""
    logger.info("开始为API路由添加GSI版本...")

    # 获取所有现有的路由规则
    api_rules = []
    for rule in app.url_map.iter_rules():
        if rule.rule.startswith('/api/') and not rule.rule.startswith('/gsi/'):
            api_rules.append(rule)

    # 为每个API路由添加GSI版本
    added_count = 0
    for rule in api_rules:
        try:
            gsi_rule = rule.rule.replace('/api/', '/gsi/api/', 1)
            # 获取视图函数
            view_func = app.view_functions.get(rule.endpoint)
            if view_func:
                # 创建新的端点名称
                gsi_endpoint = f'gsi_{rule.endpoint}'
                # 添加GSI路由
                app.add_url_rule(
                    gsi_rule,
                    gsi_endpoint,
                    view_func,
                    methods=rule.methods
                )
                logger.info(f"添加GSI路由: {gsi_rule} -> {rule.rule}")
                added_count += 1
        except Exception as e:
            logger.warning(f"无法为 {rule.rule} 添加GSI路由: {e}")

    logger.info(f"GSI路由添加完成，共添加 {added_count} 个路由")

    # 列出一些关键的质检API路由
    quality_routes = ['/gsi/api/quality/upload-gdb', '/gsi/api/quality/items', '/gsi/api/quality/config/save']
    logger.info("检查关键质检API路由:")
    for route in quality_routes:
        found = False
        for rule in app.url_map.iter_rules():
            if rule.rule == route:
                found = True
                break
        status = "OK" if found else "MISSING"
        logger.info(f"  {route}: {status}")



# 添加一个明确的根路由，优先级高于通用路由
@app.route('/')
def index():
    return app.send_static_file('index.html')

# 明确列出常见的前端路由路径，确保它们能被正确处理
@app.route('/login')
@app.route('/dashboard')
@app.route('/market')
@app.route('/tools')
@app.route('/quality')
@app.route('/coordinate')
@app.route('/cad2gis')
@app.route('/layer-preview')
@app.route('/user-management')
@app.route('/settings')
@app.route('/requirement')
@app.route('/about')
def frontend_routes():
    """明确处理常见前端路由，返回index.html"""
    logger.info(f"访问前端路由: {request.path}，返回index.html")
    index_path = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(index_path):
        return send_file(index_path)
    else:
        logger.error(f"index.html文件不存在: {index_path}")
        return "找不到前端文件，请确认静态文件已正确部署", 404

# 添加/gsi/路径的支持 - 处理带有/gsi/前缀的前端路由
@app.route('/gsi/')
def gsi_index():
    """处理/gsi/根路径"""
    logger.info(f"访问GSI根路径: {request.path}，返回index.html")
    index_path = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(index_path):
        return send_file(index_path)
    else:
        logger.error(f"index.html文件不存在: {index_path}")
        return "找不到前端文件，请确认静态文件已正确部署", 404

# 添加GSI静态资源路由
@app.route('/gsi/<path:filename>')
def gsi_static(filename):
    """处理/gsi/静态资源请求"""
    logger.info(f"GSI静态资源请求: {filename}")

    # 检查是否是assets目录下的文件
    if filename.startswith('assets/'):
        static_file_path = os.path.join(app.static_folder, filename)
        if os.path.exists(static_file_path) and os.path.isfile(static_file_path):
            logger.info(f"提供GSI静态文件: {filename}")
            return send_from_directory(app.static_folder, filename)

    # 检查是否是根目录下的静态文件（如favicon.ico等）
    static_file_path = os.path.join(app.static_folder, filename)
    if os.path.exists(static_file_path) and os.path.isfile(static_file_path):
        logger.info(f"提供GSI根静态文件: {filename}")
        return send_from_directory(app.static_folder, filename)

    # 如果不是静态文件，检查是否是前端路由
    if filename in ['login', 'dashboard', 'market', 'tools', 'quality',
                    'coordinate', 'cad2gis', 'layer-preview', 'user-management',
                    'settings', 'requirement', 'about', 'development-mode']:
        logger.info(f"GSI前端路由: {filename}，返回index.html")
        index_path = os.path.join(app.static_folder, 'index.html')
        if os.path.exists(index_path):
            return send_file(index_path)
        else:
            logger.error(f"index.html文件不存在: {index_path}")
            return "找不到前端文件，请确认静态文件已正确部署", 404

    # 其他情况返回404
    logger.warning(f"GSI路径未找到: {filename}")
    return "文件未找到", 404



# 通用路由，捕获所有其他路径（不包括GSI路径，GSI路径有专门的路由处理）
@app.route('/<path:path>')
def catch_all(path):
    """通用的前端路由处理函数，处理所有非GSI请求"""
    logger.info(f"接收到请求: {path}")

    # GSI路径已经由专门的路由处理，这里不再处理

    # 检查是否是静态资源请求
    static_file_path = os.path.join(app.static_folder, path)
    if path and os.path.exists(static_file_path) and os.path.isfile(static_file_path):
        logger.info(f"提供静态文件: {path}")
        return send_from_directory(app.static_folder, path)

    # 对于所有其他请求（包括前端路由），返回index.html
    index_path = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(index_path):
        logger.info(f"返回index.html处理前端路由: {path}")
        return send_file(index_path)
    else:
        logger.error(f"index.html文件不存在: {index_path}")
        return "找不到前端文件，请确认静态文件已正确部署", 404

def start_redis_server():
    """启动Redis服务器"""
    try:
        # 获取Redis可执行文件路径
        redis_exe = os.path.join(REDIS_DIR, 'redis-server.exe')
        redis_conf = os.path.join(REDIS_DIR, 'redis.windows.conf')
        
        if not os.path.exists(redis_exe):
            logger.error(f"Redis服务器可执行文件不存在: {redis_exe}")
            return False
            
        if not os.path.exists(redis_conf):
            logger.error(f"Redis配置文件不存在: {redis_conf}")
            return False

        # 检查是否已有Redis进程在运行
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'redis-server' in proc.info['name'].lower():
                    logger.info(f"发现已运行的Redis进程: {proc.info['pid']}")
                    proc.terminate()
                    time.sleep(1)  # 等待进程终止
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
            
        # 启动Redis服务器
        logger.info(f"正在启动Redis服务器: {redis_exe}")
        logger.info(f"使用配置文件: {redis_conf}")
        
        # 使用subprocess.Popen启动Redis，并捕获输出
        redis_process = subprocess.Popen(
            [redis_exe, redis_conf],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
            text=True
        )
        
        # 等待Redis服务器启动
        max_wait = 10  # 最大等待时间（秒）
        wait_interval = 0.5  # 检查间隔（秒）
        total_wait = 0
        
        while total_wait < max_wait:
            # 检查进程是否还在运行
            if redis_process.poll() is not None:
                # 进程已结束，获取错误信息
                stdout, stderr = redis_process.communicate()
                logger.error(f"Redis服务器启动失败: {stderr}")
                return False
                
            try:
                # 尝试连接Redis
                test_client = redis.Redis(
                    host=REDIS_CONFIG['host'],
                    port=REDIS_CONFIG['port'],
                    password=REDIS_CONFIG['password'],
                    socket_timeout=1
                )
                test_client.ping()
                logger.info("Redis服务器启动成功并已就绪")
                return True
            except Exception as e:
                time.sleep(wait_interval)
                total_wait += wait_interval
                logger.info(f"等待Redis服务器就绪... ({total_wait:.1f}/{max_wait}秒)")
                
                # 检查Redis进程的输出
                if redis_process.stdout:
                    output = redis_process.stdout.readline()
                    if output:
                        logger.info(f"Redis输出: {output.strip()}")
        
        # 如果超时，终止进程
        redis_process.terminate()
        logger.error("Redis服务器启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动Redis服务器失败: {str(e)}")
        return False

# 启动Redis服务器
if not start_redis_server():
    logger.error("无法启动Redis服务器，程序退出")
    sys.exit(1)

# 初始化Redis管理器
redis_manager = RedisManager()

# 等待Redis连接就绪
max_retries = 10
retry_count = 0
retry_interval = 2  # 重试间隔（秒）

while retry_count < max_retries:
    try:
        if redis_manager.is_connected():
            # 测试连接是否真正可用
            redis_manager.client.ping()
            logger.info("Redis连接成功")
            break
    except Exception as e:
        logger.error(f"Redis连接尝试 {retry_count + 1}/{max_retries} 失败: {str(e)}")
        retry_count += 1
        if retry_count < max_retries:
            logger.info(f"等待 {retry_interval} 秒后重试...")
            time.sleep(retry_interval)
            # 尝试重新连接
            redis_manager.reconnect()

if not redis_manager.is_connected():
    logger.error("无法连接到Redis服务，请确保Redis服务已启动")
    sys.exit(1)

# 用于存储正在运行的进程
running_processes = {}

# 清空所有task_status数据
if redis_manager.is_connected():
    try:
        task_status_keys = redis_manager.client.keys('task_status:*')
        if task_status_keys:
            redis_manager.client.delete(*task_status_keys)
            logger.info(f"已清空 {len(task_status_keys)} 个task_status记录")
    except Exception as e:
        logger.error(f"清空task_status数据时出错: {str(e)}")

# 从config.xml读取配置
config_path = os.path.join(current_dir, 'config.xml')
tree = ET.parse(config_path)
root = tree.getroot()

# 获取配置信息
config = {
    'server': {
        'host': root.find('Server/Host').text,
        'port': int(root.find('Server/Port').text),
        'debug': root.find('Server/Debug').text.lower() == 'true'
    },
    'frontend': {
        'ip': root.find('Frontend/IP').text,
        'port': int(root.find('Frontend/Port').text) if root.find('Frontend/Port').text else None
    },
    'dates': root.find('dates').text,
    'fme_home': root.find('FmeHome').text,
    'fme_dir': root.find('FmeDir').text,
    'limit': int(root.find('limits').text),
    'fme_visibility': root.find('fme_visibility').text.lower() == 'true'
}

# 获取数据库名称
DB_NAME = config['dates']
DB_PATH = os.path.join(current_dir, DB_NAME)

print(f"使用数据库: {DB_PATH}")
print(f"最大并发任务数: {config['limit']}")

# 设置前端和FME配置
FME_PATH = config['fme_home']
FRONTEND_IP = FRONTEND_CONFIG['ip']
FRONTEND_PORT = str(FRONTEND_CONFIG['port']) if FRONTEND_CONFIG.get('port') is not None else None

print(f"前端地址: {FRONTEND_IP}:{FRONTEND_PORT if FRONTEND_PORT else 'N/A'}")
print(f"FME路径: {FME_PATH}")

# 添加配置接口
@app.route('/api/config', methods=['GET'])
def get_config():
    try:
        return jsonify({
            'success': True,
            'data': {
                'server': f"http://{config['server']['host']}:{config['server']['port']}",
                'frontend': f"http://{config['frontend']['ip']}:{config['frontend']['port']}",
                'fmeHome': config['fme_home'],
                'fmeDir': config['fme_dir']
            }
        })
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

def init_quality_item_config_rules(cursor):
    """初始化质检项配置规则"""
    # 首先确保表存在
    check_and_create_table(cursor, 'quality_item_config_rules', '''
    CREATE TABLE quality_item_config_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id INTEGER NOT NULL,
        config_type TEXT NOT NULL,
        layer_filter TEXT,
        parameter_config TEXT,
        ui_component TEXT NOT NULL,
        validation_rules TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (item_id) REFERENCES quality_item_dict(id)
    )
    ''')

    # 检查是否已有配置规则数据
    cursor.execute('SELECT COUNT(*) FROM quality_item_config_rules')
    existing_count = cursor.fetchone()[0]

    if existing_count > 0:
        logger.info(f"质检项配置规则已存在 {existing_count} 个，跳过初始化")
        return

    # 配置规则数据 - 精确定义每个质检项的配置需求
    # 先清空现有的配置规则
    cursor.execute('DELETE FROM quality_item_config_rules')

    # 获取所有质检项，按item_code排序以便精确匹配
    cursor.execute('SELECT id, item_code, item_name, param_type, param_name, object_type FROM quality_item_dict ORDER BY item_code')
    quality_items = cursor.fetchall()

    config_rules = []

    for item in quality_items:
        item_id, item_code, item_name, param_type, param_name, object_type = item

        # 根据参数类型和质检项类型定义配置规则
        if param_type == '文件上传':  # 数据库检查 - 模板对比
            config_rules.append((
                item_id, 'template_upload', 'all',
                '{"accept": ".gdb,.zip", "label": "标准模板GDB文件", "description": "上传标准模板文件进行对比检查"}',
                'TemplateUpload', '{"required": true}'
            ))

        elif param_type == '图层选择' and item_name == '工作范围检查':  # 工作范围检查
            # 工作范围检查：选择范围图层参数，前端处理待检图层选择
            config_rules.append((
                item_id, 'range_layer_select', 'polygon',
                '{"layer_type": "polygon", "label": "范围图层", "description": "选择面图层作为范围边界"}',
                'LayerSelector', '{"required": true}'
            ))

        elif param_type == '单选' and '坐标系' in param_name:  # 坐标系检查
            # 坐标系检查：选择目标坐标系参数 + 选择适用图层
            config_rules.append((
                item_id, 'coordinate_system_select', 'all',
                '{"source": "coordinate_systems", "label": "目标坐标系", "description": "选择要检查的目标坐标系"}',
                'CoordinateSystemSelector', '{"required": true}'
            ))

        elif param_type == '数值':  # 数值参数
            if '线长' in param_name:
                config_rules.append((
                    item_id, 'number_input', 'line' if object_type == '线要素' else 'polygon',
                    '{"type": "number", "unit": "m", "min": 0.001, "max": 100, "step": 0.001, "default": 0.1, "label": "最小允许线长", "description": "小于此长度的线段将被标记"}',
                    'NumberInput', '{"required": true}'
                ))
            elif '夹角' in param_name:
                config_rules.append((
                    item_id, 'number_input', 'line' if object_type == '线要素' else 'polygon',
                    '{"type": "number", "unit": "°", "min": 0.1, "max": 180, "step": 0.1, "default": 10, "label": "最小允许夹角", "description": "小于此角度的夹角将被标记为尖锐角"}',
                    'NumberInput', '{"required": true}'
                ))
            elif '面积' in param_name:
                config_rules.append((
                    item_id, 'number_input', 'polygon',
                    '{"type": "number", "unit": "m²", "min": 0.001, "max": 10000, "step": 0.001, "default": 1, "label": "最小允许面积", "description": "小于此面积的面将被标记为微小面"}',
                    'NumberInput', '{"required": true}'
                ))
            else:
                config_rules.append((
                    item_id, 'number_input', 'all',
                    '{"type": "number", "min": 0, "label": "数值参数", "description": "请输入数值参数"}',
                    'NumberInput', '{"required": true}'
                ))

        elif param_type == '范围':  # 范围参数
            config_rules.append((
                item_id, 'range_input', 'polygon',
                '{"type": "range", "unit": "个", "min": 3, "max": 10000, "default_min": 3, "default_max": 1000, "label": "节点数量范围", "description": "面要素的节点数量应在此范围内"}',
                'RangeInput', '{"required": true}'
            ))

        elif param_type == '双图层选择' and object_type == '交互检查':  # 交互检查
            # 双图层选择：前端直接处理，不需要特殊配置
            config_rules.append((
                item_id, 'dual_layer_select', 'all',
                f'{{"description": "{item_name}", "param_name": "{param_name}"}}',
                'LayerSelector', '{"required": true}'
            ))

        elif param_type == '无':  # 自检查类型（无参数，只需要图层选择）
            # 根据对象类型确定图层过滤器
            layer_filter = 'all'
            if object_type == '点要素':
                layer_filter = 'point'
            elif object_type == '线要素':
                layer_filter = 'line'
            elif object_type == '面要素':
                layer_filter = 'polygon'
            elif object_type == '通用':
                layer_filter = 'all'

            config_rules.append((
                item_id, 'layer_select', layer_filter,
                f'{{"description": "{item_name}"}}',
                'LayerSelector', '{"required": true}'
            ))

        else:  # 其他未知类型，默认为图层选择
            layer_filter = 'all'
            if object_type == '点要素':
                layer_filter = 'point'
            elif object_type == '线要素':
                layer_filter = 'line'
            elif object_type == '面要素':
                layer_filter = 'polygon'

            config_rules.append((
                item_id, 'layer_select', layer_filter,
                f'{{"description": "{item_name}", "param_type": "{param_type}", "param_name": "{param_name}"}}',
                'LayerSelector', '{"required": true}'
            ))

    # 插入配置规则数据
    for rule in config_rules:
        try:
            cursor.execute('''
                INSERT INTO quality_item_config_rules
                (item_id, config_type, layer_filter, parameter_config, ui_component, validation_rules)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', rule)
        except Exception as e:
            logger.warning(f"插入配置规则失败 (item_id={rule[0]}): {str(e)}")

    logger.info(f"初始化质检项配置规则，插入 {len(config_rules)} 个规则")

def check_and_create_table(cursor, table_name, create_sql):
    """检查表是否存在，如果不存在则创建"""
    try:
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        if not cursor.fetchone():
            logger.info(f"创建缺失的表: {table_name}")
            cursor.execute(create_sql)
            return True
        return False
    except Exception as e:
        logger.error(f"检查/创建表 {table_name} 失败: {str(e)}")
        return False

def check_and_add_column(cursor, table_name, column_name, column_definition):
    """检查表中是否存在指定列，如果不存在则添加"""
    try:
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [column[1] for column in cursor.fetchall()]

        if column_name not in columns:
            logger.info(f"添加缺失的列 {column_name} 到表 {table_name}")
            cursor.execute(f'ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}')
            return True
        return False
    except Exception as e:
        logger.error(f"检查/添加列 {column_name} 到表 {table_name} 失败: {str(e)}")
        return False

# 这个函数已经被整合到 init_db 中，不再需要

def init_db():
    """初始化数据库，无论数据库是否存在都会执行，只创建不存在的表"""
    logger.info(f"开始初始化数据库: {DB_PATH}")

    # 无论数据库是否存在都连接
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 创建用户表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS login_users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        real_name TEXT UNIQUE NOT NULL,    
        role TEXT NOT NULL,
        department TEXT UNIQUE NOT NULL, 
        valid TEXT DEFAULT '',    
        created_at TIMESTAMP
    )
    ''')
    
    # 创建部门表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS department (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        department TEXT UNIQUE NOT NULL,  
        created_at TIMESTAMP
    )
    ''')
    # 创建cad2gis运行记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_run_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id TEXT NOT NULL,
        fmw_id TEXT NOT NULL,
        fmw_name TEXT NOT NULL,
        username TEXT NOT NULL,
        submit_time TIMESTAMP,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        status TEXT DEFAULT 'running',
        time_consuming INTEGER,
        file_name TEXT,
        file_size TEXT,
        error_message TEXT,
        FOREIGN KEY (username) REFERENCES login_users(username)
    )
    ''')
    
    # 创建模型市场表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS model_market (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fmw_id TEXT UNIQUE NOT NULL,
        fmw_name TEXT NOT NULL,
        project TEXT,
        tools_class TEXT,
        data TEXT,
        fmw_path TEXT,
        run_times INTEGER DEFAULT 0,
        created_at TIMESTAMP,
        author TEXT,
        def_params TEXT,
        description TEXT,
        FOREIGN KEY (author) REFERENCES login_users(username)
    )
    ''')

    # 创建工具申请表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS tool_application (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fmw_id TEXT NOT NULL,
        fmw_name TEXT NOT NULL,
        applicant TEXT NOT NULL,
        usage_count INTEGER NOT NULL,
        count INTEGER,
        end_date TIMESTAMP NOT NULL,
        reviewer TEXT,
        status TEXT DEFAULT 'pending',
        reason TEXT,
        user_project TEXT,
        review_time TIMESTAMP,
        review_comment TEXT,
        created_at TIMESTAMP,
        FOREIGN KEY (fmw_id) REFERENCES model_market(fmw_id),
        FOREIGN KEY (applicant) REFERENCES login_users(username),
        FOREIGN KEY (reviewer) REFERENCES login_users(username)
    )
    ''')
    
    # 创建用户工具表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_tools (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        tool_id INTEGER NOT NULL,
        source TEXT NOT NULL,
        created_at TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES login_users(username),
        FOREIGN KEY (tool_id) REFERENCES model_market(id)
    )
    ''')
    
    # 创建任务记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS task_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tool_id INTEGER NOT NULL,
        tool_name TEXT NOT NULL,
        project TEXT NOT NULL,
        submitter TEXT NOT NULL,
        submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        task_name TEXT NOT NULL,
        task_id TEXT NOT NULL,
        fme_args TEXT NOT NULL,
        status TEXT DEFAULT 'running',
        time_consuming INTEGER,
        file_size TEXT,
        file_name TEXT,
        up_nums TEXT,
        error_message TEXT,
        FOREIGN KEY (tool_id) REFERENCES model_market(id),
        FOREIGN KEY (submitter) REFERENCES login_users(username)
    )
    ''')
    
    # 创建任务记录表总表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS task_records_all (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tool_id INTEGER NOT NULL,
        tool_name TEXT NOT NULL,
        project TEXT NOT NULL,
        submitter TEXT NOT NULL,
        submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        task_name TEXT NOT NULL,
        task_id TEXT NOT NULL,
        fme_args TEXT NOT NULL,
        status TEXT DEFAULT 'running',
        time_consuming INTEGER,
        file_size TEXT,
        file_name TEXT,
        up_nums TEXT,
        error_message TEXT,
        FOREIGN KEY (tool_id) REFERENCES model_market(id),
        FOREIGN KEY (submitter) REFERENCES login_users(username)
    )
    ''')

    # 创建任务状态表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_task_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id TEXT UNIQUE NOT NULL,
        args TEXT,
        status TEXT DEFAULT 'running',
        progress INTEGER DEFAULT 0,
        result TEXT,
        error_message TEXT,
        created_at TIMESTAMP,
        updated_at TIMESTAMP
    )
    ''')

    # 创建CAD2GIS模型市场表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_model_market (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fmw_id TEXT UNIQUE NOT NULL,
        fmw_name TEXT NOT NULL,
        project TEXT,
        data TEXT,
        fmw_path TEXT,
        run_times INTEGER DEFAULT 0,
        created_at TIMESTAMP,
        author TEXT,
        description TEXT
    )
    ''')
    
    # 创建CAD2GIS工具申请表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_tool_application (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fmw_id TEXT NOT NULL,
        fmw_name TEXT NOT NULL,
        applicant TEXT NOT NULL,
        usage_count INTEGER NOT NULL,
        count INTEGER,
        end_date TIMESTAMP NOT NULL,
        reviewer TEXT,
        status TEXT DEFAULT 'pending',
        reason TEXT,
        user_project TEXT,
        review_time TIMESTAMP,
        review_comment TEXT,
        created_at TIMESTAMP,
        FOREIGN KEY (fmw_id) REFERENCES model_market(fmw_id),
        FOREIGN KEY (applicant) REFERENCES login_users(username),
        FOREIGN KEY (reviewer) REFERENCES login_users(username)
    )
    ''')
    
    # 创建CAD2GIS用户工具表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_user_tools (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        tool_id INTEGER NOT NULL,
        source TEXT NOT NULL,
        created_at TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES login_users(username),
        FOREIGN KEY (tool_id) REFERENCES model_market(id)
    )
    ''')
    
    # 创建CAD2GIS任务记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS cad2gis_task_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tool_id INTEGER NOT NULL,
        tool_name TEXT NOT NULL,
        project TEXT NOT NULL,
        submitter TEXT NOT NULL,
        submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        task_name TEXT NOT NULL,
        task_id TEXT NOT NULL,
        fme_args TEXT NOT NULL,
        status TEXT DEFAULT 'running',
        time_consuming INTEGER,
        file_size TEXT,
        file_name TEXT,
        error_message TEXT,
        FOREIGN KEY (tool_id) REFERENCES model_market(id),
        FOREIGN KEY (submitter) REFERENCES login_users(username)
    )
    ''')
    
    # 创建数据质检模型市场表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS quality_model_market (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        fmw_id TEXT UNIQUE NOT NULL,
        fmw_name TEXT NOT NULL,
        project TEXT,
        data TEXT,
        fmw_path TEXT,
        run_times INTEGER DEFAULT 0,
        created_at TIMESTAMP,
        author TEXT,
        description TEXT
    )
    ''')

    # 质检相关表的初始化 - 直接在这里执行
    logger.info("开始初始化质检相关表")

    try:
        # 检查质检项字典表
        check_and_create_table(cursor, 'quality_item_dict', '''
        CREATE TABLE quality_item_dict (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            level1_name TEXT NOT NULL,
            level2_name TEXT NOT NULL,
            item_name TEXT NOT NULL,
            object_type TEXT,
            item_code TEXT,
            rule_content TEXT,
            param_type TEXT,
            param_name TEXT,
            example_img TEXT
        )
        ''')

        # 检查质检配置表
        check_and_create_table(cursor, 'quality_config', '''
        CREATE TABLE quality_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            gdb_path TEXT,
            created_by TEXT,
            gdb_filename TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 检查质检配置表的列
        check_and_add_column(cursor, 'quality_config', 'created_by', 'TEXT')
        check_and_add_column(cursor, 'quality_config', 'gdb_filename', 'TEXT')
        check_and_add_column(cursor, 'quality_config', 'file_path', 'TEXT DEFAULT ""')
        check_and_add_column(cursor, 'quality_config', 'creator_id', 'INTEGER DEFAULT 1')
        check_and_add_column(cursor, 'quality_config', 'creator_name', 'TEXT')
        check_and_add_column(cursor, 'quality_config', 'global_tolerance', 'REAL DEFAULT 0.001')

        # 检查质检配置项表
        check_and_create_table(cursor, 'quality_config_items', '''
        CREATE TABLE quality_config_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_id INTEGER NOT NULL,
            item_id INTEGER NOT NULL,
            selected_layers TEXT,
            parameters TEXT,
            source_layers TEXT,
            target_layers TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES quality_config(id),
            FOREIGN KEY (item_id) REFERENCES quality_item_dict(id)
        )
        ''')

        # 检查GDB上传历史表
        check_and_create_table(cursor, 'gdb_upload_history', '''
        CREATE TABLE gdb_upload_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_filename TEXT NOT NULL,
            unique_filename TEXT NOT NULL,
            stored_path TEXT NOT NULL,
            file_size INTEGER,
            upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            uploaded_by TEXT,
            databases_count INTEGER DEFAULT 0,
            total_layers INTEGER DEFAULT 0,
            total_features INTEGER DEFAULT 0,
            status TEXT DEFAULT 'uploaded',
            notes TEXT
        )
        ''')

        # 检查GDB上传历史表的列
        check_and_add_column(cursor, 'gdb_upload_history', 'unique_filename', 'TEXT')
        check_and_add_column(cursor, 'gdb_upload_history', 'databases_info', 'TEXT')  # JSON格式存储数据库信息
        check_and_add_column(cursor, 'gdb_upload_history', 'layers_info', 'TEXT')     # JSON格式存储图层信息

        # 迁移绝对路径到相对路径
        try:
            cursor.execute('SELECT id, stored_path FROM gdb_upload_history')
            records = cursor.fetchall()

            base_path = os.path.dirname(os.path.abspath(__file__))
            updated_count = 0

            for record_id, stored_path in records:
                if stored_path and os.path.isabs(stored_path):
                    # 转换绝对路径为相对路径
                    try:
                        relative_path = os.path.relpath(stored_path, base_path)
                        relative_path = relative_path.replace('\\', '/')

                        cursor.execute(
                            'UPDATE gdb_upload_history SET stored_path = ? WHERE id = ?',
                            (relative_path, record_id)
                        )
                        updated_count += 1
                    except ValueError as e:
                        # 跨磁盘驱动器的情况，使用绝对路径
                        logger.warning(f"无法转换路径（跨磁盘驱动器），保持绝对路径 {stored_path}: {str(e)}")
                        relative_path = stored_path.replace('\\', '/')
                        cursor.execute(
                            'UPDATE gdb_upload_history SET stored_path = ? WHERE id = ?',
                            (relative_path, record_id)
                        )
                        updated_count += 1
                    except Exception as e:
                        logger.warning(f"无法转换路径 {stored_path}: {str(e)}")

            if updated_count > 0:
                conn.commit()
                logger.info(f"已将 {updated_count} 个GDB历史记录的路径转换为相对路径")

        except Exception as e:
            logger.warning(f"GDB历史记录路径迁移失败: {str(e)}")

        # 创建坐标系数据表
        check_and_create_table(cursor, 'coordinate_systems', '''
        CREATE TABLE coordinate_systems (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT NOT NULL,
            wkt_definition TEXT NOT NULL,
            epsg_code TEXT,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 检查质检项配置规则表
        check_and_create_table(cursor, 'quality_item_config_rules', '''
        CREATE TABLE quality_item_config_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id INTEGER NOT NULL,
            config_type TEXT NOT NULL,
            layer_filter TEXT,
            parameter_config TEXT,
            ui_component TEXT NOT NULL,
            validation_rules TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES quality_item_dict(id)
        )
        ''')

        # 初始化质检项字典数据
        cursor.execute('SELECT COUNT(*) FROM quality_item_dict')
        existing_count = cursor.fetchone()[0]

        if existing_count == 0:
            # 质检项字典数据
            sample_quality_items = [
                # 数据库检查 - 模板对比
                ('数据库检查', '数据库结构', '冗余数据集检查', '数据集', 'B010101', '检查是否存在模板中未定义的多余数据集', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据库结构', '缺少数据集检查', '数据集', 'B010102', '检查是否缺少模板中要求的数据集', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据库结构', '冗余图层检查', '图层', 'B010201', '检查是否存在模板中未定义的多余图层', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据库结构', '缺少图层检查', '图层', 'B010202', '检查是否缺少模板中要求的图层', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据库结构', '图层类型检查', '图层', 'B010203', '检查图层几何类型是否与模板一致', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据表结构', '冗余字段检查', '字段', 'B020101', '检查是否存在模板中未定义的多余字段', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据表结构', '缺少字段检查', '字段', 'B020102', '检查是否缺少模板中要求的字段', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据表结构', '字段类型检查', '字段', 'B020103', '检查字段数据类型是否与模板一致', '文件上传', '标准模板GDB', ''),
                ('数据库检查', '数据表结构', '字段长度检查', '字段', 'B020104', '检查字段长度是否与模板一致', '文件上传', '标准模板GDB', ''),

                # 空间检查 - 空间基础
                ('空间检查', '空间基础', '工作范围检查', '通用', 'C010101', '检查质检图层是否完全位于范围图层的空间范围内', '图层选择', '范围图层', ''),
                ('空间检查', '空间基础', '坐标系检查', '通用', 'C010102', '检查图层的坐标系统参数是否符合要求', '单选', '坐标系', ''),

                # 空间检查 - 空间几何
                ('空间检查', '空间几何', '碎线检查', '线要素', 'C020201', '检查线要素是否存在长度小于阈值的碎线段', '数值', '最小允许线长（m）', ''),
                ('空间检查', '空间几何', '超短边检查', '线要素', 'C020202', '检查线要素边界是否存在长度小于阈值的超短边', '数值', '最小允许线长（m）', ''),
                ('空间检查', '空间几何', '超短边检查', '面要素', 'C020301', '检查面要素边界是否存在长度小于阈值的超短边', '数值', '最小允许线长（m）', ''),
                ('空间检查', '空间几何', '尖锐角检查', '线要素', 'C020203', '检查线要素是否存在角度小于阈值的尖锐角', '数值', '最小允许夹角（°）', ''),
                ('空间检查', '空间几何', '尖锐角检查', '面要素', 'C020302', '检查面要素是否存在角度小于阈值的尖锐角', '数值', '最小允许夹角（°）', ''),
                ('空间检查', '空间几何', '微小面检查', '面要素', 'C020303', '检查面要素是否存在面积小于阈值的微小面', '数值', '最小允许面积（m²）', ''),
                ('空间检查', '空间几何', '节点数量检查', '面要素', 'C020304', '检查面要素的节点数量是否在合理范围内', '范围', '节点数区间（个）', ''),
                ('空间检查', '空间几何', '圆弧检查', '线要素', 'C020204', '检查线要素是否包含圆弧', '无', '', ''),
                ('空间检查', '空间几何', '圆弧检查', '面要素', 'C020305', '检查面要素边界是否包含圆弧', '无', '', ''),
                ('空间检查', '空间几何', 'Z值检查', '通用', 'C020501', '检查要素是否包含Z值（三维坐标）', '无', '', ''),

                # 空间检查 - 空间拓扑（自检查）
                ('空间检查', '空间拓扑', '不能重合', '点要素', 'C030101', '检查点要素是否与同图层中其他点重合', '无', '', ''),
                ('空间检查', '空间拓扑', '必须为单一部件', '点要素', 'C030102', '检查点要素是否为单一部件', '无', '', ''),
                ('空间检查', '空间拓扑', '不能重叠', '线要素', 'C030201', '检查线要素是否与同图层中其他线重叠', '无', '', ''),
                ('空间检查', '空间拓扑', '不能相交', '线要素', 'C030202', '检查线要素是否与同图层中其他线相交', '无', '', ''),
                ('空间检查', '空间拓扑', '不能有悬挂点', '线要素', 'C030203', '检查线要素是否存在悬挂的端点', '无', '', ''),
                ('空间检查', '空间拓扑', '不能有伪结点', '线要素', 'C030204', '检查线要素是否存在不必要的伪结点', '无', '', ''),
                ('空间检查', '空间拓扑', '不能自重叠', '线要素', 'C030205', '检查线要素是否与自身重叠', '无', '', ''),
                ('空间检查', '空间拓扑', '不能自相交', '线要素', 'C030206', '检查线要素是否与自身相交', '无', '', ''),
                ('空间检查', '空间拓扑', '必须为单一部件', '线要素', 'C030207', '检查线要素是否为单一部件', '无', '', ''),
                ('空间检查', '空间拓扑', '不能有空隙', '面要素', 'C030301', '检查面要素之间是否存在空隙', '无', '', ''),
                ('空间检查', '空间拓扑', '不能重叠', '面要素', 'C030302', '检查面要素是否与同图层中其他面重叠', '无', '', ''),
                ('空间检查', '空间拓扑', '不能自相交', '面要素', 'C030303', '检查面要素边界是否自相交', '无', '', ''),
                ('空间检查', '空间拓扑', '不能有伪结点', '面要素', 'C030304', '检查面要素是否存在不必要的伪结点', '无', '', ''),
                ('空间检查', '空间拓扑', '不能有孔洞', '面要素', 'C030305', '检查面要素是否包含内部孔洞', '无', '', ''),
                ('空间检查', '空间拓扑', '必须为单一部件', '面要素', 'C030306', '检查面要素是否为单一部件', '无', '', ''),

                # 空间检查 - 空间拓扑（交互检查）
                ('空间检查', '空间拓扑', '（点-点）必须重合', '交互检查', 'C030401', '检查点图层中的点是否与参考点图层中的点重合', '双图层选择', '点-点', ''),
                ('空间检查', '空间拓扑', '（点-线）必须被端点覆盖', '交互检查', 'C030402', '检查点图层中的点是否被参考线图层的端点覆盖', '双图层选择', '点-线', ''),
                ('空间检查', '空间拓扑', '（点-线）必须被线覆盖', '交互检查', 'C030403', '检查点图层中的点是否被参考线图层覆盖', '双图层选择', '点-线', ''),
                ('空间检查', '空间拓扑', '（点-面）必须在边界上', '交互检查', 'C030404', '检查点图层中的点是否位于参考面图层的边界上', '双图层选择', '点-面', ''),
                ('空间检查', '空间拓扑', '（点-面）必须在内部', '交互检查', 'C030405', '检查点图层中的点是否位于参考面图层的内部', '双图层选择', '点-面', ''),
                ('空间检查', '空间拓扑', '（线-点）端点必须被覆盖', '交互检查', 'C030501', '检查线图层的端点是否被参考点图层覆盖', '双图层选择', '线-点', ''),
                ('空间检查', '空间拓扑', '（线-线）必须被覆盖', '交互检查', 'C030502', '检查线图层是否被参考线图层覆盖', '双图层选择', '线-线', ''),
                ('空间检查', '空间拓扑', '（线-线）不能重叠', '交互检查', 'C030503', '检查线图层是否与参考线图层重叠', '双图层选择', '线-线', ''),
                ('空间检查', '空间拓扑', '（线-线）不能相交', '交互检查', 'C030504', '检查线图层是否与参考线图层相交', '双图层选择', '线-线', ''),
                ('空间检查', '空间拓扑', '（线-面）必须被边界覆盖', '交互检查', 'C030505', '检查线图层是否被参考面图层的边界覆盖', '双图层选择', '线-面', ''),
                ('空间检查', '空间拓扑', '（线-面）必须在内部', '交互检查', 'C030506', '检查线图层是否位于参考面图层的内部', '双图层选择', '线-面', ''),
                ('空间检查', '空间拓扑', '（面-点）必须包含点', '交互检查', 'C030601', '检查面图层是否包含参考点图层中的点', '双图层选择', '面-点', ''),
                ('空间检查', '空间拓扑', '（面-点）必须包含一个点', '交互检查', 'C030602', '检查面图层是否恰好包含参考点图层中的一个点', '双图层选择', '面-点', ''),
                ('空间检查', '空间拓扑', '（面-线）边界必须被覆盖', '交互检查', 'C030603', '检查面图层的边界是否被参考线图层覆盖', '双图层选择', '面-线', ''),
                ('空间检查', '空间拓扑', '（面-面）必须被覆盖', '交互检查', 'C030604', '检查面图层是否被参考面图层覆盖', '双图层选择', '面-面', ''),
                ('空间检查', '空间拓扑', '（面-面）必须相互覆盖', '交互检查', 'C030605', '检查面图层是否与参考面图层相互覆盖', '双图层选择', '面-面', ''),
                ('空间检查', '空间拓扑', '（面-面）不能重叠', '交互检查', 'C030606', '检查面图层是否与参考面图层重叠', '双图层选择', '面-面', ''),
                ('空间检查', '空间拓扑', '（面-面）边界必须被覆盖', '交互检查', 'C030607', '检查面图层的边界是否被参考面图层的边界覆盖', '双图层选择', '面-面', ''),


            ]

            # 插入质检项数据
            for item in sample_quality_items:
                cursor.execute('''
                    INSERT INTO quality_item_dict
                    (level1_name, level2_name, item_name, object_type, item_code, rule_content, param_type, param_name, example_img)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', item)
            logger.info(f"初始化质检项字典，插入 {len(sample_quality_items)} 个质检项")

            # 初始化质检项配置规则
            init_quality_item_config_rules(cursor)
        else:
            logger.info(f"质检项字典已存在 {existing_count} 个项目，跳过初始化")

        # 初始化坐标系数据
        cursor.execute('SELECT COUNT(*) FROM coordinate_systems')
        coord_count = cursor.fetchone()[0]

        if coord_count == 0:
            logger.info("开始初始化坐标系数据...")

            # 默认坐标系数据
            coordinate_systems = [
                ('CGCS2000 / 3-degree Gauss-Kruger zone 40', 'CGCS2000 3度分带投影坐标系 第40带',
                 'PROJCS["CGCS2000_3_Degree_GK_Zone_40",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",40500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",120.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG",4528]]',
                 'EPSG:4528'),

                ('CGCS2000 / 3-degree Gauss-Kruger zone 39', 'CGCS2000 3度分带投影坐标系 第39带',
                 'PROJCS["CGCS2000_3_Degree_GK_Zone_39",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",39500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",117.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG",4527]]',
                 'EPSG:4527'),

                ('CGCS2000 / 3-degree Gauss-Kruger zone 38', 'CGCS2000 3度分带投影坐标系 第38带',
                 'PROJCS["CGCS2000_3_Degree_GK_Zone_38",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",38500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",114.0],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG",4526]]',
                 'EPSG:4526'),

                ('China Geodetic Coordinate System 2000', 'CGCS2000地理坐标系',
                 'GEOGCS["China Geodetic Coordinate System 2000",DATUM["China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101,AUTHORITY["EPSG","1024"]],AUTHORITY["EPSG","1043"]],PRIMEM["Greenwich",0.0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4490"]]',
                 'EPSG:4490'),

                ('WGS 84', 'WGS84地理坐标系',
                 'GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]]',
                 'EPSG:4326'),

                ('WGS 84 / UTM zone 49N', 'WGS84 UTM 49N投影坐标系',
                 'PROJCS["WGS 84 / UTM zone 49N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",111],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","32649"]]',
                 'EPSG:32649'),

                ('WGS 84 / UTM zone 50N', 'WGS84 UTM 50N投影坐标系',
                 'PROJCS["WGS 84 / UTM zone 50N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",117],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","32650"]]',
                 'EPSG:32650'),

                ('Beijing 1954 / 3-degree Gauss-Kruger zone 40', '北京54坐标系 3度分带投影 第40带',
                 'PROJCS["Beijing 1954 / 3-degree Gauss-Kruger zone 40",GEOGCS["Beijing 1954",DATUM["Beijing_1954",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],AUTHORITY["EPSG","6214"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4214"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",120],PARAMETER["scale_factor",1],PARAMETER["false_easting",40500000],PARAMETER["false_northing",0],UNIT["metre",1,AUTHORITY["EPSG","9001"]],AUTHORITY["EPSG","2416"]]',
                 'EPSG:2416')
            ]

            for coord_sys in coordinate_systems:
                cursor.execute('''
                INSERT INTO coordinate_systems (name, description, wkt_definition, epsg_code)
                VALUES (?, ?, ?, ?)
                ''', coord_sys)

            logger.info(f"坐标系数据初始化完成，共插入 {len(coordinate_systems)} 个坐标系")
        else:
            logger.info(f"坐标系数据已存在 {coord_count} 个项目，跳过初始化")

        logger.info("质检相关表初始化完成")

    except Exception as e:
        logger.error(f"质检相关表初始化失败: {str(e)}")
        raise e

    # 创建需求提交表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS requirement_submissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tool_name TEXT NOT NULL,
        project TEXT NOT NULL,
        approvals TEXT NOT NULL,
        delivery_date TIMESTAMP NOT NULL,
        run_mode TEXT NOT NULL,
        test_data TEXT,
        description TEXT,
        contact TEXT NOT NULL,
        phone TEXT NOT NULL,
        submitter TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        review_time TIMESTAMP,
        review_comment TEXT,
        submit_time TIMESTAMP NOT NULL,
        FOREIGN KEY (submitter) REFERENCES login_users(username)
    )
    ''')

    # 创建需求文件表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS requirement_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        requirement_id INTEGER NOT NULL,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (requirement_id) REFERENCES requirement_submissions(id)
    )
    ''')

    # 创建导航栏设置表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS navigation_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_by TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (updated_by) REFERENCES login_users(username)
    )
    ''')

    # 初始化默认导航栏设置
    default_nav_settings = {
        'home': 'true',
        'tools': 'true',
        'market': 'true',
        'quality': 'true',
        'coordinate': 'true',
        'cad2gis': 'true',
        'requirement': 'true',
        'layerPreview': 'true',
        'userManagement': 'true',
        'settings': 'true',
        'about': 'true'
    }
    
    for key, value in default_nav_settings.items():
        cursor.execute('''
            INSERT OR IGNORE INTO navigation_settings (setting_key, setting_value)
            VALUES (?, ?)
        ''', (key, value))

    # 提交所有更改并关闭连接
    conn.commit()
    conn.close()
    logger.info("数据库初始化完成")

# 数据库初始化将在主程序启动时进行

def get_db_connection():
    """获取数据库连接"""
    try:
        #logger.info(f"尝试连接数据库: {DB_PATH}")
        if not os.path.exists(DB_PATH):
            #logger.error(f"数据库文件不存在: {DB_PATH}")
            raise FileNotFoundError(f"数据库文件不存在: {DB_PATH}")
            
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        #logger.info("数据库连接成功")
        return conn
    except Exception as e:
        #logger.error(f"数据库连接失败: {str(e)}")
        raise

@app.route('/api/captcha/generate', methods=['POST'])
def generate_captcha_api():
    """生成验证码API"""
    try:
        data = request.get_json() or {}
        username = data.get('username', '')
        
        # 生成验证码
        captcha_text, captcha_image = generate_captcha()
        
        # 如果没有提供用户名，使用临时标识符
        if not username:
            username = f'temp_{int(time.time())}_{random.randint(1000, 9999)}'
        
        # 存储验证码（临时存储，5分钟后过期）
        captcha_store[username] = {
            'text': captcha_text,
            'timestamp': time.time()
        }
        
        logger.info(f"为用户 {username} 生成验证码: {captcha_text}")
        
        return jsonify({
            'success': True,
            'captcha_image': f'data:image/png;base64,{captcha_image}',
            'message': '验证码生成成功',
            'temp_username': username if not data.get('username') else None
        })
    except Exception as e:
        logger.error(f"生成验证码失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'生成验证码失败: {str(e)}'
        }), 500


@app.route('/api/license/info', methods=['GET'])
def get_license_info():
    lic = load_license()
    if not lic:
        return jsonify({'success': False, 'message': '未找到 license 文件'}), 200
    valid_from = lic.get('valid_from')
    valid_to = lic.get('valid_to')
    ok, msg = is_time_valid(valid_from, valid_to)
    now = datetime.now()
    days_left = None
    if valid_to:
        days_left = (datetime.strptime(valid_to, '%Y-%m-%d') - now).days
    return jsonify({
        'success': True,
        'license': lic,
        'valid': ok,
        'message': msg,
        'days_left': days_left
    }), 200

@app.route('/api/license/upload', methods=['POST'])
def upload_license():
    file = request.files.get('file')
    if not file:
        return jsonify({'success': False, 'message': '未选择文件'}), 400
    save_path = os.path.join(os.path.dirname(__file__), 'license.lic')
    file.save(save_path)
    return jsonify({'success': True, 'message': 'license 文件上传成功'}), 200

@app.route('/api/captcha/check', methods=['POST'])
def check_captcha():
    """检查验证码API"""
    try:
        data = request.get_json()
        username = data.get('username', '')
        captcha_input = data.get('captcha', '').upper()
        
        # 检查验证码是否存在且未过期
        if username not in captcha_store:
            return jsonify({
                'success': False,
                'message': '验证码已过期，请重新获取'
            })
        
        captcha_data = captcha_store[username]
        current_time = time.time()
        
        # 验证码5分钟过期
        if current_time - captcha_data['timestamp'] > 300:
            del captcha_store[username]
            return jsonify({
                'success': False,
                'message': '验证码已过期，请重新获取'
            })
        
        # 检查验证码是否正确
        if captcha_input == captcha_data['text']:
            # 验证码正确，删除存储的验证码
            del captcha_store[username]
            return jsonify({
                'success': True,
                'message': '验证码正确'
            })
        else:
            return jsonify({
                'success': False,
                'message': '验证码错误'
            })
    except Exception as e:
        logger.error(f"检查验证码失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检查验证码失败: {str(e)}'
        }), 500

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    captcha = data.get('captcha', '').upper()  # 获取验证码，转换为大写
    
    print(f"登录尝试 - 用户名: {username}, 密码: {password}")
    
    if not username or not password:
        return jsonify({'success': False, 'message': '用户名和密码不能为空'})
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询用户
        cursor.execute('SELECT * FROM login_users WHERE username = ?', (username,))
        user = cursor.fetchone()
        
        print(f"数据库查询结果: {user}")
        
        if user:
            # 首先检查valid字段
            valid_field = user[6] or ''  # 获取valid字段，如果为None则设为空字符串
            
            # 如果valid字段为'0'，说明之前密码错误，需要验证码
            if valid_field == '0':
                if not captcha:
                    return jsonify({
                        'success': False, 
                        'message': '本次登录需要输入验证码',
                        'need_captcha': True
                    })
                
                # 验证验证码
                if username not in captcha_store:
                    return jsonify({
                        'success': False,
                        'message': '验证码已过期，请重新获取',
                        'need_captcha': True
                    })
                
                captcha_data = captcha_store[username]
                current_time = time.time()
                
                # 验证码5分钟过期
                if current_time - captcha_data['timestamp'] > 300:
                    del captcha_store[username]
                    return jsonify({
                        'success': False,
                        'message': '验证码已过期，请重新获取',
                        'need_captcha': True
                    })
                
                if captcha != captcha_data['text']:
                    return jsonify({
                        'success': False,
                        'message': '验证码错误',
                        'need_captcha': True
                    })
                
                # 验证码正确，继续验证密码
                if user[2] == password:
                    # 登录成功，重置valid字段为空
                    cursor.execute('UPDATE login_users SET valid = ? WHERE username = ?', ('', username))
                    conn.commit()
                    
                    return jsonify({
                        'success': True,
                        'message': '登录成功',
                        'token': f'token-{user[0]}',
                        'user': {
                            'id': user[0],
                            'username': user[1],
                            'password': user[2],  # 注意：实际生产环境不应该返回密码
                            'real_name': user[3],
                            'role': user[4],
                            'department': user[5]
                        }
                    })
                else:
                    # 验证码正确但密码错误，保持valid字段为'0'
                    return jsonify({
                        'success': False, 
                        'message': '密码错误',
                        'need_captcha': True
                    })
            
            else:
                # valid字段为空，正常验证密码
                if user[2] == password:  # 检查密码是否匹配
                    # 登录成功，确保valid字段为空
                    cursor.execute('UPDATE login_users SET valid = ? WHERE username = ?', ('', username))
                    conn.commit()
                    
                    return jsonify({
                        'success': True,
                        'message': '登录成功',
                        'token': f'token-{user[0]}',
                        'user': {
                            'id': user[0],
                            'username': user[1],
                            'password': user[2],  # 注意：实际生产环境不应该返回密码
                            'real_name': user[3],
                            'role': user[4],
                            'department': user[5]
                        }
                    })
                else:
                    print(f"密码不匹配 - 数据库密码: {user[2]}, 输入密码: {password}")
                    # 密码错误，设置valid字段为'0'
                    cursor.execute('UPDATE login_users SET valid = ? WHERE username = ?', ('0', username))
                    conn.commit()
                    
                    return jsonify({
                        'success': False, 
                        'message': '密码错误',
                        'need_captcha': True
                    })
        else:
            print(f"未找到用户: {username}")
            return jsonify({'success': False, 'message': '账号不存在'})
            
    except Exception as e:
        print(f"登录错误: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/login/check-captcha', methods=['POST'])
def check_login_captcha():
    """检查用户是否需要验证码"""
    try:
        data = request.get_json()
        username = data.get('username')
        
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'})
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询用户的valid字段
        cursor.execute('SELECT valid FROM login_users WHERE username = ?', (username,))
        result = cursor.fetchone()
        
        if result:
            need_captcha = result[0] == '0'
            return jsonify({
                'success': True,
                'need_captcha': need_captcha
            })
        else:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            })
            
    except Exception as e:
        print(f"检查验证码需求失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/tools/count', methods=['GET'])
def get_tools_count():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具总数
        cursor.execute('SELECT COUNT(*) FROM model_market')
        tools_count = cursor.fetchone()[0]
        
        # 获取本月新增工具数
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        cursor.execute('SELECT COUNT(*) FROM model_market WHERE created_at >= ?', (current_month_start,))
        new_tools_count = cursor.fetchone()[0]
        
        # 获取总运行次数
        cursor.execute('SELECT COUNT(*) FROM task_records_all')
        total_runs = cursor.fetchone()[0]
        
        # 清理过期任务
        cleanup_expired_tasks()
        
        # 获取当前队列数量
        current_queue_count = redis_manager.get_queue_length()
        logger.info(f"当前队列数量: {current_queue_count}")
        
        # 获取实际运行中的任务数量
        running_count = len(running_processes)
        logger.info(f"实际运行中的任务数量: {running_count}")
        
        # 获取等待中的任务数量（队列中的任务）
        pending_count = current_queue_count
        
        logger.info(f"统计结果 - 工具总数: {tools_count}, 队列数量: {current_queue_count}, 运行中: {running_count}, 等待中: {pending_count}")
        
        return jsonify({
            'success': True,
            'data': {
                'tools_count': tools_count,
                'new_tools_count': new_tools_count,
                'total_runs': total_runs,
                'current_queue_count': current_queue_count,
                'running_count': running_count,
                'pending_count': pending_count
            }
        })
    except Exception as e:
        logger.error(f"获取工具总数和任务状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取数据失败: {str(e)}'
        }), 500

@app.route('/api/server/stats', methods=['GET'])
def get_server_stats():
    try:
        # 获取CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 获取内存使用情况
        memory = psutil.virtual_memory()
        memory_used = round(memory.used / (1024**3), 2)  # 转换为GB
        memory_total = round(memory.total / (1024**3), 2)  # 转换为GB
        memory_percent = memory.percent
        
        # 获取磁盘使用情况 - 获取后端所在磁盘
        try:
            # 获取后端程序所在目录
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe，使用exe所在目录
                backend_dir = os.path.dirname(sys.executable)
            else:
                # 在开发环境下，使用当前文件所在目录
                backend_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 获取磁盘根目录（例如：E:\）
            drive = os.path.splitdrive(backend_dir)[0] + '\\'
            
            # 获取磁盘信息
            c_drive = win32file.GetDiskFreeSpaceEx(drive)
            total_bytes = c_drive[1]
            free_bytes = c_drive[0]
            used_bytes = total_bytes - free_bytes
            
            disk_used = round(used_bytes / (1024**3), 2)  # 转换为GB
            disk_total = round(total_bytes / (1024**3), 2)  # 转换为GB
            disk_percent = round((used_bytes / total_bytes) * 100, 2)
            
            # 获取磁盘类型（C、D、E等）
            drive_type = drive[0].upper()
        except Exception as e:
            logger.error(f"获取磁盘信息失败: {str(e)}")
            # 如果获取失败，使用默认值
            disk_used = 0
            disk_total = 0
            disk_percent = 0
            drive_type = 'C'
        
        return jsonify({
            'success': True,
            'data': {
                'cpu': {'percent': cpu_percent},
                'memory': {
                    'used': memory_used,
                    'total': memory_total,
                    'percent': memory_percent
                },
                'disk': {
                    'used': disk_used,
                    'total': disk_total,
                    'percent': disk_percent,
                    'type': drive_type,
                    'drive': drive
                }
            }
        })
    except Exception as e:
        logger.error(f"获取服务器状态失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/list', methods=['GET'])
def get_tools_list():
    try:
        logger.info("开始获取工具列表")
        
        # 获取查询参数
        username = request.args.get('username')  # 如果传入username，则只返回该用户的工具
        is_my_tools = request.args.get('my_tools', 'false').lower() == 'true'  # 是否为"我的工具"模式
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取总运行次数
        cursor.execute('SELECT COUNT(*) FROM task_records_all')
        total_runs = cursor.fetchone()[0]
        logger.info(f"总运行次数: {total_runs}")
        
        # 构建查询SQL
        if is_my_tools and username:
            # "我的工具"模式：只返回当前用户的工具
            logger.info(f"获取用户 {username} 的工具列表")
            cursor.execute('''
            SELECT 
                m.id,
                m.fmw_id,
                m.fmw_name,
                m.project,
                m.tools_class,
                m.data,
                m.fmw_path,
                COALESCE(m.run_times, 0) as run_times,
                m.created_at,
                m.author,
                COALESCE(u.real_name, m.author) as author_name,
                m.description
            FROM model_market m
            LEFT JOIN login_users u ON m.author = u.username
            WHERE m.author = ?
            ORDER BY m.created_at DESC
            ''', (username,))
        else:
            # 工具市场模式：返回所有工具
            logger.info("获取所有工具列表（工具市场）")
            cursor.execute('''
            SELECT 
                m.id,
                m.fmw_id,
                m.fmw_name,
                m.project,
                m.tools_class,
                m.data,
                m.fmw_path,
                COALESCE(m.run_times, 0) as run_times,
                m.created_at,
                m.author,
                COALESCE(u.real_name, m.author) as author_name,
                m.description
            FROM model_market m
            LEFT JOIN login_users u ON m.author = u.username
            ORDER BY m.created_at DESC
            ''')
        
        tools = cursor.fetchall()
        conn.close()
        
        # 格式化返回数据
        result = [{
            'id': tool[0],
            'fmw_id': tool[1],
            'fmw_name': tool[2],
            'project': tool[3],
            'tools_class': tool[4],
            'data': tool[5],
            'fmw_path': tool[6],
            'run_times': tool[7],
            'created_at': tool[8],
            'author': tool[9],
            'author_name': tool[10],
            'description': tool[11]
        } for tool in tools]
        
        logger.info(f"获取工具列表成功，共 {len(result)} 条数据")
        return jsonify({'success': True, 'data': result})
    except Exception as e:
        logger.error(f"获取工具列表失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/verify_run_permission', methods=['POST'])
def verify_run_permission():
    try:
        data = request.get_json()
        id = data.get('id')
        username = data.get('username')
        
        if not id or not username:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT usage_count, count, end_date
        FROM tool_application 
        WHERE id = ? AND applicant = ?
        ''', (id, username))
        
        record = cursor.fetchone()
        
        if not record:
            conn.close()
            return jsonify({'success': False, 'message': '未找到对应的申请记录'}), 404
            
        usage_count, current_count, end_date = record
        
        # 校验使用次数
        if current_count >= usage_count:
            conn.close()
            return jsonify({'success': False, 'message': '已达到申请的使用次数上限'}), 403
            
        # 校验截止日期（只到天）
        today = datetime.now().date()
        try:
            if len(end_date) == 10:
                end_date_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            else:
                end_date_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S').date()
        except Exception:
            conn.close()
            return jsonify({'success': False, 'message': '截止日期格式错误'}), 500

        if today > end_date_date:
            conn.close()
            return jsonify({'success': False, 'message': '该申请已过期'}), 403
            
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '验证通过',
            'data': {
                'usage_count': usage_count,
                'current_count': current_count,
                'remaining_count': usage_count - current_count,
                'end_date': end_date
            }
        })
    except Exception as e:
        logger.error(f"验证运行权限失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'验证运行权限失败: {str(e)}'
        }), 500

@app.route('/api/parse_fmw', methods=['GET', 'POST', 'OPTIONS'])
def parse_fmw():
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        # 根据请求方法获取参数
        if request.method == 'GET':
            model_id = request.args.get('model_id')
            debug = request.args.get('debug', 'false').lower() == 'true'
        else:  # POST
            data = request.get_json()
            model_id = data.get('fmw_id')  # 从请求体中获取fmw_id
            debug = data.get('debug', False)

        if not model_id:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        # 基本日志输出，不受debug控制
        logger.info(f"开始解析FMW文件，模型ID: {model_id}")

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT fmw_path, def_params FROM model_market WHERE fmw_id = ?', (model_id,))
        result = cursor.fetchone()
        conn.close()

        if not result:
            logger.error("未找到对应的工具")
            return jsonify({'success': False, 'message': '未找到对应的工具'}), 404

        # 使用全局的current_dir变量
        if debug:
            logger.info(f"后端目录: {current_dir}")
            logger.info(f"数据库中的相对路径: {result['fmw_path']}")
        
        # 构建完整的FMW文件路径
        fmw_path = os.path.normpath(os.path.join(current_dir, result['fmw_path']))
        if debug:
            logger.info(f"完整的FMW文件路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            logger.error(f"FMW文件不存在: {fmw_path}")
            return jsonify({'success': False, 'message': f'FMW文件不存在: {fmw_path}'}), 404

        try:
            # 使用parse_fmw.py中的函数解析参数
            params = parse_fmw_parameters(fmw_path, debug=debug)
            
            if not params:
                logger.error("解析参数失败：未返回参数")
                return jsonify({'success': False, 'message': '解析参数失败：未返回参数'}), 500
                
            # 基本日志输出，不受debug控制
            logger.info(f"成功解析到 {len(params.get('parameters', []))} 个参数")
            logger.info(f"解析到参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
            
            if debug:
                logger.info("==================\n")

            # 确保返回正确的结构
            return jsonify({
                'success': True,
                'data': {
                    'name': os.path.basename(fmw_path),
                    'path': fmw_path,
                    'parameters': params.get('parameters', []),  # 使用参数列表
                    'parameters_dict': params.get('parameters_dict', {}),  # 保持向后兼容
                    'def_params': result[1] if len(result) > 1 else 'N'  # 添加默认参数设置，使用索引访问
                }
            })
            
        except ValueError as ve:
            logger.error(f"解析参数时出错: {str(ve)}")
            return jsonify({'success': False, 'message': str(ve)}), 500

    except Exception as e:
        logger.error(f"解析FMW文件时出错: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件被上传'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    try:
        # 生成40位随机码
        chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
        random_code = ''.join(random.choice(chars) for _ in range(40))
        
        # 使用与app.py相同的相对路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe，使用exe所在目录
            base_dir = os.path.dirname(sys.executable)
        else:
            # 在开发环境下，使用当前文件所在目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
            
        # 创建temp目录
        temp_dir = os.path.join(base_dir, 'temp')
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
            print(f"创建临时目录: {temp_dir}")
        
        # 创建随机码目录
        random_dir = os.path.join(temp_dir, random_code)
        if not os.path.exists(random_dir):
            os.makedirs(random_dir)
            print(f"创建随机码目录: {random_dir}")
        
        # 保存文件到随机码目录
        file_path = os.path.join(random_dir, file.filename)
        file.save(file_path)
        
        # 检查文件是否为压缩文件
        if file.filename.lower().endswith(('.zip', '.rar', '.7z')):
            # 解压到同一随机码目录下
            extract_path = os.path.join(random_dir, os.path.splitext(file.filename)[0])
            if os.path.exists(extract_path):
                shutil.rmtree(extract_path)
            os.makedirs(extract_path)
            
            try:
                # 根据文件扩展名选择不同的解压方法
                if file.filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_path)
                elif file.filename.lower().endswith('.7z'):
                    # 使用7-Zip命令行工具解压7Z文件
                    seven_zip_path = os.path.join(base_dir, '7z', '7z.exe')
                    if not os.path.exists(seven_zip_path):
                        return jsonify({'success': False, 'message': '7-Zip工具未找到，请确保7z.exe位于backend/7z目录下'})
                    
                    cmd = [seven_zip_path, 'x', file_path, f'-o{extract_path}', '-y']
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    
                    if result.returncode != 0:
                        raise Exception(f"7-Zip解压失败: {result.stderr}")
                elif file.filename.lower().endswith('.rar'):
                    # 使用UnRAR命令行工具解压RAR文件
                    unrar_path = os.path.join(base_dir, '7z', 'UnRAR.exe')
                    if not os.path.exists(unrar_path):
                        return jsonify({'success': False, 'message': 'UnRAR工具未找到，请确保UnRAR.exe位于backend/7z目录下'})
                    
                    cmd = [unrar_path, 'x', '-y', file_path, extract_path]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    
                    if result.returncode != 0:
                        raise Exception(f"UnRAR解压失败: {result.stderr}")
                
                # 返回解压后的目录路径
                return jsonify({
                    'success': True,
                    'data': {
                        'path': extract_path
                    }
                })
            except Exception as e:
                error_msg = str(e)
                logger.error(f"解压文件失败: {error_msg}")
                return jsonify({'success': False, 'message': f'解压文件失败: {error_msg}'})
        
        # 如果不是压缩文件，直接返回文件路径
        return jsonify({
            'success': True,
            'data': {
                'path': file_path
            }
        })
        
    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({'success': False, 'message': f'文件上传处理失败: {str(e)}'})

@app.route('/api/tools/update-run-times', methods=['POST'])
def update_run_times():
    try:
        data = request.get_json()
        fmw_id = data.get('model_id')  # 使用model_id作为fmw_id
        username = request.headers.get('X-Username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新 model_market 表中的 run_times，使用fmw_id
        cursor.execute("""
            UPDATE model_market 
            SET run_times = COALESCE(run_times, 0) + 1 
            WHERE fmw_id = ?
        """, (fmw_id,))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '运行次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新运行次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新运行次数失败: {str(e)}'
        }), 500

@app.route('/api/task-records/add', methods=['POST'])
def add_task_record(data=None):
    try:
        # 如果没有传入数据，则从请求中获取
        if data is None:
            data = request.get_json()
            
        task_id = data.get('task_id')
        model_id = data.get('model_id')
        model_name = data.get('model_name')
        username = data.get('username')
        status = data.get('status')
        created_at = data.get('created_at')
        time_consuming = data.get('time_consuming')  # 获取运行时间
        file_size = data.get('file_size')  # 获取文件大小
        file_name = data.get('file_name')  # 获取文件名
        params = data.get('params', {})  # 获取参数
        up_nums = data.get('up_nums', 1)  # 获取上传文件数量，默认为1
        
        if not all([task_id, model_id, model_name, username, status, created_at]):
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        # 准备插入数据
        insert_data = (
            task_id,
            model_id,
            model_name,
            data.get('project', ''),
            username,
            created_at,
            model_name,  # 使用模型名称作为任务名称
            status,
            json.dumps(params) if isinstance(params, dict) else params,  # 确保参数是JSON字符串
            time_consuming,  # 添加运行时间
            file_size,  # 添加文件大小
            file_name,  # 添加文件名
            up_nums  # 添加上传文件数量
        )
        
        logger.info(f"\n=== 开始插入任务记录 ===")
        logger.info(f"任务ID: {task_id}")
        logger.info(f"模型ID: {model_id}")
        logger.info(f"模型名称: {model_name}")
        logger.info(f"用户名: {username}")
        logger.info(f"状态: {status}")
        logger.info(f"创建时间: {created_at}")
        logger.info(f"运行时间: {time_consuming}")
        logger.info(f"文件大小: {file_size}")
        logger.info(f"文件名: {file_name}")
        logger.info(f"上传文件数量: {up_nums}")
        logger.info(f"参数: {params}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入到task_records表
        cursor.execute("""
            INSERT INTO task_records 
            (task_id, tool_id, tool_name, project, submitter, submit_time, task_name, status, fme_args, time_consuming, file_size, file_name, up_nums)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, insert_data)
        
        # 插入到task_records_all表
        cursor.execute("""
            INSERT INTO task_records_all 
            (task_id, tool_id, tool_name, project, submitter, submit_time, task_name, status, fme_args, time_consuming, file_size, file_name, up_nums)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, insert_data)
        
        conn.commit()
        
        # 验证插入是否成功
        cursor.execute('SELECT * FROM task_records WHERE task_id = ?', (task_id,))
        inserted_record = cursor.fetchone()
        if inserted_record:
            logger.info("任务记录插入成功")
            logger.info(f"插入的记录: {dict(inserted_record)}")
        else:
            logger.error("任务记录插入失败")
        
        cursor.close()
        conn.close()
        
        logger.info("=== 任务记录插入完成 ===\n")
        
        return jsonify({
            'success': True,
            'message': '任务记录添加成功'
        })
    except Exception as e:
        logger.error(f"添加任务记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加任务记录失败: {str(e)}'
        }), 500

@app.route('/api/run_fme', methods=['POST'])
def run_fme():
    # 进程窗口显示/隐藏逻辑由config_cache['fme_visibility']控制，需重启后端服务后生效
    try:
        data = request.get_json()
        username = request.headers.get('X-Username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
            
        logger.info(f"\n=== 提交FME任务 ===")
        logger.info(f"接收到的完整数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 获取任务ID
        task_id = data.get('task_id')
        if not task_id:
            return jsonify({
                'success': False,
                'message': '未提供任务ID'
            }), 400
        
        # 获取FME路径和参数
        fmw_path = data.get('fmw_path')
        params = data.get('params', {})
        fmw_id = data.get('fmw_id')
        
        logger.info(f"接收到的FMW路径: {fmw_path}")
        logger.info(f"当前工作目录: {current_dir}")
        logger.info(f"接收到的参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        # 检查文件是否存在
        if not fmw_path:
            logger.error("FMW路径为空")
            return jsonify({'success': False, 'message': 'FMW路径为空'})
            
        # 转换为绝对路径
        if not os.path.isabs(fmw_path):
            # 如果是相对路径，转换为绝对路径
            # 处理路径中的斜杠
            relative_path = fmw_path.replace('\\', '/').lstrip('/')
            # 构建完整的FMW文件路径
            fmw_path = os.path.normpath(os.path.join(current_dir, relative_path))
            logger.info(f"转换后的绝对路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            logger.error(f"FMW文件不存在: {fmw_path}")
            return jsonify({'success': False, 'message': f'FMW文件不存在: {fmw_path}'})

        # 使用传入的task_id作为输出目录名
        output_dir = os.path.join(MODELS_DIR, fmw_id, 'output', task_id)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")

        # 解析FMW文件获取参数信息
        try:
            fmw_params = parse_fmw_parameters(fmw_path)
            logger.info(f"解析到的FMW参数: {json.dumps(fmw_params, ensure_ascii=False, indent=2)}")
        except Exception as e:
            logger.error(f"解析FMW参数失败: {str(e)}")
            return jsonify({'success': False, 'message': f'解析FMW参数失败: {str(e)}'})

        if not fmw_params:
            logger.error("解析FMW参数失败")
            return jsonify({'success': False, 'message': '解析FMW参数失败'})

        # 查找输出参数名（access_mode为write的参数）
        output_param_name = None
        parameters_dict = fmw_params.get('parameters_dict', {})
        if not isinstance(parameters_dict, dict):
            logger.error(f"参数字典格式错误: {type(parameters_dict)}")
            return jsonify({'success': False, 'message': '参数字典格式错误'})
            
        for param_name, param_info in parameters_dict.items():
            if isinstance(param_info, dict) and param_info.get('access_mode') == 'write':
                output_param_name = param_name
                break

        if not output_param_name:
            logger.error("未找到输出参数")
            return jsonify({'success': False, 'message': '未找到输出参数'})

        # 添加任务记录
        task_record_data = {
            'task_id': task_id,
            'model_id': fmw_id,
            'model_name': data.get('fmw_name'),
            'username': username,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'time_consuming': 0,
            'file_size': '0MB',
            'file_name': '',
            'project': data.get('project', ''),
            'params': json.dumps(params) if isinstance(params, dict) else params,
            'up_nums': data.get('up_nums', 1)  # 添加文件数量，默认为1
        }
        
        # 直接调用 add_task_record 函数添加任务记录
        try:
            result = add_task_record(task_record_data)
            if not result.get_json().get('success'):
                logger.error(f"添加任务记录失败: {result.get_json().get('message')}")
                return jsonify({
                    'success': False,
                    'message': f'添加任务记录失败: {result.get_json().get("message")}'
                }), 500
                
            logger.info(f"任务记录添加成功: {task_id}")
        except Exception as e:
            logger.error(f"添加任务记录时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'添加任务记录时发生错误: {str(e)}'
            }), 500
                
        # === 处理 save_path 路径，确保为绝对路径，且只出现一次 ===
        save_path = params.get('save_path')
        if save_path:
            # 如果是相对路径，根据环境选择基准路径
            if not os.path.isabs(save_path):
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe，使用exe所在目录
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # 在开发环境下，使用app.py所在目录
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                save_path = os.path.normpath(os.path.join(base_dir, save_path))
                params['save_path'] = save_path

        # 构建FME命令行参数
        cmd = [FME_PATH, fmw_path]
        # 处理所有参数
        for key, value in params.items():
            cmd.extend([f'--{key}', str(value)])
        # 添加输出目录参数，但对于特定FMW不重复添加
        if output_param_name and fmw_id not in ['cad2gis', 'coordinatetransformation', 'quality']:
            cmd.extend([f'--{output_param_name}', output_dir])
        logger.info(f"构建的FME命令: {' '.join(cmd)}")

        # 将任务添加到Redis队列
        task_data = {
            'task_id': task_id,
            'args': {
                'cmd': cmd,
                'fmw_id': fmw_id,
                'fmw_name': data.get('fmw_name'),
                'username': username,
                'output_dir': output_dir
            }
        }
        
        if redis_manager.add_task(task_id, task_data):
            logger.info(f"任务已添加到Redis队列: {task_id}")
            return jsonify({
                'success': True,
                'message': '任务已提交到队列',
                'task_id': task_id
            })
        else:
            logger.error("Redis未连接，无法添加任务到队列")
            return jsonify({
                'success': False,
                'message': 'Redis未连接，无法添加任务到队列'
            })
            
    except Exception as e:
        logger.error(f"提交任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交任务失败: {str(e)}'
        }), 500
    

@app.route('/api/cad2gis/run_fme', methods=['POST'])
def cad2gis_run_fme():
    # 进程窗口显示/隐藏逻辑由config_cache['fme_visibility']控制，需重启后端服务后生效
    try:
        data = request.get_json()
        username = request.headers.get('X-Username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
            
        logger.info(f"\n=== 提交FME任务 ===")
        logger.info(f"接收到的完整数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 获取任务ID
        task_id = data.get('task_id')
        if not task_id:
            return jsonify({
                'success': False,
                'message': '未提供任务ID'
            }), 400
        
        # 获取FME路径和参数
        fmw_path = data.get('fmw_path')
        params = data.get('params', {})
        fmw_id = data.get('fmw_id')
        
        logger.info(f"接收到的FMW路径: {fmw_path}")
        logger.info(f"当前工作目录: {current_dir}")
        logger.info(f"接收到的参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        # 检查文件是否存在
        if not fmw_path:
            logger.error("FMW路径为空")
            return jsonify({'success': False, 'message': 'FMW路径为空'})
            
        # 转换为绝对路径
        if not os.path.isabs(fmw_path):
            relative_path = fmw_path.replace('\\', '/').lstrip('/')
            fmw_path = os.path.normpath(os.path.join(current_dir, relative_path))
            logger.info(f"转换后的绝对路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            logger.error(f"FMW文件不存在: {fmw_path}")
            return jsonify({'success': False, 'message': f'FMW文件不存在: {fmw_path}'})

        # 使用传入的task_id作为输出目录名
        output_dir = os.path.join(MODELS_DIR, fmw_id, 'output', task_id)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")

        # 添加任务记录
        task_record_data = {
            'task_id': task_id,
            'model_id': fmw_id,
            'model_name': data.get('fmw_name'),
            'username': username,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'time_consuming': 0,
            'file_size': '0MB',
            'file_name': '',
            'project': data.get('project', ''),
            'params': json.dumps(params) if isinstance(params, dict) else params,
            'up_nums': data.get('up_nums', 1)
        }
        try:
            result = add_task_record(task_record_data)
            if not result.get_json().get('success'):
                logger.error(f"添加任务记录失败: {result.get_json().get('message')}")
                return jsonify({
                    'success': False,
                    'message': f'添加任务记录失败: {result.get_json().get("message")}'
                }), 500
                
            logger.info(f"任务记录添加成功: {task_id}")
        except Exception as e:
            logger.error(f"添加任务记录时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'添加任务记录时发生错误: {str(e)}'
            }), 500
                
        # === 处理 save_path 路径，确保为绝对路径，且只出现一次 ===
        save_path = params.get('save_path')
        if save_path:
            if not os.path.isabs(save_path):
                if getattr(sys, 'frozen', False):
                    base_dir = os.path.dirname(sys.executable)
                else:
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                save_path = os.path.normpath(os.path.join(base_dir, save_path))
                params['save_path'] = save_path

        # === 处理 dwg_path 路径，确保为绝对路径 ===
        dwg_path = params.get('dwg_path')
        if dwg_path:
            if not os.path.isabs(dwg_path):
                if getattr(sys, 'frozen', False):
                    base_dir = os.path.dirname(sys.executable)
                else:
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                dwg_path = os.path.normpath(os.path.join(base_dir, dwg_path))
                params['dwg_path'] = dwg_path
                logger.info(f"转换后的dwg_path绝对路径: {dwg_path}")

        # 构建FME命令行参数
        cmd = [FME_PATH, fmw_path]
        for key, value in params.items():
            cmd.extend([f'--{key}', str(value)])
        logger.info(f"构建的FME命令: {' '.join(cmd)}")

        # 将任务添加到Redis队列
        task_data = {
            'task_id': task_id,
            'args': {
                'cmd': cmd,
                'fmw_id': fmw_id,
                'fmw_name': data.get('fmw_name'),
                'username': username,
                'output_dir': output_dir
            }
        }
        
        if redis_manager.add_task(task_id, task_data):
            logger.info(f"任务已添加到Redis队列: {task_id}")
            return jsonify({
                'success': True,
                'message': '任务已提交到队列',
                'task_id': task_id
            })
        else:
            logger.error("Redis未连接，无法添加任务到队列")
            return jsonify({
                'success': False,
                'message': 'Redis未连接，无法添加任务到队列'
            })
            
    except Exception as e:
        logger.error(f"提交任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交任务失败: {str(e)}'
        }), 500

    
@app.route('/api/Coordinate/run_fme', methods=['POST'])
def coordinate_run_fme():
    try:
        data = request.get_json()
        username = request.headers.get('X-Username')
        
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
            
        logger.info(f"\n=== 提交FME任务 ===")
        logger.info(f"接收到的完整数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 获取任务ID
        task_id = data.get('task_id')
        if not task_id:
            return jsonify({
                'success': False,
                'message': '未提供任务ID'
            }), 400
        
        # 获取FME路径和参数
        fmw_path = data.get('fmw_path')
        params = data.get('params', {})
        fmw_id = data.get('fmw_id')
        
        logger.info(f"接收到的FMW路径: {fmw_path}")
        logger.info(f"当前工作目录: {current_dir}")
        logger.info(f"接收到的参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        
        # 检查文件是否存在
        if not fmw_path:
            logger.error("FMW路径为空")
            return jsonify({'success': False, 'message': 'FMW路径为空'})
            
        # 转换为绝对路径
        if not os.path.isabs(fmw_path):
            # 如果是相对路径，转换为绝对路径
            # 处理路径中的斜杠
            relative_path = fmw_path.replace('\\', '/').lstrip('/')
            # 构建完整的FMW文件路径
            fmw_path = os.path.normpath(os.path.join(current_dir, relative_path))
            logger.info(f"转换后的绝对路径: {fmw_path}")
        
        if not os.path.exists(fmw_path):
            logger.error(f"FMW文件不存在: {fmw_path}")
            return jsonify({'success': False, 'message': f'FMW文件不存在: {fmw_path}'})

        # 使用传入的task_id作为输出目录名
        output_dir = os.path.join(MODELS_DIR, fmw_id, 'output', task_id)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info(f"创建输出目录: {output_dir}")

        # 对于坐标转换模块，不需要解析FMW文件获取参数信息
        output_param_name = None
        if fmw_id not in ['coordinatetransformation', 'cad2gis', 'quality']:
            # 解析FMW文件获取参数信息
            try:
                fmw_params = parse_fmw_parameters(fmw_path)
                logger.info(f"解析到的FMW参数: {json.dumps(fmw_params, ensure_ascii=False, indent=2)}")
            except Exception as e:
                logger.error(f"解析FMW参数失败: {str(e)}")
                return jsonify({'success': False, 'message': f'解析FMW参数失败: {str(e)}'})

            if not fmw_params:
                logger.error("解析FMW参数失败")
                return jsonify({'success': False, 'message': '解析FMW参数失败'})

            # 查找输出参数名（access_mode为write的参数）
            parameters_dict = fmw_params.get('parameters_dict', {})
            if not isinstance(parameters_dict, dict):
                logger.error(f"参数字典格式错误: {type(parameters_dict)}")
                return jsonify({'success': False, 'message': '参数字典格式错误'})

            for param_name, param_info in parameters_dict.items():
                if isinstance(param_info, dict) and param_info.get('access_mode') == 'write':
                    output_param_name = param_name
                    break

            if not output_param_name:
                logger.error("未找到输出参数")
                return jsonify({'success': False, 'message': '未找到输出参数'})
        else:
            logger.info(f"跳过FMW参数解析，模块: {fmw_id}")
            # 对于特殊模块，参数由前端直接提供，不需要解析FMW

        # 添加任务记录
        task_record_data = {
            'task_id': task_id,
            'model_id': fmw_id,
            'model_name': data.get('fmw_name'),
            'username': username,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'time_consuming': 0,
            'file_size': '0MB',
            'file_name': '',
            'project': data.get('project', ''),
            'params': json.dumps(params) if isinstance(params, dict) else params,
            'up_nums': data.get('up_nums', 1)  # 添加文件数量，默认为1
        }
        
        # 直接调用 add_task_record 函数添加任务记录
        try:
            result = add_task_record(task_record_data)
            if not result.get_json().get('success'):
                logger.error(f"添加任务记录失败: {result.get_json().get('message')}")
                return jsonify({
                    'success': False,
                    'message': f'添加任务记录失败: {result.get_json().get("message")}'
                }), 500
                
            logger.info(f"任务记录添加成功: {task_id}")
        except Exception as e:
            logger.error(f"添加任务记录时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'添加任务记录时发生错误: {str(e)}'
            }), 500
                
        # === 处理 save_path 路径，确保为绝对路径，且只出现一次 ===
        save_path = params.get('save_path')
        if save_path:
            # 如果是相对路径，根据环境选择基准路径
            if not os.path.isabs(save_path):
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe，使用exe所在目录
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # 在开发环境下，使用app.py所在目录
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                save_path = os.path.normpath(os.path.join(base_dir, save_path))
                params['save_path'] = save_path

        # === 处理 dwg_path 路径，确保为绝对路径 ===
        dwg_path = params.get('dwg_path')
        if dwg_path:
            # 如果是相对路径，根据环境选择基准路径
            if not os.path.isabs(dwg_path):
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe，使用exe所在目录
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # 在开发环境下，使用app.py所在目录
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                dwg_path = os.path.normpath(os.path.join(base_dir, dwg_path))
                params['dwg_path'] = dwg_path
                logger.info(f"转换后的dwg_path绝对路径: {dwg_path}")

        # === 处理 input_date 路径，确保为绝对路径 ===
        input_date = params.get('input_date')
        if input_date:
            if not os.path.isabs(input_date):
                if getattr(sys, 'frozen', False):
                    # 生产环境（打包exe）
                    base_dir = os.path.dirname(sys.executable)
                else:
                    # 开发环境
                    base_dir = os.path.dirname(os.path.abspath(__file__))
                input_date_abs = os.path.normpath(os.path.join(base_dir, input_date))
                params['input_date'] = input_date_abs
                logger.info(f"补全后的 input_date 绝对路径: {input_date_abs}")

        # 构建FME命令行参数
        cmd = [FME_PATH, fmw_path]
        # 处理所有参数
        for key, value in params.items():
            cmd.extend([f'--{key}', str(value)])
        # 添加输出目录参数，但对于特定FMW不重复添加
        if output_param_name and fmw_id not in ['cad2gis', 'coordinatetransformation', 'quality']:
            cmd.extend([f'--{output_param_name}', output_dir])
        logger.info(f"构建的FME命令: {' '.join(cmd)}")

        # 将任务添加到Redis队列
        task_data = {
            'task_id': task_id,
            'args': {
                'cmd': cmd,
                'fmw_id': fmw_id,
                'fmw_name': data.get('fmw_name'),
                'username': username,
                'output_dir': output_dir
            }
        }
        
        if redis_manager.add_task(task_id, task_data):
            logger.info(f"任务已添加到Redis队列: {task_id}")
            return jsonify({
                'success': True,
                'message': '任务已提交到队列',
                'task_id': task_id
            })
        else:
            logger.error("Redis未连接，无法添加任务到队列")
            return jsonify({
                'success': False,
                'message': 'Redis未连接，无法添加任务到队列'
            })
            
    except Exception as e:
        logger.error(f"提交任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交任务失败: {str(e)}'
        }), 500
    

@app.route('/api/tools/upload', methods=['POST'])
def upload_tool():
    try:
        data = request.get_json()
        fmw_name = data.get('fmw_name')
        project = data.get('project')
        fmw_path = data.get('fmw_path')
        fmw_id = data.get('fmw_id')  # 使用前端生成的随机码
        data_time = data.get('data')  # 使用前端提供的日期时间
        description = data.get('description')  # 使用前端提供的工具描述
        def_params = data.get('def_params', 'N')  # 获取默认参数设置，默认为N
        tools_class = data.get('tools_class')  # 获取工具分类
        if not all([fmw_name, project, tools_class, fmw_path, fmw_id, data_time]):
            return jsonify({'success': False, 'message': '缺少必要参数'})
        
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'})
        
        # 使用与app.py相同的相对路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe，使用exe所在目录
            base_dir = os.path.dirname(sys.executable)
        else:
            # 在开发环境下，使用当前文件所在目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 创建models目录（如果不存在）
        models_dir = os.path.join(base_dir, 'models')
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
            print(f"创建models目录: {models_dir}")
        
        # 创建随机码文件夹
        random_dir = os.path.join(models_dir, fmw_id)
        if not os.path.exists(random_dir):
            os.makedirs(random_dir)
            print(f"创建随机码目录: {random_dir}")
        
        # 获取文件名
        file_name = os.path.basename(fmw_path)
        print(f"文件名: {file_name}")
        
        # 构建源文件路径（从temp目录）
        source_path = data.get('file_path')  # 直接使用上传接口返回的完整路径
        print(f"源文件路径: {source_path}")
        
        # 检查源文件是否存在
        if not os.path.exists(source_path):
            print(f"源文件不存在: {source_path}")
            return jsonify({'success': False, 'message': f'源文件不存在: {source_path}'})
        
        # 构建新的文件路径（使用相对路径）
        new_fmw_path = os.path.join('models', fmw_id, file_name)
        new_fmw_full_path = os.path.join(base_dir, new_fmw_path)
        print(f"新文件路径: {new_fmw_full_path}")
        
        # 移动文件
        try:
            shutil.move(source_path, new_fmw_full_path)
            print(f"文件移动成功: {source_path} -> {new_fmw_full_path}")
        except Exception as e:
            print(f"移动文件失败: {str(e)}")
            return jsonify({'success': False, 'message': f'移动文件失败: {str(e)}'})
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入新工具
        cursor.execute('''
        INSERT INTO model_market 
        (fmw_id, fmw_name, project, tools_class, data, fmw_path, created_at, author, description, def_params)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (fmw_id, fmw_name, project, tools_class, data_time, new_fmw_path, data_time, username, description, def_params))
        
        # 同时添加到用户工具表
        tool_id = cursor.lastrowid
        cursor.execute('''
        INSERT INTO user_tools 
        (user_id, tool_id, source, created_at)
        VALUES (?, ?, 'created', ?)
        ''', (username, tool_id, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': '工具上传成功'})
    except Exception as e:
        print(f"上传工具失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/delete', methods=['POST'])
def delete_tool():
    try:
        data = request.get_json()
        fmw_id = data.get('fmw_id')
        
        if not fmw_id:
            return jsonify({'success': False, 'message': '缺少必要参数'})
        
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'})
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在且属于当前用户
        cursor.execute('''
        SELECT fmw_path, author FROM model_market 
        WHERE fmw_id = ? AND author = ?
        ''', (fmw_id, username))
        tool = cursor.fetchone()
        
        if not tool:
            conn.close()
            return jsonify({'success': False, 'message': '工具不存在或无权删除'})
            
        # 检查是否有已批准的申请
        cursor.execute('''
        SELECT COUNT(*) as count FROM tool_application 
        WHERE fmw_id = ? AND status = 'approved'
        ''', (fmw_id,))
        approved_count = cursor.fetchone()['count']
        
        if approved_count > 0:
            conn.close()
            return jsonify({
                'success': False, 
                'message': '该工具有已批准的申请，无法删除'
            })
        
        # 获取后端根目录
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 构建完整的文件路径
        file_path = os.path.join(backend_dir, tool['fmw_path'].replace('/', '\\'))
        file_dir = os.path.dirname(file_path)
        
        # 删除整个随机码文件夹及其内容
        try:
            if os.path.exists(file_dir):
                shutil.rmtree(file_dir)  # 删除整个目录及其内容
                print(f"删除目录成功: {file_dir}")
        except Exception as e:
            print(f"删除目录失败: {str(e)}")
            conn.close()
            return jsonify({'success': False, 'message': f'删除目录失败: {str(e)}'})
        
        # 从数据库中删除记录
        cursor.execute('DELETE FROM model_market WHERE fmw_id = ?', (fmw_id,))
        cursor.execute('DELETE FROM user_tools WHERE tool_id IN (SELECT id FROM model_market WHERE fmw_id = ?)', (fmw_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': '工具删除成功'})
    except Exception as e:
        print(f"删除工具失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/update', methods=['POST', 'OPTIONS'])
def update_tool():
    if request.method == 'OPTIONS':
        return jsonify({'success': True})
        
    try:
        logger.debug("收到更新工具请求")
        data = request.get_json()
        logger.debug(f"请求数据: {data}")
        
        fmw_id = data.get('fmw_id')
        file_path = data.get('file_path')
        
        if not all([fmw_id, file_path]):
            logger.error("缺少必要参数")
            return jsonify({'success': False, 'message': '缺少必要参数'})
        
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            logger.error("未登录")
            return jsonify({'success': False, 'message': '未登录'})
        
        logger.info(f"开始更新工具，fmw_id: {fmw_id}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在且属于当前用户
        cursor.execute('''
        SELECT fmw_path, fmw_name FROM model_market 
        WHERE fmw_id = ? AND author = ?
        ''', (fmw_id, username))
        tool = cursor.fetchone()
        
        if not tool:
            conn.close()
            logger.error(f"工具不存在或无权更新，fmw_id: {fmw_id}")
            return jsonify({'success': False, 'message': '工具不存在或无权更新'})
        
        # 获取后端根目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe，使用exe所在目录
            backend_dir = os.path.dirname(sys.executable)
        else:
            # 在开发环境下，使用当前文件所在目录
            backend_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 获取新文件名
        new_file_name = os.path.basename(file_path)
        logger.info(f"新文件名: {new_file_name}")
        
        # 构建新的文件路径（直接使用models目录）
        new_fmw_path = f"models/{fmw_id}/{new_file_name}"
        new_fmw_full_path = os.path.join(backend_dir, new_fmw_path)
        logger.info(f"新文件完整路径: {new_fmw_full_path}")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(new_fmw_full_path), exist_ok=True)
        logger.info(f"确保目录存在: {os.path.dirname(new_fmw_full_path)}")
        
        # 获取旧文件路径
        old_file_path = os.path.join(backend_dir, tool['fmw_path'].replace('/', '\\'))
        logger.info(f"旧文件路径: {old_file_path}")
        
        # 如果新旧文件名不同，删除旧文件
        if os.path.basename(old_file_path) != new_file_name and os.path.exists(old_file_path):
            os.remove(old_file_path)
            logger.info(f"删除旧文件: {old_file_path}")
        
        # 移动新文件
        if os.path.exists(file_path):
            # 如果目标文件已存在，先删除
            if os.path.exists(new_fmw_full_path):
                os.remove(new_fmw_full_path)
                logger.info(f"删除已存在的目标文件: {new_fmw_full_path}")
            
            shutil.move(file_path, new_fmw_full_path)
            logger.info(f"成功移动新文件: {file_path} -> {new_fmw_full_path}")
        else:
            logger.error(f"新文件不存在: {file_path}")
            return jsonify({'success': False, 'message': '新文件不存在'})
        
        # 更新数据库记录
        cursor.execute('''
        UPDATE model_market 
        SET fmw_path = ?
        WHERE fmw_id = ? AND author = ?
        ''', (new_fmw_path, fmw_id, username))
        
        conn.commit()
        conn.close()
        
        logger.info(f"数据库更新完成，fmw_id: {fmw_id}, fmw_path: {new_fmw_path}")
        logger.info("工具更新成功")
        
        return jsonify({'success': True, 'message': '工具更新成功'})
    except Exception as e:
        logger.error(f"更新工具失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/approvers', methods=['GET'])
def get_approvers():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 从用户表中获取所有用户，排除当前用户，按real_name排序
        cursor.execute('''
            SELECT DISTINCT username, real_name 
            FROM login_users 
            WHERE username != ?
            ORDER BY real_name
        ''', (username,))
        
        approvers = [{'value': row['username'], 'label': row['real_name']} for row in cursor.fetchall()]
        
        conn.close()
        return jsonify({
            'success': True,
            'data': approvers
        })
    except Exception as e:
        logger.error(f"获取审批人员列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取审批人员列表失败: {str(e)}'
        }), 500

@app.route('/api/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        real_name = data.get('real_name')
        department = data.get('department')
        captcha = data.get('captcha', '').upper()  # 获取验证码，转换为大写
        
        if not all([username, password, real_name, department]):
            return jsonify({'success': False, 'message': '所有字段都是必填的'})
        
        # 验证验证码
        if not captcha:
            return jsonify({'success': False, 'message': '请输入验证码'})
        
        # 查找匹配的验证码（支持临时验证码）
        captcha_found = False
        captcha_data = None
        captcha_username = None
        
        # 先检查用户名对应的验证码
        if username in captcha_store:
            captcha_data = captcha_store[username]
            captcha_username = username
            captcha_found = True
        else:
            # 查找临时验证码（以temp_开头的）
            for temp_username, temp_data in captcha_store.items():
                if temp_username.startswith('temp_'):
                    if temp_data['text'] == captcha:
                        captcha_data = temp_data
                        captcha_username = temp_username
                        captcha_found = True
                        break
        
        if not captcha_found:
            return jsonify({'success': False, 'message': '验证码已过期，请重新获取'})
        
        current_time = time.time()
        
        # 验证码5分钟过期
        if current_time - captcha_data['timestamp'] > 300:
            if captcha_username in captcha_store:
                del captcha_store[captcha_username]
            return jsonify({'success': False, 'message': '验证码已过期，请重新获取'})
        
        if captcha != captcha_data['text']:
            return jsonify({'success': False, 'message': '验证码错误'})
            
        # 验证用户名格式（只允许字母和数字）
        if not username.isalnum():
            return jsonify({'success': False, 'message': '用户名只能包含字母和数字'})
            
        # 验证真实姓名（必须是中文）
        if not all('\u4e00' <= char <= '\u9fff' for char in real_name):
            return jsonify({'success': False, 'message': '真实姓名必须是中文'})
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查用户名是否已存在
        cursor.execute('SELECT username FROM login_users WHERE username = ?', (username,))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '用户名已存在'})
        
        # 插入新用户
        cursor.execute('''
            INSERT INTO login_users (username, password, real_name, department, role, created_at)
            VALUES (?, ?, ?, ?, 'user', ?)
        ''', (username, password, real_name, department, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
        
        # 注册成功后，插入三条tool_application记录
        try:
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 当前系统时间
            # 预设三条工具信息
            tools = [
                {'fmw_id': 'cad2gis', 'fmw_name': 'CAD转GIS'},
                {'fmw_id': 'coordinatetransformation', 'fmw_name': '坐标转换'},
                {'fmw_id': 'quality', 'fmw_name': '数据质检'}
            ]
            for tool in tools:
                # 中文注释：插入每条工具许可申请，所有字段均按需求赋值
                cursor.execute('''
                    INSERT INTO tool_application (
                        fmw_id, fmw_name, applicant, usage_count, count, end_date, reviewer, status, reason, user_project, review_time, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    tool['fmw_id'],
                    tool['fmw_name'],
                    username,
                    1,  # usage_count
                    0,  # count
                    '2099-12-31',
                    'auto',
                    'approved',
                    '试用许可',
                    '试用许可',
                    '2099-12-31 00:00:00',
                    now
                ))
        except Exception as e:
            # 中文注释：插入失败只记录日志，不影响注册流程
            logger.error(f"注册后插入tool_application失败: {str(e)}")
        
        # 注册成功后删除验证码
        if username in captcha_store:
            del captcha_store[username]
        # 清理所有临时验证码
        temp_keys = [key for key in captcha_store.keys() if key.startswith('temp_')]
        for temp_key in temp_keys:
            del captcha_store[temp_key]
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '注册成功'
        })
        
    except Exception as e:
        logger.error(f"注册失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/download', methods=['GET', 'POST'])
def download_tool():
    try:
        # 兼容 GET 和 POST 两种方式获取参数
        if request.method == 'POST':
            data = request.get_json()
            fmw_path = data.get('fmw_path')
        else:
            fmw_path = request.args.get('fmw_path')
        if not fmw_path:
            return jsonify({'success': False, 'message': '文件路径不能为空'}), 400
        # 构建完整的文件路径
        file_path = os.path.join(current_dir, fmw_path)
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': '文件不存在'}), 404
        file_size = os.path.getsize(file_path)
        filename = os.path.basename(file_path)
        encoded_filename = urllib.parse.quote(filename)
        range_header = request.headers.get('Range', None)
        # 支持断点续传
        def generate(file_path, start, end):
            with open(file_path, 'rb') as f:
                f.seek(start)
                remaining = end - start + 1
                chunk_size = 8192
                while remaining > 0:
                    read_size = min(chunk_size, remaining)
                    data = f.read(read_size)
                    if not data:
                        break
                    yield data
                    remaining -= len(data)
        if range_header:
            match = re.match(r'bytes=(\d+)-(\d+)?', range_header)
            if match:
                start = int(match.group(1))
                end = int(match.group(2)) if match.group(2) else file_size - 1
                if end >= file_size:
                    end = file_size - 1
                length = end - start + 1
                resp = Response(generate(file_path, start, end), status=206, mimetype='application/octet-stream')
                resp.headers.add('Content-Range', f'bytes {start}-{end}/{file_size}')
                resp.headers.add('Accept-Ranges', 'bytes')
                resp.headers.add('Content-Length', str(length))
                resp.headers.add('Content-Disposition', f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}')
                return resp
            else:
                return jsonify({'success': False, 'message': 'Range头格式错误'}), 416
        # 普通下载
        resp = make_response(send_file(file_path, as_attachment=True, download_name=filename))
        resp.headers['Content-Disposition'] = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        resp.headers['Accept-Ranges'] = 'bytes'
        return resp
    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'}), 500

@app.route('/api/tools/my-applications', methods=['GET'])
def get_my_applications():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 获取来源参数
        source = request.args.get('source', '')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 基础查询
        base_query = '''
        SELECT 
            ta.id,
            ta.fmw_id,
            ta.fmw_name,
            ta.applicant,
            u1.real_name as applicant_real_name,
            ta.usage_count,
            ta.count,
            ta.end_date,
            ta.reviewer,
            u2.real_name as reviewer_real_name,
            ta.reason,
            ta.user_project,
            CASE 
                WHEN ta.status = 'approved' THEN '已通过'
                WHEN ta.status = 'rejected' THEN '已驳回'
                ELSE '审批中'
            END as status,
            ta.review_time,
            ta.review_comment,
            ta.created_at as apply_date
        FROM tool_application ta
        LEFT JOIN login_users u1 ON ta.applicant = u1.username
        LEFT JOIN login_users u2 ON ta.reviewer = u2.username
        WHERE ta.applicant = ?
        '''
        
        # 新增：支持前端直接传递fmw_id参数，优先级高于source
        fmw_id = request.args.get('fmw_id', '').strip()
        params = [username]
        if fmw_id:
            base_query += " AND ta.fmw_id = ?"
            params.append(fmw_id)
        else:
            # 如果是来自ToolsView.vue的查询，添加过滤条件
            if source == 'tools':
                base_query += " AND ta.fmw_id NOT IN ('cad2gis', 'quality', 'coordinatetransformation')"
            # 根据不同页面source过滤不同fmw_id
            elif source == 'cad2gisView':
                base_query += " AND ta.fmw_id = 'cad2gis'"
            elif source == 'CoordinateView':
                base_query += " AND ta.fmw_id = 'coordinatetransformation'"
            elif source == 'QualityView':
                base_query += " AND ta.fmw_id = 'quality'"
        base_query += " ORDER BY ta.created_at DESC"
        cursor.execute(base_query, tuple(params))
        
        applications = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': [dict(app) for app in applications]
        })
    except Exception as e:
        logger.error(f"获取我的申请列表失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/my-approvals', methods=['GET'])
def get_my_approvals():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询我的审批列表，显示所有状态的申请
        cursor.execute('''
        SELECT 
            ta.id,
            ta.fmw_id,
            ta.fmw_name,
            ta.applicant,
            u1.real_name as applicant_real_name,
            ta.usage_count,
            ta.count,
            ta.end_date,
            ta.reviewer,
            u2.real_name as reviewer_real_name,
            ta.reason,
            ta.user_project,
            CASE 
                WHEN ta.status = 'approved' THEN '已通过'
                WHEN ta.status = 'rejected' THEN '已驳回'
                ELSE '审批中'
            END as status,
            ta.review_time,
            ta.review_comment,
            ta.created_at as apply_date
        FROM tool_application ta
        LEFT JOIN login_users u1 ON ta.applicant = u1.username
        LEFT JOIN login_users u2 ON ta.reviewer = u2.username
        WHERE ta.reviewer = ? AND ta.reviewer IS NOT NULL
        ORDER BY ta.created_at DESC
        ''', (username,))
        
        approvals = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': [dict(app) for app in approvals]
        })
    except Exception as e:
        logger.error(f"获取我的审批列表失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/cad2gis/tools/delete_result', methods=['POST'])
def delete_cad2gis_result():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({
                'success': False,
                'message': '任务ID不能为空'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取任务状态
        cursor.execute('''
            SELECT status FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        task = cursor.fetchone()
        
        if not task:
            conn.close()
            return jsonify({
                'success': False,
                'message': '任务不存在'
            })
            
        # 如果任务正在运行，需要终止FME进程
        if task['status'] == 'running':
            # 查找并终止对应的FME进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'fme' in proc.info['name'].lower():
                        # 检查命令行参数中是否包含task_id
                        cmdline = proc.info['cmdline']
                        if cmdline and any(task_id in arg for arg in cmdline):
                            logger.info(f"终止FME进程: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=5)  # 等待进程终止
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
            # 更新任务状态为已终止
            cursor.execute('''
                UPDATE task_records 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            cursor.execute('''
                UPDATE task_records_all 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            # 从Redis中删除任务
            if redis_manager.is_connected():
                redis_manager.delete_task(task_id)
        
        # 从task_records表中删除记录
        cursor.execute('''
            DELETE FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        
        # 从task_records_all表中删除记录
        cursor.execute('''
            DELETE FROM task_records_all 
            WHERE task_id = ?
        ''', (task_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        logger.error(f'删除CAD转GIS运行记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })



@app.route('/api/Coordinate/tools/delete_result', methods=['POST'])
def delete_coordinate_result():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({
                'success': False,
                'message': '任务ID不能为空'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取任务状态
        cursor.execute('''
            SELECT status FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        task = cursor.fetchone()
        
        if not task:
            conn.close()
            return jsonify({
                'success': False,
                'message': '任务不存在'
            })
            
        # 如果任务正在运行，需要终止FME进程
        if task['status'] == 'running':
            # 查找并终止对应的FME进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'fme' in proc.info['name'].lower():
                        # 检查命令行参数中是否包含task_id
                        cmdline = proc.info['cmdline']
                        if cmdline and any(task_id in arg for arg in cmdline):
                            logger.info(f"终止FME进程: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=5)  # 等待进程终止
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
            # 更新任务状态为已终止
            cursor.execute('''
                UPDATE task_records 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            cursor.execute('''
                UPDATE task_records_all 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            # 从Redis中删除任务
            if redis_manager.is_connected():
                redis_manager.delete_task(task_id)
        
        # 从task_records表中删除记录
        cursor.execute('''
            DELETE FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        logger.error(f'删除坐标转换运行记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })
    
@app.route('/api/tools/apply', methods=['POST'])
def apply_tool():
    try:

        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
            
        # 获取必要字段
        fmw_id = data.get('fmw_id')
        fmw_name = data.get('fmw_name')
        project = data.get('project')  # 从user_project获取
        applicant = data.get('applicant')
        reason = data.get('reason')
        end_date = data.get('end_date')
        usage_count = data.get('usage_count')
        user_project = data.get('user_project')
        
        # 记录系统时间和服务器时间
        system_time = datetime.now()
        server_time = datetime.now().isoformat()
        # 使用strftime格式化时间，避免时区转换问题
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"申请工具 - 系统时间: {system_time}")
        logger.info(f"申请工具 - 服务器时间: {server_time}")
        logger.info(f"申请工具 - 格式化时间: {created_at}")
        
        # 验证必要字段
        required_fields = ['fmw_id', 'fmw_name', 'user_project', 'applicant', 'reason', 'end_date', 'usage_count']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具上传者作为审批人
        cursor.execute('SELECT author FROM model_market WHERE fmw_id = ?', (fmw_id,))
        tool = cursor.fetchone()
        if not tool:
            conn.close()
            return jsonify({'success': False, 'message': '工具不存在'}), 404
        reviewer = tool['author']
        
        # 插入申请记录
        cursor.execute('''
            INSERT INTO tool_application (
                fmw_id, fmw_name, applicant, reason, end_date, usage_count, user_project, reviewer, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['applicant'],
            data['reason'],
            data['end_date'],
            data['usage_count'],
            data['user_project'],
            reviewer,
            'pending',
            created_at
        ))
        
        # 获取实际写入数据库的时间
        cursor.execute('SELECT created_at FROM tool_application WHERE id = last_insert_rowid()')
        db_time = cursor.fetchone()[0]
        logger.info(f"申请工具 - 实际写入数据库时间: {db_time}")
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '申请提交成功',
            'time_info': {
                'system_time': system_time.isoformat(),
                'server_time': server_time,
                'formatted_time': created_at,
                'db_time': db_time
            }
        })
    except Exception as e:
        logger.error(f"提交申请失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/approve', methods=['POST'])
def approve_tool():
    try:
        data = request.get_json()
        application_id = data.get('applicationId')
        
        if not application_id:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取申请记录
        cursor.execute('''
        SELECT created_at FROM tool_application 
        WHERE id = ? AND reviewer = ? AND status = 'pending'
        ''', (application_id, username))
        
        application = cursor.fetchone()
        if not application:
            conn.close()
            return jsonify({'success': False, 'message': '申请不存在或您没有权限审批'}), 403
            
        # 记录申请时间和审批时间
        apply_time = application[0]
        system_time = datetime.now()
        # 使用strftime格式化时间，避免时区转换问题
        review_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"审批工具 - 申请时间: {apply_time}")
        logger.info(f"审批工具 - 系统时间: {system_time}")
        logger.info(f"审批工具 - 格式化时间: {review_time}")
            
        # 更新申请状态为已通过，并初始化 count 字段为 0
        cursor.execute('''
        UPDATE tool_application 
        SET status = 'approved', review_time = ?, count = 0
        WHERE id = ?
        ''', (review_time, application_id))
        
        # 获取实际写入数据库的时间
        cursor.execute('SELECT review_time FROM tool_application WHERE id = ?', (application_id,))
        db_time = cursor.fetchone()[0]
        logger.info(f"审批工具 - 实际写入数据库时间: {db_time}")
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True, 
            'message': '审批通过成功',
            'time_info': {
                'apply_time': apply_time,
                'system_time': system_time.isoformat(),
                'formatted_time': review_time,
                'db_time': db_time
            }
        })
    except Exception as e:
        logger.error(f"审批通过失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

config_cache = {}

def load_config():
    global config_cache
    config_path = os.path.join(current_dir, 'config.xml')
    tree = ET.parse(config_path)
    root = tree.getroot()
    allow_register_elem = root.find('allow_register')
    config_cache['allow_register'] = allow_register_elem.text.lower() == 'true' if allow_register_elem is not None else False
    
    # 读取主题配置
    theme_elem = root.find('theme')
    config_cache['theme'] = theme_elem.text.strip() if theme_elem is not None else 'default'

    # 读取fme_visibility配置（新增）
    fme_visibility_elem = root.find('fme_visibility')
    # 默认显示窗口（false），true为隐藏
    config_cache['fme_visibility'] = fme_visibility_elem.text.lower() == 'true' if fme_visibility_elem is not None else False

# 启动时加载一次
load_config()

@app.route('/api/get_allow_register', methods=['POST'])
def get_allow_register():
    """
    读取config.xml中的allow_register参数，返回注册开关状态
    """
    try:
        allow_register = config_cache.get('allow_register', False)
        return jsonify({
            'success': True,
            'allow_register': allow_register
        })
    except Exception as e:
        logger.error(f"读取注册开关失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/get_default_theme', methods=['POST'])
def get_default_theme():
    """
    读取config.xml中的theme参数，返回默认主题
    """
    try:
        default_theme = config_cache.get('theme', 'default')
        return jsonify({
            'success': True,
            'default_theme': default_theme
        })
    except Exception as e:
        logger.error(f"读取默认主题失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/set_default_theme', methods=['POST'])
def set_default_theme():
    """
    设置config.xml中的theme参数，并写回文件
    """
    global config_cache
    try:
        data = request.get_json()
        theme = data.get('theme', 'default')
        
        # 验证主题名称是否有效
        valid_themes = ['default', 'blue', 'purple', 'green', 'orange', 'red', 'teal', 'pink', 'indigo', 'brown', 'gray', 'dark', 'light', 'cyan', 'lime', 'amber', 'deep-purple', 'light-blue', 'light-green', 'deep-orange', 'blue-grey', 'yellow', 'emerald', 'coral', 'lavender', 'mint', 'sunset', 'ocean', 'forest', 'rose', 'gold', 'silver']
        
        if theme not in valid_themes:
            return jsonify({'success': False, 'message': '无效的主题名称'}), 400
        
        config_path = os.path.join(current_dir, 'config.xml')
        tree = ET.parse(config_path)
        root = tree.getroot()
        theme_elem = root.find('theme')
        if theme_elem is None:
            theme_elem = ET.SubElement(root, 'theme')
        theme_elem.text = theme
        tree.write(config_path, encoding='utf-8', xml_declaration=True)
        
        # 更新缓存
        config_cache['theme'] = theme
        return jsonify({'success': True, 'message': '主题设置成功'})
    except Exception as e:
        logger.error(f"设置默认主题失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/set_allow_register', methods=['POST'])
def set_allow_register():
    """
    设置config.xml中的allow_register参数，并写回文件
    """
    global config_cache
    try:
        data = request.get_json()
        allow_register = data.get('allow_register', False)
        config_path = os.path.join(current_dir, 'config.xml')
        tree = ET.parse(config_path)
        root = tree.getroot()
        allow_register_elem = root.find('allow_register')
        if allow_register_elem is None:
            allow_register_elem = ET.SubElement(root, 'allow_register')
        allow_register_elem.text = 'true' if allow_register else 'false'
        tree.write(config_path, encoding='utf-8', xml_declaration=True)
        # 更新缓存
        config_cache['allow_register'] = allow_register
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"设置注册开关失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/get_fme_visibility', methods=['POST'])
def get_fme_visibility():
    """
    读取config.xml中的fme_visibility参数，返回是否隐藏FME进程窗口
    """
    try:
        fme_visibility = config_cache.get('fme_visibility', False)
        return jsonify({
            'success': True,
            'fme_visibility': fme_visibility
        })
    except Exception as e:
        logger.error(f"读取fme_visibility失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/set_fme_visibility', methods=['POST'])
def set_fme_visibility():
    """
    设置config.xml中的fme_visibility参数，并写回文件
    """
    global config_cache
    try:
        data = request.get_json()
        fme_visibility = data.get('fme_visibility', False)
        config_path = os.path.join(current_dir, 'config.xml')
        tree = ET.parse(config_path)
        root = tree.getroot()
        fme_visibility_elem = root.find('fme_visibility')
        if fme_visibility_elem is None:
            fme_visibility_elem = ET.SubElement(root, 'fme_visibility')
        fme_visibility_elem.text = 'true' if fme_visibility else 'false'
        tree.write(config_path, encoding='utf-8', xml_declaration=True)
        # 更新缓存
        config_cache['fme_visibility'] = fme_visibility
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"设置fme_visibility失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/reject', methods=['POST'])
def reject_tool():
    try:
        data = request.get_json()
        application_id = data.get('applicationId')
        reason = data.get('reason')
        
        if not all([application_id, reason]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查申请是否存在且当前用户是否为审批人
        cursor.execute('''
        SELECT id FROM tool_application 
        WHERE id = ? AND reviewer = ? AND status = 'pending'
        ''', (application_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '申请不存在或您没有权限审批'}), 403
            
        # 更新申请状态为已驳回，并记录驳回原因
        cursor.execute('''
        UPDATE tool_application 
        SET status = 'rejected', review_time = CURRENT_TIMESTAMP, review_comment = ?
        WHERE id = ?
        ''', (reason, application_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': '驳回成功'})
    except Exception as e:
        logger.error(f"驳回申请失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/settings/check_port', methods=['POST'])
def api_settings_check_port():
    """
    检测指定端口是否被占用。POST参数：port
    """
    try:
        port = int(request.json.get('port', 0))
        if not (1024 <= port <= 65535):
            return jsonify({'success': False, 'message': '端口号必须在1024-65535之间'})
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            s.bind(("0.0.0.0", port))
            s.close()
            return jsonify({'success': True, 'message': '端口未被占用'})
        except OSError:
            return jsonify({'success': False, 'message': '端口已被占用'})
    except Exception as e:
        logger.error(f"检测端口占用失败: {e}")
        return jsonify({'success': False, 'message': str(e)})
    

    
@app.route('/api/requirements/submit', methods=['POST'])
def submit_requirement():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 获取表单数据
        title = request.form.get('title')
        description = request.form.get('description')
        category = request.form.get('category')
        priority = request.form.get('priority')
        contact = request.form.get('contact')
        phone = request.form.get('phone')
        delivery_date = request.form.get('delivery_date')
        reviewer = request.form.get('approvals')  # 直接获取单个审批人
        
        # 使用系统当前时间
        submit_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"提交的数据: title={title}, description={description}, category={category}, priority={priority}, contact={contact}, phone={phone}, delivery_date={delivery_date}, reviewer={reviewer}, submit_time={submit_time}")
        
        if not all([title, description, category, contact, phone, delivery_date, reviewer]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        # 生成40位随机码
        chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
        random_code = ''.join(random.choice(chars) for _ in range(40))
        
        # 创建随机码目录
        requirement_dir = os.path.join(REQUIREMENT_DIR, random_code)
        if not os.path.exists(requirement_dir):
            os.makedirs(requirement_dir)
            logger.info(f"创建需求提交目录: {requirement_dir}")

        # 处理附件
        attachments = []
        if 'files' in request.files:
            files = request.files.getlist('files')
            for file in files:
                if file.filename:
                    # 保存文件
                    file_path = os.path.join(requirement_dir, file.filename)
                    file.save(file_path)
                    # 记录相对路径
                    relative_path = os.path.join('requirement_submissions', random_code, file.filename)
                    attachments.append(relative_path)

        # 保存到数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
            INSERT INTO requirement_submissions 
            (title, description, submitter, priority, category, attachments, status, contact, phone, delivery_date, reviewer, submit_time)
            VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?, ?)
            ''', (
                title, 
                description, 
                username, 
                priority, 
                category, 
                json.dumps(attachments),
                contact,
                phone,
                delivery_date,
                reviewer,  # 直接使用单个审批人
                submit_time  # 使用前端传入的提交时间
            ))
            
            conn.commit()
            logger.info("数据成功保存到数据库")
            
            return jsonify({
                'success': True, 
                'message': '需求提交成功',
                'data': {
                    'id': cursor.lastrowid,
                    'random_code': random_code
                }
            })
        except Exception as e:
            conn.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            raise
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"需求提交失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin-users', methods=['GET'])
def get_admin_users():
    """
    获取所有管理员用户（role='admin'），返回 username 和 real_name，按 real_name 升序排序。
    前端用于审批人选择，显示 real_name，实际传递 username。
    允许自己也可以作为审批人。
    """
    try:
        # 获取当前登录用户名
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        conn = get_db_connection()
        cursor = conn.cursor()
        # 查询所有管理员，不排除当前用户，按 real_name 排序
        cursor.execute('''
            SELECT username, real_name FROM login_users
            WHERE role = ?
            ORDER BY real_name ASC
        ''', ('admin',))
        admin_users = [
            {'username': row['username'], 'real_name': row['real_name']} for row in cursor.fetchall()
        ]
        conn.close()
        return jsonify({'success': True, 'data': admin_users})
    except Exception as e:
        logger.error(f"获取管理员列表失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取管理员列表失败: {str(e)}'}), 500
    
    


@app.route('/api/cad2gis/tools/update-run-times', methods=['POST'])
def update_cad2gis_run_times():
    try:
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新 cad2gis_model_market 表中的 run_times
        cursor.execute("""
            UPDATE cad2gis_model_market 
            SET run_times = COALESCE(run_times, 0) + 1 
            WHERE fmw_id = 'cad2gis'
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '运行次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新cad2gis运行次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新运行次数失败: {str(e)}'
        }), 500

@app.route('/api/Coordinate/tools/update-run-times', methods=['POST'])
def update_Coordinate_run_times():
    try:
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 401
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新 cad2gis_model_market 表中的 run_times
        cursor.execute("""
            UPDATE cad2gis_model_market 
            SET run_times = COALESCE(run_times, 0) + 1 
            WHERE fmw_id = 'cad2gis'
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '运行次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新cad2gis运行次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新运行次数失败: {str(e)}'
        }), 500


@app.route('/api/cad2gis/tools/parse', methods=['POST'])
def parse_cad2gis_tool():
    try:
        logger.info("正在解析cad2gis工具参数")
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        # 获取用户名
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400



        # 根据当前环境动态选择FMW文件路径
        if getattr(sys, 'frozen', False):
            # 生产环境，exe目录
            base_dir = os.path.dirname(sys.executable)
            fmw_path = os.path.join(base_dir, 'tools', 'cad2gis', 'cad2gis.fmw')
            logger.info(f"生产环境FMW路径: {fmw_path}")
        else:
            # 开发环境，app.py目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
            fmw_path = os.path.join(base_dir, 'tools', 'cad2gis', 'cad2gis.fmw')
            logger.info(f"开发环境FMW路径: {fmw_path}")
        # 打印当前exe路径、当前解压后的app.py路径和fmw_path路径
        logger.info(f"当前exe路径: {sys.executable}")
        logger.info(f"当前解压后的app.py路径: {os.path.abspath(__file__)}")
        logger.info(f"fmw_path路径: {fmw_path}")
        if os.path.exists(fmw_path):
            params = parse_fmw_parameters(fmw_path)
            parameters_list = params.get('parameters', [])
            parameters_dict = params.get('parameters_dict', {})
        else:
            logger.error(f"FMW文件不存在: {fmw_path}")
            parameters_list = []
            parameters_dict = {}
        
        return jsonify({
            'success': True,
            'data': {
                'parameters': parameters_list,
                'parameters_dict': parameters_dict
            }
        })
    except Exception as e:
        logger.error(f"解析cad2gis工具参数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'解析工具参数失败: {str(e)}'
        }), 500
    
@app.route('/api/Coordinate/tools/parse', methods=['POST'])
def parse_Coordinate_tool():
    try:
        logger.info("正在解析Coordinate工具参数")
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        # 获取用户名
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400



        # 根据当前环境动态选择FMW文件路径
        if getattr(sys, 'frozen', False):
            # 生产环境，exe目录
            base_dir = os.path.dirname(sys.executable)
            fmw_path = os.path.join(base_dir, 'tools', 'cad2gis', 'cad2gis.fmw')
            logger.info(f"生产环境FMW路径: {fmw_path}")
        else:
            # 开发环境，app.py目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
            fmw_path = os.path.join(base_dir, 'tools', 'cad2gis', 'cad2gis.fmw')
            logger.info(f"开发环境FMW路径: {fmw_path}")
        # 打印当前exe路径、当前解压后的app.py路径和fmw_path路径
        logger.info(f"当前exe路径: {sys.executable}")
        logger.info(f"当前解压后的app.py路径: {os.path.abspath(__file__)}")
        logger.info(f"fmw_path路径: {fmw_path}")
        if os.path.exists(fmw_path):
            params = parse_fmw_parameters(fmw_path)
            parameters_list = params.get('parameters', [])
            parameters_dict = params.get('parameters_dict', {})
        else:
            logger.error(f"FMW文件不存在: {fmw_path}")
            parameters_list = []
            parameters_dict = {}
        
        return jsonify({
            'success': True,
            'data': {
                'parameters': parameters_list,
                'parameters_dict': parameters_dict
            }
        })
    except Exception as e:
        logger.error(f"解析cad2gis工具参数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'解析工具参数失败: {str(e)}'
        }), 500

@app.route('/api/get_departments', methods=['POST'])
def get_departments():
    """
    获取所有部门名称，去重并按拼音首字母排序，返回JSON数组。
    禁止硬编码，所有配置从config.py读取。
    """
    try:
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        # 查询所有部门名称
        cursor.execute('SELECT department FROM department')
        departments = [row[0] for row in cursor.fetchall() if row[0]]
        # 去重
        departments = list(set(departments))
        # 按拼音首字母排序（中文）
        def get_pinyin_first(word):
            # 获取拼音首字母
            py = lazy_pinyin(word, style=Style.FIRST_LETTER)
            return py[0] if py else word
        departments.sort(key=lambda x: get_pinyin_first(x))
        return jsonify({'departments': departments})
    except Exception as e:
        return jsonify({'departments': [], 'error': str(e)}), 500
@app.route('/api/requirements/my-approvals', methods=['GET'])
def get_my_requirement_approvals():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        logger.info(f"收到需求审批列表请求，用户名: {username}")
        
        if not username:
            logger.error("未登录")
            return jsonify({'success': False, 'message': '未登录'}), 401

        logger.info(f"开始获取需求审批列表，用户名: {username}")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取需要当前用户审批的需求列表
        query = '''
        SELECT 
            r.id,
            r.title,
            r.description,
            r.submitter,
            u.real_name as submitter_real_name,
            r.submit_time,
            r.priority,
            r.status,
            r.category,
            r.attachments,
            r.reviewer,
            r.review_time,
            r.review_comment,
            r.contact,
            r.phone,
            r.delivery_date
        FROM requirement_submissions r
        LEFT JOIN login_users u ON r.submitter = u.username
        WHERE r.reviewer = ?
        ORDER BY r.submit_time DESC
        '''
        logger.info(f"执行SQL查询: {query}")
        logger.info(f"查询参数: {username}")
        
        cursor.execute(query, (username,))
        requirements = cursor.fetchall()
        logger.info(f"查询到 {len(requirements)} 条需求审批记录")
        
        # 转换结果为字典列表
        result = []
        for req in requirements:
            requirement_dict = {
                'id': req[0],
                'title': req[1],
                'description': req[2],
                'submitter': req[3],
                'submitter_real_name': req[4],
                'submit_time': req[5],
                'priority': req[6],
                'status': req[7],
                'category': req[8],
                'attachments': json.loads(req[9]) if req[9] else [],
                'reviewer': req[10],
                'review_time': req[11],
                'review_comment': req[12],
                'contact': req[13],
                'phone': req[14],
                'delivery_date': req[15]
            }
            result.append(requirement_dict)
            logger.info(f"处理记录: {requirement_dict}")
        
        logger.info("需求审批列表获取成功")
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"获取需求审批列表失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/requirements/approve', methods=['POST'])
def approve_requirement():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 获取需求ID
        data = request.get_json()
        requirement_id = data.get('requirementId')
        
        if not requirement_id:
            return jsonify({'success': False, 'message': '缺少需求ID'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查需求是否存在且属于当前用户审批
        cursor.execute('''
        SELECT id FROM requirement_submissions 
        WHERE id = ? AND reviewer = ? AND status = 'pending'
        ''', (requirement_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '需求不存在或无权审批'}), 403
        
        # 更新需求状态，并初始化 count 字段为 0
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
        UPDATE requirement_submissions 
        SET status = 'approved', review_time = ?, review_comment = '已通过', count = 0
        WHERE id = ?
        ''', (current_time, requirement_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '审批通过成功'
        })
        
    except Exception as e:
        logger.error(f"需求审批失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/requirements/reject', methods=['POST'])
def reject_requirement():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 获取需求ID和驳回原因
        data = request.get_json()
        requirement_id = data.get('requirementId')
        reason = data.get('reason')
        
        if not requirement_id or not reason:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查需求是否存在且属于当前用户审批
        cursor.execute('''
        SELECT id FROM requirement_submissions 
        WHERE id = ? AND reviewer = ? AND status = 'pending'
        ''', (requirement_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '需求不存在或无权审批'}), 403
        
        # 更新需求状态
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
        UPDATE requirement_submissions 
        SET status = 'rejected', review_time = ?, review_comment = ?
        WHERE id = ?
        ''', (current_time, reason, requirement_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '驳回成功'
        })
        
    except Exception as e:
        logger.error(f"需求驳回失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/requirements/download', methods=['GET', 'POST'])
def download_requirement_file():
    try:
        # 兼容 GET 和 POST 两种方式获取参数
        if request.method == 'POST':
            data = request.get_json()
            file_path = data.get('filePath')
        else:
            file_path = request.args.get('filePath')
        if not file_path:
            return jsonify({'success': False, 'message': '文件路径不能为空'}), 400
        # 构建完整的文件路径
        full_path = os.path.join(current_dir, file_path)
        if not os.path.exists(full_path):
            return jsonify({'success': False, 'message': '文件不存在'}), 404
        file_size = os.path.getsize(full_path)
        filename = os.path.basename(file_path)
        encoded_filename = urllib.parse.quote(filename)
        range_header = request.headers.get('Range', None)
        # 支持断点续传
        def generate(file_path, start, end):
            with open(file_path, 'rb') as f:
                f.seek(start)
                remaining = end - start + 1
                chunk_size = 8192
                while remaining > 0:
                    read_size = min(chunk_size, remaining)
                    data = f.read(read_size)
                    if not data:
                        break
                    yield data
                    remaining -= len(data)
        if range_header:
            match = re.match(r'bytes=(\d+)-(\d+)?', range_header)
            if match:
                start = int(match.group(1))
                end = int(match.group(2)) if match.group(2) else file_size - 1
                if end >= file_size:
                    end = file_size - 1
                length = end - start + 1
                resp = Response(generate(full_path, start, end), status=206, mimetype='application/octet-stream')
                resp.headers.add('Content-Range', f'bytes {start}-{end}/{file_size}')
                resp.headers.add('Accept-Ranges', 'bytes')
                resp.headers.add('Content-Length', str(length))
                resp.headers.add('Content-Disposition', f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}')
                return resp
            else:
                return jsonify({'success': False, 'message': 'Range头格式错误'}), 416
        # 普通下载
        resp = make_response(send_file(full_path, as_attachment=True, download_name=filename))
        resp.headers['Content-Disposition'] = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        resp.headers['Accept-Ranges'] = 'bytes'
        return resp
    except Exception as e:
        logger.error(f'下载文件失败: {str(e)}')
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'}), 500

@app.route('/api/requirements/complete', methods=['POST'])
def complete_requirement():
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 获取需求ID和完成时间
        data = request.get_json()
        requirement_id = data.get('requirementId')
        complete_time = data.get('completeTime')
        
        if not requirement_id or not complete_time:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查需求是否存在且属于当前用户审批
        cursor.execute('''
        SELECT id FROM requirement_submissions 
        WHERE id = ? AND reviewer = ? AND status = 'pending'
        ''', (requirement_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '需求不存在或无权操作'}), 403
        
        # 更新需求状态为已完成，使用前端传来的时间
        cursor.execute('''
        UPDATE requirement_submissions 
        SET status = 'completed', review_time = ?, review_comment = '已完成'
        WHERE id = ?
        ''', (complete_time, requirement_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '确认完成成功'
        })
        
    except Exception as e:
        logger.error(f"确认需求完成失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/detail', methods=['POST', 'OPTIONS'])
def get_tool_detail():
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        fmw_id = data.get('fmw_id')
        
        if not fmw_id:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询工具详情
        cursor.execute('''
            SELECT id, fmw_id, fmw_name, project, fmw_path, run_times, created_at, author, description
            FROM model_market
            WHERE fmw_id = ?
        ''', (fmw_id,))
        
        tool = cursor.fetchone()
        conn.close()
        
        if not tool:
            return jsonify({
                'success': False,
                'message': '工具不存在'
            }), 404
            
        # 将查询结果转换为字典
        tool_dict = {
            'id': tool[0],
            'fmw_id': tool[1],
            'fmw_name': tool[2],
            'project': tool[3],
            'fmw_path': tool[4],
            'run_times': tool[5],
            'created_at': tool[6],
            'author': tool[7],
            'description': tool[8]
        }
        
        return jsonify({
            'success': True,
            'data': tool_dict
        })
        
    except Exception as e:
        logger.error(f"获取工具详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具详情失败: {str(e)}'
        }), 500
@app.route('/api/user/list', methods=['POST'])
def user_list():
    """
    获取所有用户信息（不返回密码字段）。
    """
    try:
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, real_name, role, department, created_at FROM login_users')
        users = [dict(zip(['id', 'username', 'real_name', 'role', 'department', 'created_at'], row)) for row in cursor.fetchall()]
        return jsonify({'success': True, 'users': users})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/user/add', methods=['POST'])
def user_add():
    """
    新增用户，用户名唯一。
    """
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        real_name = data.get('real_name')
        role = data.get('role')
        department = data.get('department')
        if not all([username, password, real_name, role, department]):
            return jsonify({'success': False, 'message': '所有字段均为必填'})
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        # 检查用户名唯一
        cursor.execute('SELECT COUNT(*) FROM login_users WHERE username=?', (username,))
        if cursor.fetchone()[0] > 0:
            return jsonify({'success': False, 'message': '用户名已存在'})
        # 检查部门存在
        cursor.execute('SELECT COUNT(*) FROM department WHERE department=?', (department,))
        if cursor.fetchone()[0] == 0:
            return jsonify({'success': False, 'message': '部门不存在'})
        # 插入用户
        cursor.execute('INSERT INTO login_users (username, password, real_name, role, department, created_at) VALUES (?, ?, ?, ?, ?, datetime("now"))',
                       (username, password, real_name, role, department))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/user/delete', methods=['POST'])
def user_delete():
    """
    删除用户，禁止删除用户名为admin的账号。
    """
    try:
        data = request.get_json()
        username = data.get('username')
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'})
        if username == 'admin':
            return jsonify({'success': False, 'message': 'admin账号禁止删除'})
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('DELETE FROM login_users WHERE username=?', (username,))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/user/reset_pwd', methods=['POST'])
def user_reset_pwd():
    """
    重置用户密码，自动生成随机密码并返回。
    """
    try:
        data = request.get_json()
        username = data.get('username')
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'})
        # 生成随机密码
        new_pwd = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('UPDATE login_users SET password=? WHERE username=?', (new_pwd, username))
        conn.commit()
        return jsonify({'success': True, 'new_password': new_pwd})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/user/update', methods=['POST'])
def user_update():
    """
    修改用户类型/部门/真实姓名（用户名不可修改）。
    """
    try:
        data = request.get_json()
        username = data.get('username')
        real_name = data.get('real_name')
        role = data.get('role')
        department = data.get('department')
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'})
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        # 检查部门存在
        if department:
            cursor.execute('SELECT COUNT(*) FROM department WHERE department=?', (department,))
            if cursor.fetchone()[0] == 0:
                return jsonify({'success': False, 'message': '部门不存在'})
        # 只更新有传递的字段
        update_fields = []
        params = []
        if real_name:
            update_fields.append('real_name=?')
            params.append(real_name)
        if role:
            update_fields.append('role=?')
            params.append(role)
        if department:
            update_fields.append('department=?')
            params.append(department)
        if not update_fields:
            return jsonify({'success': False, 'message': '无可更新字段'})
        params.append(username)
        sql = f'UPDATE login_users SET {", ".join(update_fields)} WHERE username=?'
        cursor.execute(sql, tuple(params))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/department/add', methods=['POST'])
def department_add():
    """
    新增部门，唯一性校验。
    """
    try:
        data = request.get_json()
        department = data.get('department')
        if not department:
            return jsonify({'success': False, 'message': '部门名称不能为空'})
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        # 唯一性校验
        cursor.execute('SELECT COUNT(*) FROM department WHERE department=?', (department,))
        if cursor.fetchone()[0] > 0:
            return jsonify({'success': False, 'message': '部门已存在'})
        cursor.execute('INSERT INTO department (department, created_at) VALUES (?, datetime("now"))', (department,))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})
@app.route('/api/tools/run_records', methods=['GET', 'POST', 'OPTIONS'])
def get_run_records():
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        # 根据请求方法获取参数
        if request.method == 'GET':
            fmw_id = request.args.get('fmw_id')
            username = request.args.get('username')
            page = int(request.args.get('page', 1))  # 获取页码，默认第1页
            page_size = int(request.args.get('page_size', 10))  # 获取每页数量，默认10条
        else:  # POST
            data = request.get_json()
            fmw_id = data.get('fmw_id')
            username = data.get('username')
            page = int(data.get('page', 1))
            page_size = int(data.get('page_size', 10))
            
        logger.info(f"\n=== 获取运行记录 ===")
        logger.info(f"请求方法: {request.method}")
        logger.info(f"fmw_id: {fmw_id}")
        logger.info(f"username: {username}")
        logger.info(f"页码: {page}")
        logger.info(f"每页数量: {page_size}")
        
        if not fmw_id or not username:
            logger.error("缺少必要参数")
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 计算总记录数
        cursor.execute('''
            SELECT COUNT(*) as total 
            FROM task_records 
            WHERE tool_id = ? AND submitter = ?
        ''', (fmw_id, username))
        total = cursor.fetchone()['total']
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询运行记录（带分页）
        query = '''
            SELECT 
                tr.id,
                tr.tool_id,
                tr.tool_name,
                tr.project,
                tr.submitter,
                tr.submit_time,
                tr.task_name,
                tr.task_id,
                tr.fme_args,
                tr.status,
                tr.time_consuming,
                tr.file_size,
                tr.file_name,
                tr.error_message
            FROM task_records tr
            WHERE tr.tool_id = ? AND tr.submitter = ?
            ORDER BY tr.submit_time DESC
            LIMIT ? OFFSET ?
        '''
        logger.info(f"执行SQL查询: {query}")
        logger.info(f"查询参数: fmw_id={fmw_id}, submitter={username}, limit={page_size}, offset={offset}")
        
        cursor.execute(query, (fmw_id, username, page_size, offset))
        records = cursor.fetchall()
        
        logger.info(f"查询到 {len(records)} 条记录")
        
        # 格式化返回数据
        result = []
        for record in records:
            record_dict = {
                'id': record[0],
                'tool_id': record[1],
                'tool_name': record[2],
                'project': record[3],
                'submitter': record[4],
                'submit_time': record[5],
                'task_name': record[6],
                'task_id': record[7],
                'fme_args': json.loads(record[8]) if record[8] else {},
                'status': record[9],
                'time_consuming': record[10],
                'file_size': record[11],
                'file_name': record[12],
                'error_message': record[13]
            }
            result.append(record_dict)
            logger.info(f"记录详情: {json.dumps(record_dict, ensure_ascii=False, indent=2)}")
        
        conn.close()
        
        logger.info("==================\n")
        
        return jsonify({
            'success': True,
            'data': {
                'records': result,
                'pagination': {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': total_pages
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取运行记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取运行记录失败: {str(e)}'
        }), 500
# 生成随机文件夹名称
def generate_random_folder():
    """生成随机文件夹名称"""
    chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    return ''.join(random.choice(chars) for _ in range(40))

def generate_unique_extract_dir(base_path):
    """生成唯一的提取目录名，避免多文件同时上传时的目录冲突"""
    counter = 1
    while True:
        extract_dir = os.path.join(base_path, f'extracted_{counter}')
        if not os.path.exists(extract_dir):
            return extract_dir
        counter += 1

def force_delete_path(path):
    """强制删除文件或文件夹，处理GDB等特殊文件夹的删除问题"""
    import stat
    import time
    
    def on_rm_error(func, path, exc_info):
        """处理删除错误，修改文件权限后重试"""
        try:
            # 修改文件权限为可写
            os.chmod(path, stat.S_IWRITE)
            # 重试删除
            func(path)
        except Exception as e:
            print(f"重试删除失败: {path}, 错误: {e}")
            # 如果还是失败，尝试重命名后删除
            try:
                temp_name = path + f"_delete_{int(time.time())}"
                os.rename(path, temp_name)
                if os.path.isdir(temp_name):
                    shutil.rmtree(temp_name, ignore_errors=True)
                else:
                    os.remove(temp_name)
            except Exception as e2:
                print(f"重命名删除也失败: {path}, 错误: {e2}")
    
    try:
        if os.path.isfile(path):
            # 删除文件
            os.chmod(path, stat.S_IWRITE)
            os.remove(path)
        elif os.path.isdir(path):
            # 删除文件夹
            shutil.rmtree(path, onerror=on_rm_error)
        return True
    except Exception as e:
        print(f"强制删除失败: {path}, 错误: {e}")
        return False
@app.route('/api/department/delete', methods=['POST'])
def department_delete():
    """
    删除部门，若有用户关联则禁止删除。
    """
    try:
        data = request.get_json()
        department = data.get('department')
        if not department:
            return jsonify({'success': False, 'message': '部门名称不能为空'})
        from config import DB_PATH
        import sqlite3
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        # 检查是否有用户关联该部门
        cursor.execute('SELECT COUNT(*) FROM login_users WHERE department=?', (department,))
        if cursor.fetchone()[0] > 0:
            return jsonify({'success': False, 'message': '有用户属于该部门，禁止删除'})
        cursor.execute('DELETE FROM department WHERE department=?', (department,))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})
# 处理压缩文件
def extract_archive(file_path, extract_path):
    """解压文件并筛选出DWG文件"""
    try:
        if file_path.lower().endswith('.zip'):
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                # 获取所有文件列表
                file_list = zip_ref.namelist()
                # 筛选出DWG文件
                dwg_files = [f for f in file_list if f.lower().endswith('.dwg')]
                # 解压DWG文件
                extracted_files = []
                for dwg_file in dwg_files:
                    # 处理文件名冲突
                    filename, ext = os.path.splitext(dwg_file)
                    counter = 1
                    new_filename = dwg_file
                    
                    while os.path.exists(os.path.join(extract_path, new_filename)):
                        new_filename = f"{filename}_{counter}{ext}"
                        counter += 1
                    
                    # 解压到临时文件
                    temp_path = os.path.join(extract_path, f"temp_{new_filename}")
                    zip_ref.extract(dwg_file, extract_path)
                    # 重命名文件
                    os.rename(os.path.join(extract_path, dwg_file), temp_path)
                    os.rename(temp_path, os.path.join(extract_path, new_filename))
                    extracted_files.append(new_filename)
                return extracted_files
        elif file_path.lower().endswith(('.rar', '.7z')):
            with rarfile.RarFile(file_path, 'r') as rar_ref:
                # 获取所有文件列表
                file_list = rar_ref.namelist()
                # 筛选出DWG文件
                dwg_files = [f for f in file_list if f.lower().endswith('.dwg')]
                # 解压DWG文件
                extracted_files = []
                for dwg_file in dwg_files:
                    # 处理文件名冲突
                    filename, ext = os.path.splitext(dwg_file)
                    counter = 1
                    new_filename = dwg_file
                    
                    while os.path.exists(os.path.join(extract_path, new_filename)):
                        new_filename = f"{filename}_{counter}{ext}"
                        counter += 1
                    
                    # 解压到临时文件
                    temp_path = os.path.join(extract_path, f"temp_{new_filename}")
                    rar_ref.extract(dwg_file, extract_path)
                    # 重命名文件
                    os.rename(os.path.join(extract_path, dwg_file), temp_path)
                    os.rename(temp_path, os.path.join(extract_path, new_filename))
                    extracted_files.append(new_filename)
                return extracted_files
        return []
    except Exception as e:
        logger.error(f"解压文件失败: {str(e)}")
        return []

# 工具客户端下载路径映射
CLIENT_DOWNLOAD_PATHS = {
    'fme': os.path.join('download', 'fme', 'FME.zip'),
    'gispro': os.path.join('download', 'gispro', 'GISPRO.zip'),
    'gsi': os.path.join('download', 'GSI', 'GSI.zip'),
}

@app.route('/api/download/client', methods=['GET', 'POST'])
def download_client():
    """
    工具客户端下载接口，支持断点续传和多线程下载。
    GET参数: ?type=fme|gispro|gsi
    POST参数: {type: 'fme'|'gispro'|'gsi'}
    """
    # 兼容GET和POST两种方式
    if request.method == 'POST':
        data = request.get_json(force=True)
        tool_type = data.get('type')
    else:
        tool_type = request.args.get('type')
    if tool_type not in CLIENT_DOWNLOAD_PATHS:
        return jsonify({'error': '参数type无效'}), 400

    # 根据运行环境拼接绝对路径
    if getattr(sys, 'frozen', False):
        base_dir = os.path.dirname(sys.executable)
    else:
        base_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(base_dir, CLIENT_DOWNLOAD_PATHS[tool_type])

    if not os.path.exists(file_path):
        logger.error(f'下载文件不存在: {file_path}')
        logger.info(f'当前工作目录: {base_dir}')
        logger.info(f'查找的文件路径: {file_path}')
        # 列出download目录的内容以便调试
        download_dir = os.path.join(base_dir, 'download')
        if os.path.exists(download_dir):
            logger.info(f'download目录存在，内容: {os.listdir(download_dir)}')
        else:
            logger.error(f'download目录不存在: {download_dir}')
        return jsonify({'error': f'文件不存在: {file_path}'}), 404

    file_size = os.path.getsize(file_path)
    file_name = os.path.basename(file_path)
    encoded_filename = urllib.parse.quote(file_name)
    range_header = request.headers.get('Range', None)

    def generate(file_path, start, end):
        """分块读取文件，支持大文件断点续传"""
        with open(file_path, 'rb') as f:
            f.seek(start)
            remaining = end - start + 1
            chunk_size = 8192
            while remaining > 0:
                read_size = min(chunk_size, remaining)
                data = f.read(read_size)
                if not data:
                    break
                yield data
                remaining -= len(data)

    if range_header:
        # 解析Range头，支持多线程分块下载
        match = re.match(r'bytes=(\d+)-(\d+)?', range_header)
        if match:
            start = int(match.group(1))
            end = int(match.group(2)) if match.group(2) else file_size - 1
            if end >= file_size:
                end = file_size - 1
            length = end - start + 1
            resp = Response(generate(file_path, start, end),
                            status=206,
                            mimetype='application/zip')
            resp.headers.add('Content-Range', f'bytes {start}-{end}/{file_size}')
            resp.headers.add('Accept-Ranges', 'bytes')
            resp.headers.add('Content-Length', str(length))
            resp.headers.add('Content-Disposition', f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}')
            return resp
        else:
            # Range头格式错误时也要return，避免None
            return jsonify({'error': 'Range头格式错误'}), 416
    else:
        # 普通下载
        resp = make_response(send_file(file_path, as_attachment=True, download_name=file_name))
        resp.headers['Content-Disposition'] = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        resp.headers['Accept-Ranges'] = 'bytes'
        return resp



@app.route('/api/cad2gis/upload', methods=['POST'])
def upload_cad2gis_file():
    """
    CAD转GIS文件上传接口
    支持DWG直传和压缩包（zip/rar/7z），压缩包用7z.exe/UnRAR.exe解压，DWG自动加序号，返回文件列表
    """
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件被上传'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})

    try:
        # 获取folderId参数，如果没有则生成新的
        folder_id = request.form.get('folderId')
        if not folder_id:
            folder_id = generate_random_folder()

        # 使用TEMP_DIR环境变量
        folder_path = os.path.join(TEMP_DIR, folder_id)
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        # 直接上传DWG文件
        if file.filename.lower().endswith('.dwg'):
            original_filename = file.filename
            filename, ext = os.path.splitext(original_filename)
            counter = 1
            new_filename = original_filename
            while os.path.exists(os.path.join(folder_path, new_filename)):
                new_filename = f"{filename}_{counter}{ext}"
                counter += 1
            file_path = os.path.join(folder_path, new_filename)
            file.save(file_path)
            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'folderId': folder_id,
                'files': [{
                    'name': new_filename,
                    'size': os.path.getsize(file_path),
                    'status': 'success'
                }]
            })

        # 处理压缩包
        if file.filename.lower().endswith(('.zip', '.rar', '.7z')):
            # 使用唯一的提取目录名，避免多文件同时上传时的冲突
            extract_dir = generate_unique_extract_dir(folder_path)
            os.makedirs(extract_dir)

            file_path = os.path.join(folder_path, file.filename)
            file.save(file_path)

            # 获取base_dir用于查找7z/UnRAR工具
            if getattr(sys, 'frozen', False):
                base_dir = os.path.dirname(sys.executable)
            else:
                base_dir = os.path.dirname(os.path.abspath(__file__))

            try:
                if file.filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                elif file.filename.lower().endswith('.7z'):
                    seven_zip_path = os.path.join(base_dir, '7z', '7z.exe')
                    if not os.path.exists(seven_zip_path):
                        return jsonify({'success': False, 'message': '7-Zip工具未找到，请确保7z.exe位于backend/7z目录下'})
                    cmd = [seven_zip_path, 'x', file_path, f'-o{extract_dir}', '-y']
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        raise Exception(f"7-Zip解压失败: {result.stderr}")
                elif file.filename.lower().endswith('.rar'):
                    unrar_path = os.path.join(base_dir, '7z', 'UnRAR.exe')
                    if not os.path.exists(unrar_path):
                        return jsonify({'success': False, 'message': 'UnRAR工具未找到，请确保UnRAR.exe位于backend/7z目录下'})
                    cmd = [unrar_path, 'x', '-y', file_path, extract_dir]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        raise Exception(f"UnRAR解压失败: {result.stderr}")
            except Exception as e:
                return jsonify({'success': False, 'message': f'解压失败: {str(e)}'})

            # 递归查找所有DWG文件并移动到主文件夹
            dwg_files = []
            for root, dirs, files in os.walk(extract_dir):
                for file_name in files:
                    if file_name.lower().endswith('.dwg'):
                        source_path = os.path.join(root, file_name)
                        target_path = os.path.join(folder_path, file_name)
                        counter = 1
                        while os.path.exists(target_path):
                            name, ext = os.path.splitext(file_name)
                            target_path = os.path.join(folder_path, f"{name}_{counter}{ext}")
                            counter += 1
                        shutil.move(source_path, target_path)
                        dwg_files.append({
                            'name': os.path.basename(target_path),
                            'size': os.path.getsize(target_path),
                            'status': 'success'
                        })

            # 删除解压目录和压缩包
            shutil.rmtree(extract_dir)
            os.remove(file_path)

            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'folderId': folder_id,
                'files': dwg_files
            })

        return jsonify({
            'success': False,
            'message': '不支持的文件类型，只能上传DWG文件或包含DWG文件的压缩包'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传文件时发生错误: {str(e)}'
        })


@app.route('/api/Coordinate/upload', methods=['POST'])
def upload_Coordinate_file():
    """
    坐标转换工具文件上传接口
    支持DWG直传和压缩包（zip/rar/7z），压缩包用7z.exe/UnRAR.exe解压，
    递归批量提取DWG、SHP及其所有同名相关文件、GDB（含.gdbtable等），同名自动加序号
    """


    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件被上传'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})

    try:
        folder_id = request.form.get('folderId')
        if not folder_id:
            folder_id = generate_random_folder()

        folder_path = os.path.join(TEMP_DIR, folder_id)
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        def get_unique_name(name, existed):
            base, ext = os.path.splitext(name)
            counter = 1
            new_name = name
            while new_name in existed:
                new_name = f"{base}_{counter}{ext}"
                counter += 1
            existed.add(new_name)
            return new_name

        # 直接上传DWG
        if file.filename.lower().endswith('.dwg'):
            existed = set(os.listdir(folder_path))
            new_filename = get_unique_name(file.filename, existed)
            file_path = os.path.join(folder_path, new_filename)
            file.save(file_path)
            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'folderId': folder_id,
                'files': [{'type': 'DWG', 'name': new_filename}]
            })

        # 处理压缩包
        if file.filename.lower().endswith(('.zip', '.rar', '.7z')):
            # 使用唯一的提取目录名，避免多文件同时上传时的冲突
            extract_dir = generate_unique_extract_dir(folder_path)
            os.makedirs(extract_dir)

            file_path = os.path.join(folder_path, file.filename)
            file.save(file_path)

            # 获取base_dir用于查找7z/UnRAR工具
            if getattr(sys, 'frozen', False):
                base_dir = os.path.dirname(sys.executable)
            else:
                base_dir = os.path.dirname(os.path.abspath(__file__))

            try:
                if file.filename.lower().endswith('.zip'):
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                elif file.filename.lower().endswith('.7z'):
                    seven_zip_path = os.path.join(base_dir, '7z', '7z.exe')
                    if not os.path.exists(seven_zip_path):
                        return jsonify({'success': False, 'message': '7-Zip工具未找到，请确保7z.exe位于backend/7z目录下'})
                    cmd = [seven_zip_path, 'x', file_path, f'-o{extract_dir}', '-y']
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        raise Exception(f"7-Zip解压失败: {result.stderr}")
                elif file.filename.lower().endswith('.rar'):
                    unrar_path = os.path.join(base_dir, '7z', 'UnRAR.exe')
                    if not os.path.exists(unrar_path):
                        return jsonify({'success': False, 'message': 'UnRAR工具未找到，请确保UnRAR.exe位于backend/7z目录下'})
                    cmd = [unrar_path, 'x', '-y', file_path, extract_dir]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        raise Exception(f"UnRAR解压失败: {result.stderr}")
            except Exception as e:
                return jsonify({'success': False, 'message': f'解压失败: {str(e)}'})

            file_list = []
            existed = set(os.listdir(folder_path))
            processed_shp_bases = set()
            # 递归扫描所有文件和文件夹
            for root, dirs, files in os.walk(extract_dir):
                # 1. 识别GDB文件夹（含.gdbtable文件）
                for d in dirs:
                    dpath = os.path.join(root, d)
                    if d.lower().endswith('.gdb'):
                        has_gdbtable = False
                        for _r, _ds, _fs in os.walk(dpath):
                            for _f in _fs:
                                if _f.lower().endswith('.gdbtable'):
                                    has_gdbtable = True
                                    break
                            if has_gdbtable:
                                break
                        if has_gdbtable:
                            gdb_name = get_unique_name(os.path.basename(d), existed)
                            target_gdb = os.path.join(folder_path, gdb_name)
                            if os.path.exists(target_gdb):
                                shutil.rmtree(target_gdb)
                            shutil.move(dpath, target_gdb)
                            file_list.append({'type': 'GDB', 'name': gdb_name})
                # 2. 识别SHP及其相关文件
                for fname in files:
                    fpath = os.path.join(root, fname)
                    if fname.lower().endswith('.shp'):
                        base = os.path.splitext(fname)[0]
                        if (root, base) in processed_shp_bases:
                            continue
                        processed_shp_bases.add((root, base))
                        related_exts = ['.shp', '.shx', '.dbf', '.prj', '.sbn', '.sbx', '.cpg', '.qix', '.fix', '.ain', '.aih', '.atx', '.ixs', '.mxs', '.xml']
                        for ext in related_exts:
                            related_file = base + ext
                            related_path = os.path.join(root, related_file)
                            if os.path.exists(related_path):
                                new_name = get_unique_name(os.path.basename(related_file), existed)
                                shutil.move(related_path, os.path.join(folder_path, new_name))
                                if ext == '.shp':
                                    file_list.append({'type': 'SHP', 'name': new_name})
                    # 3. 识别DWG文件
                    elif fname.lower().endswith('.dwg'):
                        new_name = get_unique_name(os.path.basename(fname), existed)
                        shutil.move(fpath, os.path.join(folder_path, new_name))
                        file_list.append({'type': 'DWG', 'name': new_name})

            # 清理解压目录和压缩包
            shutil.rmtree(extract_dir)
            os.remove(file_path)
            return jsonify({
                'success': True,
                'message': '文件上传成功',
                'folderId': folder_id,
                'files': file_list
            })

        return jsonify({
            'success': False,
            'message': '不支持的文件类型，只能上传DWG文件或包含DWG/SHP/GDB的压缩包'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'上传文件时发生错误: {str(e)}'
        })


@app.route('/api/cad2gis/delete', methods=['POST'])
def delete_cad2gis_file():
    try:
        data = request.get_json()
        if not data or 'folderId' not in data or 'fileName' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            })
        
        folder_id = data['folderId']
        file_name = data['fileName']
        
        # 使用TEMP_DIR环境变量构建文件路径
        file_path = os.path.join(TEMP_DIR, folder_id, file_name)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            })
        
        try:
            # 如果是SHP，批量删除配套文件
            if file_name.lower().endswith('.shp'):
                base_name = os.path.splitext(file_name)[0]
                exts = ['.shp', '.shx', '.dbf', '.prj', '.sbn', '.sbx', '.cpg', '.qix', '.fix', '.ain', '.aih', '.atx', '.ixs', '.mxs', '.xml']
                for ext in exts:
                    related_file = os.path.join(TEMP_DIR, folder_id, base_name + ext)
                    if os.path.exists(related_file):
                        force_delete_path(related_file)
            else:
                force_delete_path(file_path)
            
            # 检查文件夹是否为空
            folder_path = os.path.join(TEMP_DIR, folder_id)
            if os.path.exists(folder_path) and not os.listdir(folder_path):
                # 如果文件夹为空，删除文件夹
                force_delete_path(folder_path)
                print(f"空文件夹已删除: {folder_path}")
            
            return jsonify({
                'success': True,
                'message': '文件删除成功'
            })
            
        except Exception as e:
            print(f"删除文件时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'删除文件时发生错误: {str(e)}'
            })
            
    except Exception as e:
        print(f"处理删除请求时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理删除请求时发生错误: {str(e)}'
        })


@app.route('/api/Coordinate/delete', methods=['POST'])
def delete_Coordinate_file():
    try:
        data = request.get_json()
        if not data or 'folderId' not in data or 'fileName' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            })
        
        folder_id = data['folderId']
        file_name = data['fileName']
        
        # 使用TEMP_DIR环境变量构建文件路径
        file_path = os.path.join(TEMP_DIR, folder_id, file_name)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            })
        
        try:
            # 如果是SHP，批量删除配套文件
            if file_name.lower().endswith('.shp'):
                base_name = os.path.splitext(file_name)[0]
                exts = ['.shp', '.shx', '.dbf', '.prj', '.sbn', '.sbx', '.cpg', '.qix', '.fix', '.ain', '.aih', '.atx', '.ixs', '.mxs', '.xml']
                for ext in exts:
                    related_file = os.path.join(TEMP_DIR, folder_id, base_name + ext)
                    if os.path.exists(related_file):
                        force_delete_path(related_file)
            else:
                force_delete_path(file_path)
            
            # 检查文件夹是否为空
            folder_path = os.path.join(TEMP_DIR, folder_id)
            if os.path.exists(folder_path) and not os.listdir(folder_path):
                # 如果文件夹为空，删除文件夹
                force_delete_path(folder_path)
                print(f"空文件夹已删除: {folder_path}")
            
            return jsonify({
                'success': True,
                'message': '文件删除成功'
            })
            
        except Exception as e:
            print(f"删除文件时发生错误: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'删除文件时发生错误: {str(e)}'
            })
            
    except Exception as e:
        print(f"处理删除请求时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理删除请求时发生错误: {str(e)}'
        })

@app.route('/api/tools/download_result', methods=['GET', 'POST'])
def download_result():
    try:
        # 兼容 GET 和 POST 两种方式获取参数
        if request.method == 'POST':
            data = request.get_json()
            file_path = data.get('file_path')
        else:
            file_path = request.args.get('file_path')
        if not file_path:
            return jsonify({'success': False, 'message': '文件路径不能为空'}), 400
        # 构建完整的文件路径
        full_path = os.path.join(current_dir, file_path)
        if not os.path.exists(full_path):
            return jsonify({'success': False, 'message': '文件不存在'}), 404
        file_size = os.path.getsize(full_path)
        filename = os.path.basename(file_path)
        encoded_filename = urllib.parse.quote(filename)
        range_header = request.headers.get('Range', None)
        # 支持断点续传
        def generate(file_path, start, end):
            with open(file_path, 'rb') as f:
                f.seek(start)
                remaining = end - start + 1
                chunk_size = 8192
                while remaining > 0:
                    read_size = min(chunk_size, remaining)
                    data = f.read(read_size)
                    if not data:
                        break
                    yield data
                    remaining -= len(data)
        if range_header:
            match = re.match(r'bytes=(\d+)-(\d+)?', range_header)
            if match:
                start = int(match.group(1))
                end = int(match.group(2)) if match.group(2) else file_size - 1
                if end >= file_size:
                    end = file_size - 1
                length = end - start + 1
                resp = Response(generate(full_path, start, end), status=206, mimetype='application/octet-stream')
                resp.headers.add('Content-Range', f'bytes {start}-{end}/{file_size}')
                resp.headers.add('Accept-Ranges', 'bytes')
                resp.headers.add('Content-Length', str(length))
                resp.headers.add('Content-Disposition', f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}')
                return resp
            else:
                return jsonify({'success': False, 'message': 'Range头格式错误'}), 416
        # 普通下载
        resp = make_response(send_file(full_path, as_attachment=True, download_name=filename))
        resp.headers['Content-Disposition'] = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        resp.headers['Accept-Ranges'] = 'bytes'
        return resp
    except Exception as e:
        logger.error(f'下载文件失败: {str(e)}')
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'}), 500
        logger.error(f"保存系统设置失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/settings/ips', methods=['POST'])
def api_settings_ips():
    """
    获取本机所有可用IPv4地址（过滤虚拟网卡，优先显示有网关、已启用的物理网卡）。
    """
    try:
        ip_list = []
        stats = psutil.net_if_stats()
        # 常见虚拟网卡关键字
        vm_keywords = ['vmware', 'virtual', 'hyper-v', 'loopback', 'tunneling', 'vbox', 'docker', 'bluetooth']
        for iface, addrs in psutil.net_if_addrs().items():
            # 过滤虚拟网卡
            if any(vm in iface.lower() for vm in vm_keywords):
                continue
            # 只保留已启用的网卡
            if iface not in stats or not stats[iface].isup:
                continue
            for addr in addrs:
                if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                    if addr.address not in ip_list:
                        ip_list.append(addr.address)
        # 兜底：如果没有获取到，返回127.0.0.1
        if not ip_list:
            ip_list = ['127.0.0.1']
        return jsonify({'success': True, 'data': ip_list})
    except Exception as e:
        logger.error(f"获取本机IP失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/settings/get-nav-settings', methods=['POST'])
def api_settings_get_nav():
    """获取导航栏显示设置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 从数据库获取导航栏设置
        cursor.execute('SELECT setting_key, setting_value FROM navigation_settings')
        settings = cursor.fetchall()
        
        visible_items = {}
        item_modes = {}
        item_progress = {}
        
        for setting in settings:
            key = setting['setting_key']
            value = setting['setting_value']
            
            # 区分可见性设置、模式设置和进度设置
            if key.endswith('_mode'):
                # 模式设置
                item_key = key.replace('_mode', '')
                item_modes[item_key] = value
            elif key.endswith('_progress'):
                # 进度设置
                item_key = key.replace('_progress', '')
                try:
                    item_progress[item_key] = int(value)
                except ValueError:
                    item_progress[item_key] = 75  # 默认进度
            else:
                # 可见性设置
                visible_items[key] = value.lower() == 'true'
        
        # 确保所有必要的设置项都存在
        required_keys = ['home', 'tools', 'market', 'quality', 'coordinate', 'cad2gis', 'requirement', 'layerPreview', 'userManagement', 'settings', 'about']
        for key in required_keys:
            if key not in visible_items:
                visible_items[key] = True  # 默认显示
            if key not in item_modes:
                item_modes[key] = 'production'  # 默认生产模式
            if key not in item_progress:
                item_progress[key] = 75  # 默认进度
        
        # 确保用户管理和设置始终为true和生产模式，进度为100%
        visible_items['userManagement'] = True
        visible_items['settings'] = True
        item_modes['userManagement'] = 'production'
        item_modes['settings'] = 'production'
        item_progress['userManagement'] = 100
        item_progress['settings'] = 100
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'visibleItems': visible_items,
                'itemModes': item_modes,
                'itemProgress': item_progress
            }
        })
    except Exception as e:
        logger.error(f"获取导航栏设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取导航栏设置失败'
        })

@app.route('/api/settings/set-nav-settings', methods=['POST'])
def api_settings_set_nav():
    """保存导航栏显示设置"""
    try:
        data = request.get_json()
        visible_items = data.get('visibleItems', {})
        item_modes = data.get('itemModes', {})
        item_progress = data.get('itemProgress', {})
        username = request.headers.get('X-Username', 'unknown')
        
        # 确保用户管理和设置始终为true和生产模式，进度为100%
        visible_items['userManagement'] = True
        visible_items['settings'] = True
        item_modes['userManagement'] = 'production'
        item_modes['settings'] = 'production'
        item_progress['userManagement'] = 100
        item_progress['settings'] = 100
        
        # 验证数据格式
        required_keys = ['home', 'tools', 'market', 'quality', 'coordinate', 'cad2gis', 'requirement', 'layerPreview', 'userManagement', 'settings', 'about']
        for key in required_keys:
            if key not in visible_items:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要的可见性设置项: {key}'
                })
            if key not in item_modes:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要的模式设置项: {key}'
                })
            if key not in item_progress:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要的进度设置项: {key}'
                })
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 保存可见性设置到数据库
        for key, value in visible_items.items():
            cursor.execute('''
                INSERT OR REPLACE INTO navigation_settings (setting_key, setting_value, updated_by, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, str(value).lower(), username))
        
        # 保存模式设置到数据库
        for key, value in item_modes.items():
            cursor.execute('''
                INSERT OR REPLACE INTO navigation_settings (setting_key, setting_value, updated_by, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (f'{key}_mode', value, username))
        
        # 保存进度设置到数据库
        for key, value in item_progress.items():
            cursor.execute('''
                INSERT OR REPLACE INTO navigation_settings (setting_key, setting_value, updated_by, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (f'{key}_progress', str(value), username))
        
        conn.commit()
        conn.close()
        
        logger.info(f"导航栏设置已保存: visible_items={visible_items}, item_modes={item_modes}, item_progress={item_progress}, 操作者: {username}")
        
        return jsonify({
            'success': True,
            'message': '导航栏设置已保存'
        })
    except Exception as e:
        logger.error(f"保存导航栏设置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '保存导航栏设置失败'
        })

@app.route('/api/settings/get', methods=['POST'])
def api_settings_get():
    """获取服务器配置设置"""
    try:
        config_path = os.path.join(current_dir, 'config.xml')
        tree = ET.parse(config_path)
        root = tree.getroot()

        # 读取当前配置
        server_host = root.find('Server/Host').text
        server_port = int(root.find('Server/Port').text)
        limits = int(root.find('limits').text)

        # 判断运行模式
        mode = 'offline' if server_host == '127.0.0.1' else 'online'

        return jsonify({
            'success': True,
            'data': {
                'server_host': server_host,
                'server_port': server_port,
                'limits': limits,
                'mode': mode
            }
        })
    except Exception as e:
        logger.error(f"获取服务器配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取服务器配置失败'
        })

@app.route('/api/settings/set', methods=['POST'])
def api_settings_set():
    """保存服务器配置设置"""
    try:
        data = request.get_json()
        server_host = data.get('server_host', '127.0.0.1')
        server_port = data.get('server_port', 9997)
        limits = data.get('limits', 3)
        username = request.headers.get('X-Username', 'unknown')

        config_path = os.path.join(current_dir, 'config.xml')
        tree = ET.parse(config_path)
        root = tree.getroot()

        # 更新配置
        root.find('Server/Host').text = server_host
        root.find('Server/Port').text = str(server_port)
        root.find('limits').text = str(limits)

        # 保存到文件
        tree.write(config_path, encoding='utf-8', xml_declaration=True)

        logger.info(f"服务器配置已保存: host={server_host}, port={server_port}, limits={limits}, 操作者: {username}")

        return jsonify({
            'success': True,
            'message': '服务器配置已保存'
        })
    except Exception as e:
        logger.error(f"保存服务器配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '保存服务器配置失败'
        })

@app.route('/api/settings/restart', methods=['POST'])
def api_settings_restart():
    """
    重启后端服务。开发环境重启app.py，生产环境重启exe（路径从config.xml读取FmeHome）。重启前检测redis任务队列。
    前端可传force参数，true时无论有无任务都强制重启。
    """
    try:
        import sys
        import threading
        import time
        import socket
        force = request.json.get('force', False)
        # 检查redis队列
        queue_count = 0
        if redis_manager.is_connected():
            queue_count = redis_manager.get_queue_length()
        if queue_count > 0 and not force:
            return jsonify({'success': False, 'needForce': True, 'message': f'当前有{queue_count}个任务正在进行，是否强制重启？'})
        # 定义端口检测函数
        def is_port_free(port):
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            try:
                s.bind(("0.0.0.0", port))
                s.close()
                return True
            except OSError:
                return False
        # 延迟执行重启，先返回响应
        def do_restart():
            time.sleep(1)
            cleanup()  # 清理任务和资源
            is_frozen = getattr(sys, 'frozen', False)
            if not is_frozen and sys.argv[0].endswith('.py'):
                # 开发环境，重启app.py
                py_path = sys.executable  # python解释器路径
                app_path = os.path.abspath(sys.argv[0])
                logger.info(f"[重启] 开发环境重启app.py: {py_path} {app_path}")
                subprocess.Popen([py_path, app_path], creationflags=subprocess.CREATE_NEW_CONSOLE)
                os._exit(0)
            else:
                # 生产环境，重启exe
                config_path = os.path.join(current_dir, 'config.xml')
                tree = ET.parse(config_path)
                root = tree.getroot()
                exe_path = root.findtext('FmeHome', None)
                port_str = root.findtext('Server/Port', '9997')
                try:
                    port = int(port_str)
                except Exception:
                    port = 9997
                if exe_path and os.path.exists(exe_path):
                    exe_name = os.path.basename(exe_path)
                    logger.info(f"[重启] 终止旧进程: {exe_name}")
                    for proc in psutil.process_iter(['pid', 'name']):
                        try:
                            if proc.info['name'].lower() == exe_name.lower():
                                logger.info(f"[重启] 终止进程PID={proc.info['pid']}")
                                proc.terminate()
                                proc.wait(timeout=5)
                        except Exception as e:
                            logger.warning(f"[重启] 终止进程异常: {e}")
                    # 循环检测端口是否空闲，最多等10秒
                    for i in range(10):
                        if is_port_free(port):
                            logger.info(f"[重启] 端口{port}已释放，准备启动新进程")
                            break
                        logger.info(f"[重启] 端口{port}仍被占用，等待1秒（{i+1}/10）")
                        time.sleep(1)
                    else:
                        logger.error(f"[重启] 端口{port}在重启后依然被占用，放弃启动新进程")
                        return
                    logger.info(f"[重启] 启动新exe: {exe_path}")
                    try:
                        subprocess.Popen([exe_path], creationflags=subprocess.CREATE_NEW_CONSOLE)
                        logger.info(f"[重启] 新进程已启动: {exe_path}")
                    except Exception as e:
                        logger.error(f"[重启] 启动新进程失败: {e}")
        threading.Thread(target=do_restart).start()
        return jsonify({'success': True, 'message': '重启命令已下发'})
    except Exception as e:
        logger.error(f"重启服务器失败: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/tools/delete_result', methods=['POST'])
def delete_result():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({
                'success': False,
                'message': '任务ID不能为空'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取任务状态
        cursor.execute('''
            SELECT status FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        task = cursor.fetchone()
        
        if not task:
            conn.close()
            return jsonify({
                'success': False,
                'message': '任务不存在'
            })
            
        # 如果任务正在运行，需要终止FME进程
        if task['status'] == 'running':
            # 查找并终止对应的FME进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'fme' in proc.info['name'].lower():
                        # 检查命令行参数中是否包含task_id
                        cmdline = proc.info['cmdline']
                        if cmdline and any(task_id in arg for arg in cmdline):
                            logger.info(f"终止FME进程: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=5)  # 等待进程终止
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
            # 更新任务状态为已终止
            cursor.execute('''
                UPDATE task_records 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            cursor.execute('''
                UPDATE task_records_all 
                SET status = 'terminated',
                    error_message = '用户手动终止任务'
                WHERE task_id = ?
            ''', (task_id,))
            
            # 从Redis中删除任务
            if redis_manager.is_connected():
                redis_manager.delete_task(task_id)
        
        # 从task_records表中删除记录
        cursor.execute('''
            DELETE FROM task_records 
            WHERE task_id = ?
        ''', (task_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        logger.error(f'删除运行记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        })

@app.route('/api/task-records/update', methods=['POST'])
def update_task_record():
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        status = data.get('status')
        time_consuming = data.get('time_consuming')
        file_size = data.get('file_size')
        file_name = data.get('file_name')
        error_message = data.get('error_message')
        
        if not task_id or not status:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建更新语句
        update_fields = ['status = ?']
        update_values = [status]
        
        if time_consuming is not None:
            update_fields.append('time_consuming = ?')
            update_values.append(time_consuming)
        
        
        if file_size is not None:
            update_fields.append('file_size = ?')
            update_values.append(file_size)
        
        if file_name is not None:
            update_fields.append('file_name = ?')
            update_values.append(file_name)
        
        if error_message is not None:
            update_fields.append('error_message = ?')
            update_values.append(error_message)
        
        # 添加task_id到更新值列表
        update_values.append(task_id)
        
        # 更新task_records表
        cursor.execute(f'''
            UPDATE task_records 
            SET {', '.join(update_fields)}
            WHERE task_id = ?
        ''', update_values)
        
        # 更新task_records_all表
        cursor.execute(f'''
            UPDATE task_records_all 
            SET {', '.join(update_fields)}
            WHERE task_id = ?
        ''', update_values)
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '任务记录更新成功'
        })
        
    except Exception as e:
        logger.error(f"更新任务记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新任务记录失败: {str(e)}'
        }), 500




def process_task_queue():
    """
    处理任务队列中的任务
    使用Redis作为消息队列，根据配置的limit参数控制同时运行的最大任务数
    当工具正在运行时，创建临时副本执行任务
    """
    global running_processes
    # 任务超时时间（秒）
    TASK_TIMEOUT = 360000  # 100小时
    
    while True:
        try:
            if not redis_manager.is_connected():
                logger.error("Redis未连接，无法处理任务队列")
                time.sleep(5)
                continue

            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查已完成的进程和超时的进程
            completed_tasks = []
            for task_id, process_info in list(running_processes.items()):
                process = process_info['process']
                start_time = process_info['start_time']
                current_time = datetime.now()
                
                # 检查进程是否结束
                if process.poll() is not None:
                    completed_tasks.append((task_id, process_info))
                    del running_processes[task_id]
                    # 更新Redis中的任务状态
                    redis_manager.update_task_status(task_id, 'completed')
                    redis_manager.delete_task(task_id)
                # 检查是否超时
                elif (current_time - start_time).total_seconds() > TASK_TIMEOUT:
                    logger.warning(f"任务 {task_id} 执行超时，强制终止")
                    try:
                        process.terminate()
                        process.wait(timeout=5)  # 等待进程终止
                    except subprocess.TimeoutExpired:
                        process.kill()  # 如果进程没有及时终止，强制结束
                    
                    # 更新任务状态为超时
                    cursor.execute('''
                    UPDATE task_records 
                    SET status = 'timeout',
                        error_message = '任务执行超时'
                    WHERE task_id = ?
                    ''', (task_id,))
                    
                    cursor.execute('''
                    UPDATE task_records_all 
                    SET status = 'timeout',
                        error_message = '任务执行超时'
                    WHERE task_id = ?
                    ''', (task_id,))
                    
                    # 更新Redis中的任务状态
                    redis_manager.update_task_status(task_id, 'timeout')
                    redis_manager.delete_task(task_id)
                    conn.commit()
                    
                    completed_tasks.append((task_id, process_info))
                    del running_processes[task_id]
            
            # 处理已完成的进程
            for task_id, process_info in completed_tasks:
                process = process_info['process']
                args = process_info['args']
                start_time = process_info['start_time']
                
                try:
                    stdout, stderr = process.communicate(timeout=5)
                    end_time = datetime.now()
                    time_consuming = int((end_time - start_time).total_seconds())
                    
                    if process.returncode == 0:
                        # 生成压缩包名称（使用工具名称和当前时间）
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        zip_filename = f"{args['fmw_name']}_{timestamp}.zip"
                        
                        # 根据任务类型选择正确的输出目录
                        if args['fmw_id'] == 'cad2gis':
                            # CAD转GIS任务的输出目录
                            output_dir = os.path.join(current_dir, 'tools', 'cad2gis', 'output', task_id)
                            zip_dir = os.path.join(current_dir, 'tools', 'cad2gis', 'output', task_id)
                        elif args['fmw_id'] == 'quality':
                            # 质量检查任务的输出目录
                            output_dir = os.path.join(current_dir, 'tools', 'quality', 'output', task_id)
                            zip_dir = os.path.join(current_dir, 'tools', 'quality', 'output', task_id)
                        elif args['fmw_id'] == 'coordinatetransformation':
                            # 坐标转换任务的输出目录
                            output_dir = os.path.join(current_dir, 'tools', 'coordinatetransformation', 'output', task_id)
                            zip_dir = os.path.join(current_dir, 'tools', 'coordinatetransformation', 'output', task_id)
                        else:
                            # 其他任务的输出目录
                            output_dir = os.path.join(current_dir, 'models', args['fmw_id'], 'output', task_id)
                            zip_dir = os.path.join(current_dir, 'models',args['fmw_id'], 'output', task_id)
                            
                        # 确保输出目录存在
                        if not os.path.exists(output_dir):
                            os.makedirs(output_dir)
                        if not os.path.exists(zip_dir):
                            os.makedirs(zip_dir)
                            
                        zip_path = os.path.join(zip_dir, zip_filename)
                        
                        # 创建压缩包
                        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                            for root, dirs, files in os.walk(output_dir):
                                for file in files:
                                    if file != zip_filename:  # 排除压缩包本身
                                        file_path = os.path.join(root, file)
                                        arcname = os.path.relpath(file_path, output_dir)
                                        zipf.write(file_path, arcname)
                        
                        # 获取压缩包大小（以MB为单位，保留3位小数）
                        zip_size = round(os.path.getsize(zip_path) / (1024 * 1024), 4)
                        
                        try:
                            # 开始事务
                            cursor.execute('BEGIN TRANSACTION')
                            
                            # 更新model_market表中的run_times
                            cursor.execute('''
                                UPDATE model_market 
                                SET run_times = COALESCE(run_times, 0) + 1 
                                WHERE fmw_id = ?
                            ''', (args['fmw_id'],))
                            
                            # 更新task_records和task_records_all表中的记录
                            cursor.execute('''
                            UPDATE task_records 
                            SET status = 'success',
                                time_consuming = ?,
                                file_size = ?,
                                file_name = ?
                            WHERE task_id = ?
                            ''', (time_consuming, f"{zip_size}MB", zip_filename, task_id))
                            
                            cursor.execute('''
                            UPDATE task_records_all 
                            SET status = 'success',
                                time_consuming = ?,
                                file_size = ?,
                                file_name = ?
                            WHERE task_id = ?
                            ''', (time_consuming, f"{zip_size}MB", zip_filename, task_id))
                            
                            # 从Redis中删除任务
                            redis_manager.update_task_status(task_id, 'success')
                            redis_manager.delete_task(task_id)
                            
                            # 如果是临时工具，删除临时文件
                            if args.get('is_temp_tool'):
                                temp_fmw_path = args['temp_fmw_path']
                                cleanup_temp_fmw_copy(temp_fmw_path)
                            
                            # 提交事务
                            conn.commit()
                            logger.info(f"任务 {task_id} 执行成功")
                            
                        except Exception as e:
                            # 如果发生错误，回滚事务
                            conn.rollback()
                            logger.error(f"更新数据库失败: {str(e)}")
                            raise
                    
                    else:
                        # 获取FME日志文件路径
                        fmw_path = args['cmd'][1]  # 获取FMW文件路径
                        log_path = os.path.splitext(fmw_path)[0] + '.log'  # 构建日志文件路径
                        
                        # 读取FME日志文件
                        error_message = "未知错误"
                        if os.path.exists(log_path):
                            try:
                                with open(log_path, 'r', encoding='utf-8') as f:
                                    error_message = f.read()
                            except Exception as e:
                                logger.error(f"读取FME日志文件失败: {str(e)}")
                                error_message = stderr.decode() if stderr else "未知错误"
                        else:
                            error_message = stderr.decode() if stderr else "未知错误"
                        
                        logger.error(f"任务 {task_id} 执行失败: {error_message}")
                        
                        # 更新task_records和task_records_all表中的记录
                        cursor.execute('''
                        UPDATE task_records 
                        SET status = 'failed',
                            error_message = ?
                        WHERE task_id = ?
                        ''', (error_message, task_id))
                        
                        cursor.execute('''
                        UPDATE task_records_all 
                        SET status = 'failed',
                            error_message = ?
                        WHERE task_id = ?
                        ''', (error_message, task_id))
                        
                        # 更新Redis中的任务状态
                        redis_manager.update_task_status(task_id, 'failed')
                        redis_manager.delete_task(task_id)
                        
                        conn.commit()
                        
                except subprocess.TimeoutExpired:
                    logger.error(f"任务 {task_id} 通信超时")
                    process.kill()
                    cursor.execute('''
                    UPDATE task_records 
                    SET status = 'failed',
                        error_message = '进程通信超时'
                    WHERE task_id = ?
                    ''', (task_id,))
                    
                    cursor.execute('''
                    UPDATE task_records_all 
                    SET status = 'failed',
                        error_message = '进程通信超时'
                    WHERE task_id = ?
                    ''', (task_id,))
                    
                    # 更新Redis中的任务状态
                    redis_manager.update_task_status(task_id, 'failed')
                    redis_manager.delete_task(task_id)
                    
                    # 如果是临时工具，删除临时文件
                    if args.get('is_temp_tool'):
                        temp_fmw_path = args['temp_fmw_path']
                        if os.path.exists(temp_fmw_path):
                            os.remove(temp_fmw_path)
                            logger.info(f"删除临时工具文件: {temp_fmw_path}")
                        
                        conn.commit()
            
            # 获取当前运行中的任务数量
            running_count = len(running_processes)
            
            # 如果运行中的任务数小于限制，则获取新的任务
            if running_count < config['limit']:
                # 计算还可以启动多少个新任务
                available_slots = config['limit'] - running_count
                
                # 从Redis队列中获取待处理的任务
                for _ in range(available_slots):
                    # 从Redis队列中获取一个任务
                    task_data = redis_manager.get_task()
                    if not task_data:
                        break
                        
                    task_id = task_data['task_id']
                    args = task_data['args']
                    fmw_id = args['fmw_id']
                    
                    # 检查该工具是否已有正在运行的任务
                    tool_running = False
                    for running_task in running_processes.values():
                        if running_task['args']['fmw_id'] == fmw_id:
                            tool_running = True
                            logger.info(f"工具 {fmw_id} 已有任务正在运行，创建临时副本")
                            
                            # 创建临时工具副本
                            try:
                                # 获取原始FMW文件路径
                                found = False
                                for table in [
                                    'model_market',
                                    'cad2gis_model_market',
                                    'quality_model_market',
                                    'coordinatetransformation_model_market'
                                ]:
                                    cursor.execute(f'SELECT fmw_path FROM {table} WHERE fmw_id = ?', (fmw_id,))
                                    result = cursor.fetchone()
                                    if result:
                                        found = True
                                        break
                                if not found:
                                    raise Exception(f"未找到工具 {fmw_id} 的文件路径")
                                
                                # 打印原始FMW路径
                                logger.info(f"原始FMW路径: {result[0]}")
                                
                                # 拼接完整路径
                                original_fmw_path = os.path.normpath(os.path.join(current_dir, result[0]))
                                logger.info(f"完整原始FMW路径: {original_fmw_path}")
                                
                                # 检查路径是否存在
                                if not os.path.exists(original_fmw_path):
                                    raise Exception(f"原始FMW文件不存在: {original_fmw_path}")
                                
                                # 创建临时FMW副本
                                temp_fmw_path = create_temp_fmw_copy(original_fmw_path, task_id)
                                
                                # 打印新FMW路径
                                logger.info(f"新FMW路径: {temp_fmw_path}")
                                
                                # 更新任务参数
                                args['cmd'][1] = temp_fmw_path  # 更新FME命令中的FMW路径
                                args['is_temp_tool'] = True
                                args['temp_fmw_path'] = temp_fmw_path
                                
                                tool_running = False  # 允许继续处理任务
                                
                            except Exception as e:
                                logger.error(f"创建临时工具副本失败: {str(e)}")
                                # 如果创建临时副本失败，将任务重新放回队列
                                redis_manager.add_task(task_id, task_data)
                                continue
                            
                            break
                    
                    if tool_running:
                        # 如果工具已有任务在运行且创建临时副本失败，将当前任务重新放回队列
                        redis_manager.add_task(task_id, task_data)
                        continue
                    
                    logger.info(f"\n=== 开始处理任务 {task_id} ===")
                    
                    try:
                        # 更新任务记录状态为running
                        cursor.execute('''
                        UPDATE task_records 
                        SET status = 'running'
                        WHERE task_id = ?
                        ''', (task_id,))
                        
                        cursor.execute('''
                        UPDATE task_records_all 
                        SET status = 'running'
                        WHERE task_id = ?
                        ''', (task_id,))
                        
                        conn.commit()
                        
                        # 更新Redis中的任务状态
                        redis_manager.update_task_status(task_id, 'running')
                        
                        # 记录开始时间
                        start_time = datetime.now()
                        
                        # 运行FME（非阻塞方式）
                        # 根据fme_visibility参数决定是否隐藏窗口
                        creationflags = subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.CREATE_NEW_CONSOLE
                        if sys.platform == 'win32' and config_cache.get('fme_visibility', False):
                            # 隐藏窗口
                            creationflags = subprocess.CREATE_NEW_PROCESS_GROUP | 0x08000000  # CREATE_NO_WINDOW
                        process = subprocess.Popen(
                            args['cmd'],
                            stdout=None,  # 改为None，直接输出到控制台
                            stderr=None,  # 改为None，直接输出到控制台
                            creationflags=creationflags
                        )
                        
                        # 存储进程信息
                        running_processes[task_id] = {
                            'process': process,
                            'args': args,
                            'start_time': start_time
                        }
                        
                    except Exception as e:
                        logger.error(f"启动任务 {task_id} 时出错: {str(e)}")
                        
                        # 更新task_records和task_records_all表中的记录
                        cursor.execute('''
                        UPDATE task_records 
                        SET status = 'failed',
                            error_message = ?
                        WHERE task_id = ?
                        ''', (str(e), task_id))
                        
                        cursor.execute('''
                        UPDATE task_records_all 
                        SET status = 'failed',
                            error_message = ?
                        WHERE task_id = ?
                        ''', (str(e), task_id))
                        
                        conn.commit()
                        
                        # 从Redis中删除任务
                        redis_manager.delete_task(task_id)
                        
                        # 如果是临时工具，删除临时文件
                        if args.get('is_temp_tool'):
                            temp_fmw_path = args['temp_fmw_path']
                            if os.path.exists(temp_fmw_path):
                                os.remove(temp_fmw_path)
                                logger.info(f"删除临时工具文件: {temp_fmw_path}")
            
            conn.close()
            
            # 等待一段时间再检查下一个任务
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"处理任务队列时出错: {str(e)}")
            time.sleep(5)  # 发生错误时等待较长时间

def create_temp_fmw_copy(original_fmw_path, task_id):
    """
    创建FMW文件的临时副本，放在与原文件相同的目录
    
    Args:
        original_fmw_path: 原始FMW文件路径
        task_id: 任务ID
        
    Returns:
        temp_fmw_path: 临时FMW文件路径
    """
    try:
        # 获取原文件所在目录和文件名
        original_dir = os.path.dirname(original_fmw_path)
        original_name = os.path.basename(original_fmw_path)
        name_without_ext = os.path.splitext(original_name)[0]
        
        # 创建临时FMW副本，放在与原文件相同的目录
        temp_fmw_name = f"{name_without_ext}_task_{task_id}.fmw"
        temp_fmw_path = os.path.join(original_dir, temp_fmw_name)
        
        logger.info(f"创建临时FMW副本: {original_fmw_path} -> {temp_fmw_path}")
        shutil.copy2(original_fmw_path, temp_fmw_path)
        
        return temp_fmw_path
    except Exception as e:
        logger.error(f"创建临时FMW副本失败: {str(e)}")
        raise

def get_fmw_path_by_fmw_id(fmw_id, model_id, conn):
    """
    根据fmw_id和model_id从对应的表中查找fmw_path
    :param fmw_id: 工具类型标识，如'cad2gis'、'coordinatetransformation'、'quality'等
    :param model_id: 具体模型的id
    :param conn: sqlite3数据库连接
    :return: fmw_path字符串，未找到则返回None
    """
    # 工具类型与表名映射
    table_map = {
        'cad2gis': 'cad2gis_model_market',
        'coordinatetransformation': 'coordinatetransformation_model_market',
        'quality': 'quality_model_market'
    }
    table_name = table_map.get(fmw_id, 'model_market')
    sql = f"SELECT fmw_path FROM {table_name} WHERE id = ?"
    cursor = conn.cursor()
    cursor.execute(sql, (model_id,))
    row = cursor.fetchone()
    return row[0] if row else None


def cleanup_temp_fmw_copy(temp_fmw_path):
    """
    清理临时FMW文件副本
    
    Args:
        temp_fmw_path: 临时FMW文件路径
    """
    try:
        if temp_fmw_path and os.path.exists(temp_fmw_path):
            os.remove(temp_fmw_path)
            logger.info(f"删除临时FMW文件: {temp_fmw_path}")
    except Exception as e:
        logger.warning(f"删除临时FMW文件失败: {temp_fmw_path}, 错误: {e}")

def cleanup_expired_tasks():
    """清理过期的任务状态记录"""
    global running_processes
    try:
        if not redis_manager.is_connected():
            return
            
        task_status_keys = redis_manager.client.keys('task_status:*')
        for key in task_status_keys:
            # 检查任务是否已经完成或失败
            task_id = key.decode('utf-8').split(':')[1] if isinstance(key, bytes) else key.split(':')[1]
            if task_id not in running_processes:
                redis_manager.delete_task(task_id)
                logger.info(f"清理过期任务: {task_id}")
    except Exception as e:
        logger.error(f"清理过期任务失败: {str(e)}")

@app.route('/api/task/status/count', methods=['GET'])
def get_task_status_count():
    """获取任务状态数量统计"""
    global running_processes
    try:
        logger.info("\n=== 开始获取任务状态数量 ===")
        
        if not redis_manager.is_connected():
            logger.error("Redis未连接")
            return jsonify({
                'success': False,
                'message': 'Redis未连接'
            })
        
        # 清理过期任务
        cleanup_expired_tasks()
        
        # 获取当前队列数量
        current_queue_count = redis_manager.get_queue_length()
        logger.info(f"当前队列数量: {current_queue_count}")
        
        # 获取实际运行中的任务数量
        running_count = len(running_processes)
        logger.info(f"实际运行中的任务数量: {running_count}")
        
        # 获取等待中的任务数量（队列中的任务）
        pending_count = current_queue_count
        
        logger.info(f"统计结果 - 队列数量: {current_queue_count}, 运行中: {running_count}, 等待中: {pending_count}")
        
        return jsonify({
            'success': True,
            'data': {
                'current_queue_count': current_queue_count,
                'running_count': running_count,
                'pending_count': pending_count
            }
        })
        
    except Exception as e:
        logger.error(f"获取任务状态数量时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取任务状态数量失败: {str(e)}'
        })

@app.route('/api/task/current/list', methods=['POST'])
def get_current_tasks():
    """获取当前进行中和等待中的任务列表"""
    global running_processes
    try:
        logger.info("\n=== 开始获取当前任务列表 ===")
        
        if not redis_manager.is_connected():
            logger.error("Redis未连接")
            return jsonify({
                'success': False,
                'message': 'Redis未连接'
            })
        
        # 清理过期任务
        cleanup_expired_tasks()
        
        # 获取当前队列中的任务
        queue_tasks = redis_manager.get_queue_tasks()
        logger.info(f"队列中的任务: {queue_tasks}")
        
        # 获取运行中的任务
        running_tasks = []
        for task_id, process_info in running_processes.items():
            if isinstance(process_info, dict):
                running_tasks.append({
                    'task_id': task_id,
                    'status': 'running',
                    'submit_time': process_info.get('submit_time', ''),
                    'tool_name': process_info.get('tool_name', ''),
                    'submitter': process_info.get('submitter', '')
                })
        
        # 构建等待中的任务列表
        pending_tasks = []
        for task_data in queue_tasks:
            if isinstance(task_data, dict):
                pending_tasks.append({
                    'task_id': task_data.get('task_id', ''),
                    'status': 'pending',
                    'submit_time': task_data.get('submit_time', ''),
                    'tool_name': task_data.get('tool_name', ''),
                    'submitter': task_data.get('submitter', '')
                })
        
        # 合并所有任务
        all_tasks = running_tasks + pending_tasks
        
        # 从数据库获取任务的详细信息
        conn = get_db_connection()
        cursor = conn.cursor()
        
        enhanced_tasks = []
        for task in all_tasks:
            task_id = task.get('task_id')
            if task_id:
                # 从task_records_all表获取详细信息
                cursor.execute('''
                    SELECT tool_name, submitter, submit_time, project, file_name, up_nums
                    FROM task_records_all 
                    WHERE task_id = ?
                    ORDER BY id DESC
                    LIMIT 1
                ''', (task_id,))
                
                db_record = cursor.fetchone()
                if db_record:
                    tool_name, submitter, submit_time, project, file_name, up_nums = db_record
                    enhanced_task = {
                        'task_id': task_id,
                        'status': task.get('status', ''),
                        'tool_name': tool_name or '未知工具',
                        'submitter': submitter or '未知用户',
                        'submit_time': submit_time or task.get('submit_time', ''),
                        'project': project or '',
                        'file_name': file_name or '',
                        'up_nums': up_nums or 1
                    }
                else:
                    # 如果数据库中没有记录，使用Redis中的数据
                    enhanced_task = {
                        'task_id': task_id,
                        'status': task.get('status', ''),
                        'tool_name': task.get('tool_name', '未知工具'),
                        'submitter': task.get('submitter', '未知用户'),
                        'submit_time': task.get('submit_time', ''),
                        'project': '',
                        'file_name': '',
                        'up_nums': 1
                    }
                enhanced_tasks.append(enhanced_task)
        
        cursor.close()
        conn.close()
        
        logger.info(f"获取到 {len(enhanced_tasks)} 个任务")
        logger.info(f"运行中: {len(running_tasks)}, 等待中: {len(pending_tasks)}")
        
        return jsonify({
            'success': True,
            'data': {
                'tasks': enhanced_tasks,
                'running_count': len(running_tasks),
                'pending_count': len(pending_tasks)
            }
        })
        
    except Exception as e:
        logger.error(f"获取当前任务列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取当前任务列表失败: {str(e)}'
        })

@app.route('/api/task/export-excel', methods=['POST'])
def export_task_records_excel():
    """导出任务记录为Excel文件"""
    conn = None
    cursor = None
    wb = None
    
    try:
        data = request.get_json()
        table_name = data.get('table_name', 'task_records_all')  # 默认导出task_records_all表
        date_range = data.get('date_range', None)  # 日期范围过滤
        
        logger.info(f"\n=== 开始导出任务记录 ===")
        logger.info(f"表名: {table_name}")
        logger.info(f"日期范围: {date_range}")
        
        # 验证表名
        valid_tables = ['task_records', 'task_records_all', 'cad2gis_run_records']
        if table_name not in valid_tables:
            return jsonify({
                'success': False,
                'message': '无效的表名'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 构建查询SQL
        if table_name == 'task_records':
            query = '''
                SELECT 
                    task_id, tool_name, project, submitter, submit_time, 
                    task_name, status, time_consuming, file_size, file_name, up_nums
                FROM task_records
            '''
        elif table_name == 'task_records_all':
            query = '''
                SELECT 
                    task_id, tool_name, project, submitter, submit_time, 
                    task_name, status, time_consuming, file_size, file_name, up_nums
                FROM task_records_all
            '''
        elif table_name == 'cad2gis_run_records':
            query = '''
                SELECT 
                    task_id, fmw_name as tool_name, '' as project, username as submitter, 
                    submit_time, fmw_name as task_name, status, time_consuming, 
                    file_size, file_name, 1 as up_nums
                FROM cad2gis_run_records
            '''
        
        # 添加日期范围过滤
        if date_range and len(date_range) == 2:
            start_date, end_date = date_range
            query += f" WHERE submit_time BETWEEN '{start_date}' AND '{end_date}'"
        
        query += " ORDER BY submit_time DESC"
        
        cursor.execute(query)
        records = cursor.fetchall()
        
        if not records:
            cursor.close()
            conn.close()
            return jsonify({
                'success': False,
                'message': '没有找到符合条件的记录'
            }), 404
        
        # 生成Excel文件

        
        # 定义列名
        columns = [
            '任务ID', '工具名称', '项目', '提交者', '提交时间', 
            '任务名称', '状态', '运行时间(秒)', '文件大小', '文件名', '文件数量'
        ]
        
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "任务记录"
        
        # 设置表头
        header_font = Font(bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')
        
        for col, header in enumerate(columns, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.alignment = header_alignment
        
        # 写入数据
        for row, record in enumerate(records, 2):
            for col, value in enumerate(record, 1):
                ws.cell(row=row, column=col, value=value)
        
        # 调整列宽
        column_widths = [20, 15, 15, 12, 20, 15, 10, 12, 12, 20, 10]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
        
        # 创建临时文件
        tmp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                tmp_file_path = tmp_file.name
                wb.save(tmp_file_path)
            
            # 确保工作簿被关闭
            wb.close()
            
            # 等待一小段时间确保文件写入完成
            import time
            time.sleep(0.1)
            
            # 读取文件内容
            with open(tmp_file_path, 'rb') as f:
                file_content = f.read()
                
        except Exception as e:
            logger.error(f"处理临时文件时出错: {str(e)}")
            raise
        finally:
            # 确保临时文件被删除
            if tmp_file_path and os.path.exists(tmp_file_path):
                try:
                    os.unlink(tmp_file_path)
                except Exception as e:
                    logger.warning(f"删除临时文件失败: {str(e)}")
        
        # 生成文件名
        from datetime import datetime
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{table_name}_{current_time}.xlsx"
        
        logger.info(f"导出完成，共 {len(records)} 条记录")
        
        # 返回文件
        response = make_response(file_content)
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        logger.error(f"导出任务记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500
    finally:
        # 确保资源被正确释放
        if cursor:
            try:
                cursor.close()
            except Exception as e:
                logger.warning(f"关闭数据库游标失败: {str(e)}")
        
        if conn:
            try:
                conn.close()
            except Exception as e:
                logger.warning(f"关闭数据库连接失败: {str(e)}")
        
        if wb:
            try:
                wb.close()
            except Exception as e:
                logger.warning(f"关闭工作簿失败: {str(e)}")

@app.route('/api/tools/revoke-approval', methods=['POST'])
def revoke_approval():
    try:
        data = request.get_json()
        application_id = data.get('applicationId')
        
        if not application_id:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查申请是否存在且当前用户是否为工具所有者
        cursor.execute('''
        SELECT ta.id, ta.fmw_id, m.author 
        FROM tool_application ta
        JOIN model_market m ON ta.fmw_id = m.fmw_id
        WHERE ta.id = ? AND m.author = ? AND ta.status = 'approved'
        ''', (application_id, username))
        
        application = cursor.fetchone()
        
        if not application:
            conn.close()
            return jsonify({'success': False, 'message': '申请不存在或无权撤销'}), 403
            
        # 更新申请状态为已撤销
        cursor.execute('''
        UPDATE tool_application 
        SET status = 'revoked', review_time = CURRENT_TIMESTAMP, review_comment = '工具所有者撤销了使用权限'
        WHERE id = ?
        ''', (application_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '撤销成功'
        })
        
    except Exception as e:
        logger.error(f"撤销申请失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/task/records', methods=['POST'])
def get_task_records():
    """
    分页获取任务运行记录，支持表名、日期范围筛选，返回所有导出字段，严格无硬编码。
    """
    import math
    from flask import current_app
    try:
        data = request.get_json()
        table_name = data.get('table_name', 'task_records_all')
        date_range = data.get('date_range', None)
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))

        # 合法表名校验
        valid_tables = ['task_records', 'task_records_all', 'cad2gis_run_records']
        if table_name not in valid_tables:
            return jsonify({'success': False, 'message': '无效的表名'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建基础SQL
        if table_name == 'task_records':
            base_query = '''SELECT task_id, tool_name, project, submitter, submit_time, task_name, status, time_consuming, file_size, file_name, up_nums FROM task_records'''
            count_query = 'SELECT COUNT(*) FROM task_records'
        elif table_name == 'task_records_all':
            base_query = '''SELECT task_id, tool_name, project, submitter, submit_time, task_name, status, time_consuming, file_size, file_name, up_nums FROM task_records_all'''
            count_query = 'SELECT COUNT(*) FROM task_records_all'
        elif table_name == 'cad2gis_run_records':
            base_query = '''SELECT task_id, fmw_name as tool_name, '' as project, username as submitter, submit_time, fmw_name as task_name, status, time_consuming, file_size, file_name, 1 as up_nums FROM cad2gis_run_records'''
            count_query = 'SELECT COUNT(*) FROM cad2gis_run_records'

        # 构建参数和条件
        params = []
        where_clauses = []
        if date_range and isinstance(date_range, list) and len(date_range) == 2:
            where_clauses.append('submit_time BETWEEN ? AND ?')
            params.extend(date_range)

        # 拼接where条件
        if where_clauses:
            base_query += ' WHERE ' + ' AND '.join(where_clauses)
            count_query += ' WHERE ' + ' AND '.join(where_clauses)

        # 排序
        base_query += ' ORDER BY submit_time DESC'

        # 统计总数
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        total_pages = math.ceil(total / page_size) if page_size else 1
        offset = (page - 1) * page_size

        # 分页查询
        base_query += ' LIMIT ? OFFSET ?'
        params.extend([page_size, offset])
        cursor.execute(base_query, params)
        records = cursor.fetchall()

        # 字段名与导出一致
        columns = [
            '任务ID', '工具名称', '项目', '提交者', '提交时间',
            '任务名称', '状态', '运行时间(秒)', '文件大小', '文件名', '文件数量'
        ]
        # 转为字典列表
        result = []
        for row in records:
            result.append({
                'task_id': row[0],
                'tool_name': row[1],
                'project': row[2],
                'submitter': row[3],
                'submit_time': row[4],
                'task_name': row[5],
                'status': row[6],
                'time_consuming': row[7],
                'file_size': row[8],
                'file_name': row[9],
                'up_nums': row[10]
            })

        # 关闭连接
        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'records': result,
                'pagination': {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': total_pages
                },
                'columns': columns
            }
        })
    except Exception as e:
        # 中文注释：捕获异常并返回错误信息
        current_app.logger.error(f'获取任务记录分页失败: {str(e)}')
        return jsonify({'success': False, 'message': f'获取任务记录失败: {str(e)}'}), 500


@app.route('/api/tools/delete-application', methods=['POST'])
def delete_application():
    try:
        data = request.get_json()
        application_id = data.get('applicationId')
        
        if not application_id:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查申请是否存在且属于当前用户且状态为已通过
        cursor.execute('''
        SELECT id FROM tool_application 
        WHERE id = ? AND applicant = ? AND status = 'approved'
        ''', (application_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '申请不存在或无权删除'}), 403
            
        # 删除申请记录
        cursor.execute('DELETE FROM tool_application WHERE id = ?', (application_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除申请失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/withdraw-application', methods=['POST'])
def withdraw_application():
    try:
        data = request.get_json()
        application_id = data.get('applicationId')
        
        if not application_id:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查申请是否存在且属于当前用户且状态为待审批
        cursor.execute('''
        SELECT id FROM tool_application 
        WHERE id = ? AND applicant = ? AND status = 'pending'
        ''', (application_id, username))
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '申请不存在或无权撤回'}), 403
            
        # 删除申请记录
        cursor.execute('DELETE FROM tool_application WHERE id = ?', (application_id,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '撤回成功'
        })
        
    except Exception as e:
        logger.error(f"撤回申请失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/tools/resubmit-application', methods=['POST'])
def resubmit_application():
    """重新提交工具申请"""
    try:
        data = request.get_json()
        if not data or 'applicationId' not in data:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        application_id = data['applicationId']
        reason = data.get('reason', '')
        end_date = data.get('end_date', '')
        usage_count = data.get('usage_count', 1)
        user_project = data.get('user_project', '')
        
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
            
        logger.info(f"重新提交申请 - application_id: {application_id}, username: {username}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 先查询申请记录，看看是否存在
        cursor.execute('''
        SELECT id, status, applicant FROM tool_application 
        WHERE id = ?
        ''', (application_id,))
        
        application = cursor.fetchone()
        if not application:
            conn.close()
            logger.error(f"申请记录不存在 - application_id: {application_id}")
            return jsonify({'success': False, 'message': '申请记录不存在'}), 404
            
        logger.info(f"找到申请记录 - id: {application[0]}, status: {application[1]}, applicant: {application[2]}")
        
        # 检查申请是否存在且属于当前用户且状态为已驳回
        cursor.execute('''
        SELECT id FROM tool_application 
        WHERE id = ? AND applicant = ? AND status = 'rejected'
        ''', (application_id, username))
        
        if not cursor.fetchone():
            conn.close()
            logger.error(f"申请权限验证失败 - application_id: {application_id}, username: {username}")
            return jsonify({'success': False, 'message': '申请不存在或无权重新提交'}), 403
        
        # 更新申请信息
        cursor.execute('''
        UPDATE tool_application 
        SET status = 'pending',
            reason = ?,
            end_date = ?,
            usage_count = ?,
            user_project = ?,
            review_time = NULL,
            review_comment = NULL,
            count = 0
        WHERE id = ?
        ''', (reason, end_date, usage_count, user_project, application_id))
        
        conn.commit()
        conn.close()
        
        logger.info(f"申请重新提交成功 - application_id: {application_id}")
        return jsonify({'success': True, 'message': '申请已重新提交'})
        
    except Exception as e:
        logger.error(f"重新提交申请失败: {str(e)}")
        return jsonify({'success': False, 'message': f'重新提交申请失败: {str(e)}'}), 500



# 将cad2gis相关的API路由移到这里，在@app.route('/')之前
@app.route('/api/cad2gis/tools/list', methods=['POST'])
def get_cad2gis_tools_list():
    try:
        username = "everyone"
        if not username:
            return jsonify({
                'success': False,
                'message': '用户名不能为空'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具列表
        cursor.execute('''
            SELECT m.*, u.username as author_name
            FROM cad2gis_model_market m
            LEFT JOIN login_users u ON m.author = u.username
            ORDER BY m.created_at DESC
        ''')
        
        tools = cursor.fetchall()
        
        tools_list = []
        for tool in tools:
            tools_list.append({
                'id': tool[0],
                'fmw_id': tool[1],
                'fmw_name': tool[2],
                'project': tool[3],
                'description': tool[4],
                'fmw_path': tool[5],
                'run_times': tool[6],
                'created_at': tool[7],
                'author': tool[8],
                'author_name': tool[9]
            })
        
        return jsonify({
            'success': True,
            'data': tools_list
        })
    except Exception as e:
        print(f"获取cad2gis工具列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具列表失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

# 将cad2gis相关的API路由移到这里，在@app.route('/')之前
@app.route('/api/Coordinate/tools/list', methods=['POST'])
def get_Coordinate_tools_list():
    try:
        username = "everyone"
        if not username:
            return jsonify({
                'success': False,
                'message': '用户名不能为空'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具列表
        cursor.execute('''
            SELECT m.*, u.username as author_name
            FROM cad2gis_model_market m
            LEFT JOIN login_users u ON m.author = u.username
            ORDER BY m.created_at DESC
        ''')
        
        tools = cursor.fetchall()
        
        tools_list = []
        for tool in tools:
            tools_list.append({
                'id': tool[0],
                'fmw_id': tool[1],
                'fmw_name': tool[2],
                'project': tool[3],
                'description': tool[4],
                'fmw_path': tool[5],
                'run_times': tool[6],
                'created_at': tool[7],
                'author': tool[8],
                'author_name': tool[9]
            })
        
        return jsonify({
            'success': True,
            'data': tools_list
        })
    except Exception as e:
        print(f"获取cad2gis工具列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具列表失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/cad2gis/tools/count', methods=['POST'])
def get_cad2gis_tools_count():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具总数
        cursor.execute('SELECT COUNT(*) FROM cad2gis_model_market')
        tools_count = cursor.fetchone()[0]
        
        # 获取新增工具数（最近7天）
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_model_market 
            WHERE created_at >= datetime('now', '-7 days')
        ''')
        new_tools_count = cursor.fetchone()[0]
        
        # 获取总运行次数
        cursor.execute('SELECT SUM(run_times) FROM cad2gis_model_market')
        total_runs = cursor.fetchone()[0] or 0
        
        # 获取当前队列中的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'pending'
        ''')
        current_queue_count = cursor.fetchone()[0]
        
        # 获取正在运行的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'running'
        ''')
        running_count = cursor.fetchone()[0]
        
        # 获取等待中的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'pending'
        ''')
        pending_count = cursor.fetchone()[0]
        
        return jsonify({
            'success': True,
            'data': {
                'tools_count': tools_count,
                'new_tools_count': new_tools_count,
                'total_runs': total_runs,
                'current_queue_count': current_queue_count,
                'running_count': running_count,
                'pending_count': pending_count
            }
        })
    except Exception as e:
        print(f"获取cad2gis工具统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具统计失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/count', methods=['POST'])
def get_Coordinate_tools_count():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取工具总数
        cursor.execute('SELECT COUNT(*) FROM cad2gis_model_market')
        tools_count = cursor.fetchone()[0]
        
        # 获取新增工具数（最近7天）
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_model_market 
            WHERE created_at >= datetime('now', '-7 days')
        ''')
        new_tools_count = cursor.fetchone()[0]
        
        # 获取总运行次数
        cursor.execute('SELECT SUM(run_times) FROM cad2gis_model_market')
        total_runs = cursor.fetchone()[0] or 0
        
        # 获取当前队列中的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'pending'
        ''')
        current_queue_count = cursor.fetchone()[0]
        
        # 获取正在运行的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'running'
        ''')
        running_count = cursor.fetchone()[0]
        
        # 获取等待中的任务数
        cursor.execute('''
            SELECT COUNT(*) FROM cad2gis_task_records 
            WHERE status = 'pending'
        ''')
        pending_count = cursor.fetchone()[0]
        
        return jsonify({
            'success': True,
            'data': {
                'tools_count': tools_count,
                'new_tools_count': new_tools_count,
                'total_runs': total_runs,
                'current_queue_count': current_queue_count,
                'running_count': running_count,
                'pending_count': pending_count
            }
        })
    except Exception as e:
        print(f"获取cad2gis工具统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工具统计失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/cad2gis/tools/upload', methods=['POST'])
def upload_cad2gis_tool():
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        required_fields = ['fmw_id', 'fmw_name', 'project', 'description', 'fmw_path']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具ID是否已存在
        cursor.execute('SELECT id FROM cad2gis_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        if cursor.fetchone():
            return jsonify({
                'success': False,
                'message': '工具ID已存在'
            }), 400
        
        # 获取用户名
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400
        
        # 处理文件路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe
            exe_dir = os.path.dirname(sys.executable)
            fmw_path = os.path.join(exe_dir, 'tools', 'cad2gis', data['fmw_path'])
        else:
            # 如果是开发环境
            fmw_path = os.path.join('tools', 'cad2gis', data['fmw_path'])
        
        # 插入工具信息
        cursor.execute('''
            INSERT INTO cad2gis_model_market (
                fmw_id, fmw_name, project, description, fmw_path, 
                run_times, created_at, author
            ) VALUES (?, ?, ?, ?, ?, 0, datetime('now'), ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['project'],
            data['description'],
            fmw_path,
            username
        ))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '工具上传成功'
        })
    except Exception as e:
        print(f"上传cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'上传工具失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/upload', methods=['POST'])
def upload_Coordinate_tool():
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        required_fields = ['fmw_id', 'fmw_name', 'project', 'description', 'fmw_path']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具ID是否已存在
        cursor.execute('SELECT id FROM cad2gis_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        if cursor.fetchone():
            return jsonify({
                'success': False,
                'message': '工具ID已存在'
            }), 400
        
        # 获取用户名
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400
        
        # 处理文件路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe
            exe_dir = os.path.dirname(sys.executable)
            fmw_path = os.path.join(exe_dir, 'tools', 'cad2gis', data['fmw_path'])
        else:
            # 如果是开发环境
            fmw_path = os.path.join('tools', 'cad2gis', data['fmw_path'])
        
        # 插入工具信息
        cursor.execute('''
            INSERT INTO cad2gis_model_market (
                fmw_id, fmw_name, project, description, fmw_path, 
                run_times, created_at, author
            ) VALUES (?, ?, ?, ?, ?, 0, datetime('now'), ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['project'],
            data['description'],
            fmw_path,
            username
        ))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '工具上传成功'
        })
    except Exception as e:
        print(f"上传cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'上传工具失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/cad2gis/tools/delete', methods=['POST'])
def delete_cad2gis_tool():
    try:
        data = request.get_json()
        if not data or 'fmw_id' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在且属于当前用户
        cursor.execute('''
            SELECT m.id, m.fmw_path 
            FROM cad2gis_model_market m
            JOIN users u ON m.author_id = u.id
            WHERE m.fmw_id = ? AND u.username = ?
        ''', (data['fmw_id'], request.headers.get('X-Username')))
        
        tool = cursor.fetchone()
        if not tool:
            return jsonify({
                'success': False,
                'message': '工具不存在或无权删除'
            }), 404
        
        # 删除工具
        cursor.execute('DELETE FROM cad2gis_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        conn.commit()
        
        # 删除工具文件
        try:
            if tool[1] and os.path.exists(tool[1]):
                os.remove(tool[1])
        except Exception as e:
            print(f"删除工具文件失败: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': '工具删除成功'
        })
    except Exception as e:
        print(f"删除cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除工具失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/delete', methods=['POST'])
def delete_Coordinate_tool():
    try:
        data = request.get_json()
        if not data or 'fmw_id' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在且属于当前用户
        cursor.execute('''
            SELECT m.id, m.fmw_path 
            FROM cad2gis_model_market m
            JOIN users u ON m.author_id = u.id
            WHERE m.fmw_id = ? AND u.username = ?
        ''', (data['fmw_id'], request.headers.get('X-Username')))
        
        tool = cursor.fetchone()
        if not tool:
            return jsonify({
                'success': False,
                'message': '工具不存在或无权删除'
            }), 404
        
        # 删除工具
        cursor.execute('DELETE FROM cad2gis_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        conn.commit()
        
        # 删除工具文件
        try:
            if tool[1] and os.path.exists(tool[1]):
                os.remove(tool[1])
        except Exception as e:
            print(f"删除工具文件失败: {str(e)}")
        
        return jsonify({
            'success': True,
            'message': '工具删除成功'
        })
    except Exception as e:
        print(f"删除cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除工具失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/cad2gis/tools/apply', methods=['POST'])
def apply_cad2gis_tool():
    try:

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        required_fields = ['fmw_id', 'fmw_name', 'user_project', 'applicant', 'reason', 'end_date', 'usage_count']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        # 记录系统时间和服务器时间，使用UTC时间
        system_time = datetime.now()
        server_time = datetime.now().isoformat()
        # 使用strftime格式化时间，避免时区转换问题
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"申请工具 - 系统时间: {system_time}")
        logger.info(f"申请工具 - 服务器时间: {server_time}")
        logger.info(f"申请工具 - 格式化时间: {created_at}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在
        cursor.execute('SELECT id FROM cad2gis_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        tool = cursor.fetchone()
        if not tool:
            return jsonify({
                'success': False,
                'message': '工具不存在'
            }), 404
        
        # 使用前端传递的审批人
        reviewer = data.get('reviewer')
        if not reviewer:
            return jsonify({
                'success': False,
                'message': '审批人不能为空'
            }), 400
        
        # 插入申请记录
        cursor.execute('''
            INSERT INTO tool_application (
                fmw_id, fmw_name, applicant, reason, end_date, usage_count, user_project, reviewer, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['applicant'],
            data['reason'],
            data['end_date'],
            data['usage_count'],
            data['user_project'],
            reviewer,
            'pending',
            created_at
        ))
        
        # 获取实际写入数据库的时间
        cursor.execute('SELECT created_at FROM tool_application WHERE id = last_insert_rowid()')
        db_time = cursor.fetchone()[0]
        logger.info(f"申请工具 - 实际写入数据库时间: {db_time}")
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '申请提交成功',
            'time_info': {
                'system_time': system_time.isoformat(),
                'server_time': server_time,
                'formatted_time': created_at,
                'db_time': db_time
            }
        })
    except Exception as e:
        logger.error(f"申请cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'申请提交失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/apply', methods=['POST'])
def apply_Coordinate_tool():
    try:
        # 检查许可证

        
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '无效的请求数据'
            }), 400
            
        required_fields = ['fmw_id', 'fmw_name', 'user_project', 'applicant', 'reason', 'end_date', 'usage_count']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必要字段: {field}'
                }), 400
        
        # 记录系统时间和服务器时间，使用UTC时间
        system_time = datetime.now()
        server_time = datetime.now().isoformat()
        # 使用strftime格式化时间，避免时区转换问题
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"申请工具 - 系统时间: {system_time}")
        logger.info(f"申请工具 - 服务器时间: {server_time}")
        logger.info(f"申请工具 - 格式化时间: {created_at}")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查工具是否存在
        cursor.execute('SELECT id FROM coordinatetransformation_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        tool = cursor.fetchone()
        if not tool:
            return jsonify({
                'success': False,
                'message': '工具不存在'
            }), 404
        
        # 使用前端传递的审批人
        reviewer = data.get('reviewer')
        if not reviewer:
            return jsonify({
                'success': False,
                'message': '审批人不能为空'
            }), 400
        
        # 插入申请记录
        cursor.execute('''
            INSERT INTO tool_application (
                fmw_id, fmw_name, applicant, reason, end_date, usage_count, user_project, reviewer, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['applicant'],
            data['reason'],
            data['end_date'],
            data['usage_count'],
            data['user_project'],
            reviewer,
            'pending',
            created_at
        ))
        
        # 获取实际写入数据库的时间
        cursor.execute('SELECT created_at FROM tool_application WHERE id = last_insert_rowid()')
        db_time = cursor.fetchone()[0]
        logger.info(f"申请工具 - 实际写入数据库时间: {db_time}")
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '申请提交成功',
            'time_info': {
                'system_time': system_time.isoformat(),
                'server_time': server_time,
                'formatted_time': created_at,
                'db_time': db_time
            }
        })
    except Exception as e:
        logger.error(f"申请cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'申请提交失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/cad2gis/tools/approve', methods=['POST'])
def approve_cad2gis_tool():
    try:
        data = request.get_json()
        if not data or 'applicationId' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取申请信息
        cursor.execute('''
            SELECT a.*, m.fmw_id, m.fmw_name, m.project
            FROM tool_application a
            JOIN cad2gis_model_market m ON a.fmw_id = m.fmw_id
            WHERE a.id = ?
        ''', (data['applicationId'],))
        
        application = cursor.fetchone()
        if not application:
            return jsonify({
                'success': False,
                'message': '申请不存在'
            }), 404
        
        # 更新申请状态
        cursor.execute('''
            UPDATE tool_application 
            SET status = '已通过', approved_at = datetime('now')
            WHERE id = ?
        ''', (data['applicationId'],))
        
        # 添加用户工具关系
        cursor.execute('''
            INSERT INTO cad2gis_user_tools (
                fmw_id, username, usage_count, remaining_count,
                end_date, created_at, user_project
            ) VALUES (?, ?, ?, ?, ?, datetime('now'), ?)
        ''', (
            application[1],  # fmw_id
            application[4],  # applicant
            application[6],  # usage_count
            application[6],  # remaining_count
            application[5],  # end_date
            application[10]  # user_project
        ))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '审批通过成功'
        })
    except Exception as e:
        print(f"审批cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'审批失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/approve', methods=['POST'])
def approve_Coordinate_tool():
    try:
        data = request.get_json()
        if not data or 'applicationId' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取申请信息
        cursor.execute('''
            SELECT a.*, m.fmw_id, m.fmw_name, m.project
            FROM tool_application a
            JOIN cad2gis_model_market m ON a.fmw_id = m.fmw_id
            WHERE a.id = ?
        ''', (data['applicationId'],))
        
        application = cursor.fetchone()
        if not application:
            return jsonify({
                'success': False,
                'message': '申请不存在'
            }), 404
        
        # 更新申请状态
        cursor.execute('''
            UPDATE tool_application 
            SET status = '已通过', approved_at = datetime('now')
            WHERE id = ?
        ''', (data['applicationId'],))
        
        # 添加用户工具关系
        cursor.execute('''
            INSERT INTO cad2gis_user_tools (
                fmw_id, username, usage_count, remaining_count,
                end_date, created_at, user_project
            ) VALUES (?, ?, ?, ?, ?, datetime('now'), ?)
        ''', (
            application[1],  # fmw_id
            application[4],  # applicant
            application[6],  # usage_count
            application[6],  # remaining_count
            application[5],  # end_date
            application[10]  # user_project
        ))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '审批通过成功'
        })
    except Exception as e:
        print(f"审批cad2gis工具失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'审批失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/cad2gis/tools/reject', methods=['POST'])
def reject_cad2gis_tool():
    try:
        data = request.get_json()
        if not data or 'applicationId' not in data or 'reason' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新申请状态
        cursor.execute('''
            UPDATE cad2gis_tool_application 
            SET status = '已驳回', reject_reason = ?, rejected_at = datetime('now')
            WHERE id = ?
        ''', (data['reason'], data['applicationId']))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '驳回成功'
        })
    except Exception as e:
        print(f"驳回cad2gis工具申请失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'驳回失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/reject', methods=['POST'])
def reject_Coordinate_tool():
    try:
        data = request.get_json()
        if not data or 'applicationId' not in data or 'reason' not in data:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新申请状态
        cursor.execute('''
            UPDATE cad2gis_tool_application 
            SET status = '已驳回', reject_reason = ?, rejected_at = datetime('now')
            WHERE id = ?
        ''', (data['reason'], data['applicationId']))
        
        conn.commit()
        
        return jsonify({
            'success': True,
            'message': '驳回成功'
        })
    except Exception as e:
        print(f"驳回cad2gis工具申请失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'驳回失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/cad2gis/tools/my-approvals', methods=['POST'])
def get_my_cad2gis_approvals():
    try:
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取用户的审批列表
        cursor.execute('''
            SELECT a.*, 
                   (SELECT COUNT(*) FROM cad2gis_task_records 
                    WHERE fmw_id = a.fmw_id AND username = a.applicant) as count
            FROM cad2gis_tool_application a
            JOIN cad2gis_model_market m ON a.fmw_id = m.fmw_id
            WHERE m.author = ?
            ORDER BY a.created_at DESC
        ''', (username,))
        
        approvals = cursor.fetchall()
        approvals_list = []
        
        for app in approvals:
            app_dict = {
                'id': app[0],
                'fmw_id': app[1],
                'fmw_name': app[2],
                'project': app[3],
                'applicant': app[4],
                'reason': app[5],
                'end_date': app[6],
                'usage_count': app[7],
                'status': app[8],
                'created_at': app[9],
                'user_project': app[10],
                'count': app[11]
            }
            approvals_list.append(app_dict)
        
        return jsonify({
            'success': True,
            'data': approvals_list
        })
    except Exception as e:
        print(f"获取cad2gis审批列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取审批列表失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()

@app.route('/api/Coordinate/tools/my-approvals', methods=['POST'])
def get_my_Coordinate_approvals():
    try:
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取用户的审批列表
        cursor.execute('''
            SELECT a.*, 
                   (SELECT COUNT(*) FROM cad2gis_task_records 
                    WHERE fmw_id = a.fmw_id AND username = a.applicant) as count
            FROM cad2gis_tool_application a
            JOIN cad2gis_model_market m ON a.fmw_id = m.fmw_id
            WHERE m.author = ?
            ORDER BY a.created_at DESC
        ''', (username,))
        
        approvals = cursor.fetchall()
        approvals_list = []
        
        for app in approvals:
            app_dict = {
                'id': app[0],
                'fmw_id': app[1],
                'fmw_name': app[2],
                'project': app[3],
                'applicant': app[4],
                'reason': app[5],
                'end_date': app[6],
                'usage_count': app[7],
                'status': app[8],
                'created_at': app[9],
                'user_project': app[10],
                'count': app[11]
            }
            approvals_list.append(app_dict)
        
        return jsonify({
            'success': True,
            'data': approvals_list
        })
    except Exception as e:
        print(f"获取cad2gis审批列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取审批列表失败: {str(e)}'
        }), 500
    finally:
        if conn:
            conn.close()


@app.route('/api/cad2gis/tools/run_records', methods=['POST'])
def get_cad2gis_run_records():
    try:
        data = request.get_json()
        fmw_id = data.get('fmw_id')  # 从请求中获取 fmw_id
        username = data.get('username')
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        conditions = []
        params = []
        if fmw_id:
            conditions.append("tool_id = ?")  # 使用 tool_id 而不是 tools_id
            params.append(fmw_id)
        if username:
            conditions.append("submitter = ?")
            params.append(username)
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"

        # 获取总记录数
        cursor.execute(f'''
            SELECT COUNT(*) FROM task_records 
            WHERE {where_clause}
        ''', params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        cursor.execute(f'''
            SELECT 
                id,
                task_id,
                tool_id,
                submit_time,
                status,
                time_consuming,
                file_name,
                file_size,
                error_message,
                up_nums
            FROM task_records 
            WHERE {where_clause}
            ORDER BY submit_time DESC
            LIMIT ? OFFSET ?
        ''', params + [page_size, offset])
        
        records = cursor.fetchall()
        
        # 格式化返回数据
        formatted_records = []
        for record in records:
            formatted_record = {
                "task_id": record[1],
                "tool_id": record[2],
                "submit_time": record[3],
                "status": record[4],
                "time_consuming": str(record[5]) if record[5] else "0",
                "file_name": record[6] or "",
                "file_size": str(record[7]) if record[7] else "0",
                "error_message": str(record[8]) if record[8] else "",
                "up_nums": str(record[9]) if record[9] else "0"
            }
            formatted_records.append(formatted_record)

        return jsonify({
            "success": True,
            "data": {
                "records": formatted_records,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"获取运行记录失败: {str(e)}"
        })
    finally:
        if 'conn' in locals():
            conn.close()


@app.route('/api/Coordinate/tools/run_records', methods=['POST'])
def get_Coordinate_run_records():
    try:
        data = request.get_json()
        fmw_id = data.get('fmw_id')  # 从请求中获取 fmw_id
        username = data.get('username')
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))

        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建查询条件
        conditions = []
        params = []
        if fmw_id:
            conditions.append("tool_id = ?")  # 使用 tool_id 而不是 tools_id
            params.append(fmw_id)
        if username:
            conditions.append("submitter = ?")
            params.append(username)
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"

        # 获取总记录数
        cursor.execute(f'''
            SELECT COUNT(*) FROM task_records 
            WHERE {where_clause}
        ''', params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        cursor.execute(f'''
            SELECT 
                id,
                task_id,
                tool_id,
                submit_time,
                status,
                time_consuming,
                file_name,
                file_size,
                error_message,
                up_nums
            FROM task_records 
            WHERE {where_clause}
            ORDER BY submit_time DESC
            LIMIT ? OFFSET ?
        ''', params + [page_size, offset])
        
        records = cursor.fetchall()
        
        # 格式化返回数据
        formatted_records = []
        for record in records:
            formatted_record = {
                "task_id": record[1],
                "tool_id": record[2],
                "submit_time": record[3],
                "status": record[4],
                "time_consuming": str(record[5]) if record[5] else "0",
                "file_name": record[6] or "",
                "file_size": str(record[7]) if record[7] else "0",
                "error_message": str(record[8]) if record[8] else "",
                "up_nums": str(record[9]) if record[9] else "0"
            }
            formatted_records.append(formatted_record)

        return jsonify({
            "success": True,
            "data": {
                "records": formatted_records,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "message": f"获取运行记录失败: {str(e)}"
        })
    finally:
        if 'conn' in locals():
            conn.close()


@app.route('/api/tools/update_usacount', methods=['POST'])
def update_usacount():
    try:
        data = request.get_json()
        id = data.get('id')
        username = data.get('username')
        
        if not id or not username:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新使用次数
        cursor.execute('''
        UPDATE tool_application 
        SET count = COALESCE(count, 0) + 1
        WHERE id = ? AND applicant = ?
        ''', (id, username))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '未找到对应的申请记录'}), 404
            
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '使用次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新使用次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新使用次数失败: {str(e)}'
        }), 500
    
@app.route('/api/cad2gis/tools/update_usacount', methods=['POST'])
def update_cad2gis_usacount():
    try:
        data = request.get_json()
        id = data.get('id')
        username = data.get('username')
        file_count = data.get('file_count', 1)  # 获取文件数量，默认为1
        
        if not id or not username:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新使用次数，根据文件数量增加
        cursor.execute('''
        UPDATE tool_application 
        SET count = COALESCE(count, 0) + ?
        WHERE id = ? AND applicant = ?
        ''', (file_count, id, username))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '未找到对应的申请记录'}), 404
            
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '使用次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新使用次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新使用次数失败: {str(e)}'
        }), 500
    
@app.route('/api/Coordinate/tools/update_usacount', methods=['POST'])
def update_coordinate_usacount():
    try:
        data = request.get_json()
        id = data.get('id')
        username = data.get('username')
        file_count = data.get('file_count', 1)  # 获取文件数量，默认为1
        
        if not id or not username:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新使用次数，根据文件数量增加
        cursor.execute('''
        UPDATE tool_application 
        SET count = COALESCE(count, 0) + ?
        WHERE id = ? AND applicant = ?
        ''', (file_count, id, username))
        
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '未找到对应的申请记录'}), 404
            
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': '使用次数更新成功'
        })
    except Exception as e:
        logger.error(f"更新使用次数失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新使用次数失败: {str(e)}'
        }), 500



@app.route('/api/quality/tools/my-approvals', methods=['POST'])
def get_my_quality_approvals():
    """
    获取当前用户为审批人的“数据质检”工具使用申请列表
    字段结构与 /api/Coordinate/tools/my-approvals 完全一致
    """
    try:
        username = request.headers.get('X-Username')

        if not username:
            return jsonify({
                'success': False,
                'message': '未提供用户名'
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询当前用户为审批人，且fmw_id为quality的申请
        cursor.execute('''
            SELECT ta.id,
                   ta.fmw_id,
                   ta.fmw_name,
                   ta.applicant,
                   ta.reason,
                   ta.end_date,
                   ta.usage_count,
                   ta.status,
                   ta.created_at,
                   ta.user_project,
                   COALESCE(ta.count, 0) as count
            FROM tool_application ta
            WHERE ta.applicant = ? AND ta.fmw_id = 'quality'
            ORDER BY ta.created_at DESC
        ''', (username,))

        approvals = cursor.fetchall()
        approvals_list = []
        for app in approvals:
            app_dict = {
                'id': app[0],
                'fmw_id': app[1],
                'fmw_name': app[2],
                'applicant': app[3],
                'reason': app[4],
                'end_date': app[5],
                'usage_count': app[6],
                'status': app[7],
                'created_at': app[8],
                'user_project': app[9],
                'count': app[10]
            }
            approvals_list.append(app_dict)

        return jsonify({
            'success': True,
            'data': approvals_list
        })
    except Exception as e:
        print(f"获取数据质检审批列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取审批列表失败: {str(e)}'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()



@app.route('/api/quality/tools/apply', methods=['POST'])
def apply_quality_tool():
    """
    数据质检工具使用申请接口
    字段与其它 apply 接口一致，支持审批人、参数校验、异常处理
    """
    try:
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'}), 400
        print(data)
        # 校验必要字段
        required_fields = ['fmw_id', 'fmw_name', 'user_project', 'applicant', 'reason', 'end_date', 'usage_count', 'reviewer']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必要字段: {field}'}), 400

        # 记录系统时间和服务器时间
        from datetime import datetime
        system_time = datetime.now()
        server_time = datetime.now().isoformat()
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        conn = get_db_connection()
        cursor = conn.cursor()
        print(data)
        # 检查工具是否存在
        cursor.execute('SELECT id FROM quality_model_market WHERE fmw_id = ?', (data['fmw_id'],))
        tool = cursor.fetchone()
        if not tool:
            return jsonify({'success': False, 'message': '工具不存在'}), 404
        print(data)
        # 插入申请记录
        cursor.execute('''
            INSERT INTO tool_application (
                fmw_id, fmw_name, applicant, reason, end_date, usage_count, user_project, reviewer, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['fmw_id'],
            data['fmw_name'],
            data['applicant'],
            data['reason'],
            data['end_date'],
            data['usage_count'],
            data['user_project'],
            data['reviewer'],
            'pending',
            created_at
        ))

        # 获取实际写入数据库的时间
        cursor.execute('SELECT created_at FROM tool_application WHERE id = last_insert_rowid()')
        db_time = cursor.fetchone()[0]

        conn.commit()
        return jsonify({
            'success': True,
            'message': '申请提交成功',
            'time_info': {
                'system_time': system_time.isoformat(),
                'server_time': server_time,
                'formatted_time': created_at,
                'db_time': db_time
            }
        })
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"提交数据质检工具申请失败: {str(e)}")
        return jsonify({'success': False, 'message': f'申请提交失败: {str(e)}'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/api/quality/upload-gdb', methods=['POST'])
@app.route('/gsi/api/quality/upload-gdb', methods=['POST'])
def upload_gdb_file():
    """上传并解析GDB压缩包"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400

        # 检查文件类型（支持压缩包）
        file_ext = file.filename.lower().split('.')[-1]
        if file_ext not in ['zip', 'rar', '7z']:
            return jsonify({'success': False, 'message': '只支持zip、rar、7z格式的压缩包'}), 400

        # 生成唯一文件名和任务ID
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
        task_id = f"{timestamp}_{random_suffix}"
        safe_filename = f"gdb_{task_id}.{file_ext}"

        # 保存文件到指定目录 tools/quality/data/随机码
        quality_data_dir = os.path.join(current_dir, 'tools', 'quality', 'data', task_id)
        if not os.path.exists(quality_data_dir):
            os.makedirs(quality_data_dir)

        file_path = os.path.join(quality_data_dir, safe_filename)
        file.save(file_path)

        # 启动后台解析任务
        import threading

        # 初始化任务状态
        task_status[task_id] = {
            'status': 'extracting',
            'progress': 0,
            'message': '正在解压文件...',
            'gdb_databases': [],
            'error': None,
            'uploaded_by': username,
            'original_filename': file.filename
        }

        # 启动后台线程进行解析
        thread = threading.Thread(target=process_gdb_async, args=(task_id, file_path, file_ext, quality_data_dir))
        thread.daemon = True
        thread.start()

        # 立即返回任务ID，前端可以通过SSE获取进度
        return jsonify({
            'success': True,
            'data': {
                'task_id': task_id,
                'message': '文件上传成功，开始解析...'
            }
        })
    except Exception as e:
        logger.error(f"上传GDB文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'上传GDB文件失败: {str(e)}'
        }), 500

def parse_gdb_layers(gdb_path):
    """解析GDB文件夹中的图层信息"""
    try:
        logger.info(f"开始解析GDB文件夹: {gdb_path}")

        # 尝试使用不同的方法解析GDB
        layers = []
        try:
            # 首先尝试使用GDAL/OGR解析
            layers = parse_gdb_with_gdal(gdb_path)
            logger.info(f"使用GDAL成功解析GDB，发现 {len(layers)} 个图层")
        except Exception as gdal_error:
            logger.warning(f"GDAL解析失败: {str(gdal_error)}")
            try:
                # 尝试使用fiona解析
                layers = parse_gdb_with_fiona(gdb_path)
                logger.info(f"使用fiona成功解析GDB，发现 {len(layers)} 个图层")
            except Exception as fiona_error:
                logger.warning(f"fiona解析失败: {str(fiona_error)}")
                # 使用模拟数据作为后备
                logger.info("使用模拟数据作为后备方案")
                layers = get_mock_gdb_layers(gdb_path)

        return layers
    except Exception as e:
        logger.error(f"解析GDB图层失败: {str(e)}")
        return get_mock_gdb_layers(gdb_path)

def process_gdb_async(task_id, file_path, file_ext, quality_data_dir):
    """后台异步处理GDB解析"""
    try:
        # 更新状态：开始解压
        task_status[task_id]['status'] = 'extracting'
        task_status[task_id]['progress'] = 10
        task_status[task_id]['message'] = '正在解压文件...'

        # 解压文件到同一目录
        extract_dir = os.path.join(quality_data_dir, 'extracted')
        if not os.path.exists(extract_dir):
            os.makedirs(extract_dir)

        try:
            if file_ext == 'zip':
                import zipfile
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            elif file_ext == 'rar':
                # 使用UnRAR.exe解压rar文件
                unrar_path = os.path.join(current_dir, '7z', 'UnRAR.exe')
                if os.path.exists(unrar_path):
                    subprocess.run([unrar_path, 'x', file_path, extract_dir], check=True)
                else:
                    raise Exception('UnRAR.exe未找到，无法解压RAR文件')
            elif file_ext == '7z':
                # 使用7z.exe解压7z文件
                seven_zip_path = os.path.join(current_dir, '7z', '7z.exe')
                if os.path.exists(seven_zip_path):
                    subprocess.run([seven_zip_path, 'x', file_path, f'-o{extract_dir}'], check=True)
                else:
                    raise Exception('7z.exe未找到，无法解压7Z文件')
        except Exception as e:
            task_status[task_id]['status'] = 'error'
            task_status[task_id]['error'] = f'解压文件失败: {str(e)}'
            return

        # 更新状态：查找GDB文件夹
        task_status[task_id]['status'] = 'scanning'
        task_status[task_id]['progress'] = 30
        task_status[task_id]['message'] = '正在查找GDB文件夹...'

        # 查找GDB文件夹
        gdb_folders = []
        for root, dirs, files in os.walk(extract_dir):
            for d in dirs:
                if d.lower().endswith('.gdb'):
                    gdb_path = os.path.join(root, d)
                    # 检查是否包含.gdbtable文件
                    has_gdbtable = False
                    for _r, _ds, _fs in os.walk(gdb_path):
                        for _f in _fs:
                            if _f.lower().endswith('.gdbtable'):
                                has_gdbtable = True
                                break
                        if has_gdbtable:
                            break
                    if has_gdbtable:
                        gdb_folders.append(gdb_path)

        if not gdb_folders:
            task_status[task_id]['status'] = 'error'
            task_status[task_id]['error'] = '压缩包中未找到有效的GDB文件夹'
            return

        # 更新状态：开始解析GDB
        task_status[task_id]['status'] = 'parsing'
        task_status[task_id]['progress'] = 50
        task_status[task_id]['message'] = f'发现 {len(gdb_folders)} 个GDB文件夹，开始解析...'

        # 解析所有GDB文件夹
        gdb_databases = []
        for i, gdb_path in enumerate(gdb_folders):
            # 更新进度
            progress = 50 + (i / len(gdb_folders)) * 40
            task_status[task_id]['progress'] = int(progress)
            task_status[task_id]['message'] = f'正在解析第 {i+1}/{len(gdb_folders)} 个GDB文件夹...'

            layers = parse_gdb_layers(gdb_path)
            gdb_database = {
                'path': gdb_path,
                'name': os.path.basename(gdb_path).replace('.gdb', ''),
                'layers': layers,
                'totalFeatures': sum(layer.get('feature_count', 0) for layer in layers)
            }
            gdb_databases.append(gdb_database)

            # 实时更新已解析的数据库
            task_status[task_id]['gdb_databases'] = gdb_databases.copy()

        # 完成解析
        task_status[task_id]['status'] = 'completed'
        task_status[task_id]['progress'] = 100
        task_status[task_id]['message'] = f'解析完成！共发现 {len(gdb_databases)} 个数据库'
        task_status[task_id]['data'] = {
            'base_path': extract_dir,
            'gdb_databases': gdb_databases,
            'gdb_count': len(gdb_folders)
        }

        # 记录到上传历史表
        try:
            # 计算统计信息
            total_layers = sum(len(db['layers']) for db in gdb_databases)
            total_features = sum(db.get('totalFeatures', 0) for db in gdb_databases)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

            # 获取原始文件名和上传用户信息
            original_filename = task_status[task_id].get('original_filename', os.path.basename(file_path))
            uploaded_by = task_status[task_id].get('uploaded_by', 'unknown')

            # 生成唯一文件名（避免同名文件冲突）
            unique_filename = generate_unique_filename(original_filename, uploaded_by)

            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # 使用服务器系统时间
            from datetime import datetime
            upload_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 转换为相对路径，处理跨磁盘驱动器的情况
            try:
                relative_extract_dir = os.path.relpath(extract_dir, os.path.dirname(os.path.abspath(__file__)))
                relative_extract_dir = relative_extract_dir.replace('\\', '/')
            except ValueError as e:
                # 如果跨磁盘驱动器，使用绝对路径
                logger.warning(f"无法计算相对路径（跨磁盘驱动器），使用绝对路径: {str(e)}")
                relative_extract_dir = extract_dir.replace('\\', '/')

            # 序列化数据库和图层信息
            import json
            databases_info_json = json.dumps(gdb_databases, ensure_ascii=False)

            # 提取所有图层信息
            all_layers = []
            for db in gdb_databases:
                for layer in db.get('layers', []):
                    layer_info = {
                        'database_name': db['name'],
                        'database_path': db['path'],
                        'layer_name': layer['name'],
                        'layer_type': layer['type'],
                        'feature_count': layer.get('feature_count', 0),
                        'fields': layer.get('fields', [])
                    }
                    all_layers.append(layer_info)

            layers_info_json = json.dumps(all_layers, ensure_ascii=False)

            cursor.execute('''
            INSERT INTO gdb_upload_history
            (original_filename, unique_filename, stored_path, file_size, uploaded_by, databases_count, total_layers, total_features, status, upload_time, databases_info, layers_info)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                original_filename,
                unique_filename,
                relative_extract_dir,
                file_size,
                uploaded_by,
                len(gdb_databases),
                total_layers,
                total_features,
                'completed',
                upload_time,
                databases_info_json,
                layers_info_json
            ))

            conn.commit()
            conn.close()

            logger.info(f"GDB上传历史记录已保存: {original_filename}")

        except Exception as e:
            logger.error(f"保存GDB上传历史失败: {str(e)}")
            # 不影响主流程，继续执行

    except Exception as e:
        logger.error(f"后台解析GDB失败: {str(e)}")
        task_status[task_id]['status'] = 'error'
        task_status[task_id]['error'] = str(e)

@app.route('/api/quality/gdb-progress/<task_id>')
@app.route('/gsi/api/quality/gdb-progress/<task_id>')
def gdb_progress_stream(task_id):
    """SSE端点，实时推送GDB解析进度"""
    def generate():
        try:
            while True:
                if task_id not in task_status:
                    yield f"data: {json.dumps({'error': '任务不存在'})}\n\n"
                    break

                status = task_status[task_id]
                yield f"data: {json.dumps(status)}\n\n"

                # 如果任务完成或出错，结束流
                if status['status'] in ['completed', 'error']:
                    break

                time.sleep(1)  # 每秒推送一次

        except Exception as e:
            logger.error(f"SSE流推送错误: {str(e)}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"

    response = Response(generate(), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['Connection'] = 'keep-alive'
    response.headers['Access-Control-Allow-Origin'] = '*'
    return response

@app.route('/api/quality/gdb-status/<task_id>')
@app.route('/gsi/api/quality/gdb-status/<task_id>')
def get_gdb_status(task_id):
    """获取GDB解析任务状态"""
    if task_id not in task_status:
        return jsonify({'success': False, 'message': '任务不存在'}), 404

    return jsonify({
        'success': True,
        'data': task_status[task_id]
    })

def parse_gdb_with_gdal(gdb_path):
    """使用GDAL/OGR解析GDB文件"""
    try:
        from osgeo import ogr, osr
        import os

        logger.info(f"使用GDAL解析GDB: {gdb_path}")

        # 检查GDB路径是否存在
        if not os.path.exists(gdb_path):
            raise Exception(f"GDB路径不存在: {gdb_path}")

        layers = []

        # 打开GDB数据源
        driver = ogr.GetDriverByName("OpenFileGDB")
        if driver is None:
            logger.error("GDAL OpenFileGDB驱动不可用")
            raise Exception("GDAL OpenFileGDB驱动不可用")

        data_source = driver.Open(gdb_path, 0)  # 0表示只读
        if data_source is None:
            logger.error(f"无法打开GDB文件: {gdb_path}")
            raise Exception(f"无法打开GDB文件: {gdb_path}")

        layer_count = data_source.GetLayerCount()
        logger.info(f"GDAL发现 {layer_count} 个图层")

        # 遍历所有图层
        for i in range(layer_count):
            try:
                layer = data_source.GetLayer(i)
                if layer is None:
                    logger.warning(f"无法获取第 {i} 个图层")
                    continue

                layer_name = layer.GetName()
                logger.info(f"正在解析图层: {layer_name}")

                # 获取图层信息
                layer_defn = layer.GetLayerDefn()
                feature_count = layer.GetFeatureCount()

                # 获取几何类型
                geom_type = layer.GetGeomType()
                geometry_type = get_geometry_type_name(geom_type)
                layer_type = get_layer_type_from_geom(geom_type)

                # 获取坐标系信息
                spatial_ref = layer.GetSpatialRef()
                coordinate_system = parse_coordinate_system(spatial_ref)

                # 获取字段信息
                fields = []
                for j in range(layer_defn.GetFieldCount()):
                    field_defn = layer_defn.GetFieldDefn(j)
                    field_info = {
                        'name': field_defn.GetName(),
                        'type': get_field_type_name(field_defn.GetType()),
                        'length': field_defn.GetWidth()
                    }
                    fields.append(field_info)

                # 检查是否在要素集中
                feature_set = None
                original_layer_name = layer_name
                if '\\' in layer_name or '/' in layer_name:
                    parts = layer_name.replace('\\', '/').split('/')
                    if len(parts) > 1:
                        feature_set = parts[0]
                        layer_name = parts[-1]

                layer_info = {
                    'name': layer_name,
                    'type': layer_type,
                    'feature_count': feature_count,
                    'geometry_type': geometry_type,
                    'feature_set': feature_set,
                    'coordinate_system': coordinate_system,
                    'fields': fields
                }

                layers.append(layer_info)
                logger.info(f"成功解析图层: {layer_name}, 要素数: {feature_count}, 几何类型: {geometry_type}, 字段数: {len(fields)}")

            except Exception as layer_error:
                logger.error(f"解析第 {i} 个图层失败: {str(layer_error)}")
                continue

        data_source = None  # 关闭数据源
        logger.info(f"GDAL解析完成，共解析 {len(layers)} 个图层")
        return layers

    except ImportError:
        raise Exception("GDAL库未安装，请安装GDAL")
    except Exception as e:
        logger.error(f"GDAL解析GDB详细错误: {str(e)}")
        raise Exception(f"GDAL解析GDB失败: {str(e)}")

def parse_gdb_with_fiona(gdb_path):
    """使用fiona解析GDB文件"""
    try:
        import fiona
        import os

        logger.info(f"使用fiona解析GDB: {gdb_path}")

        # 检查GDB路径是否存在
        if not os.path.exists(gdb_path):
            raise Exception(f"GDB路径不存在: {gdb_path}")

        layers = []

        # 列出GDB中的所有图层
        try:
            layer_names = fiona.listlayers(gdb_path)
            logger.info(f"fiona发现图层列表: {layer_names}")
        except Exception as list_error:
            logger.error(f"fiona列出图层失败: {str(list_error)}")
            raise Exception(f"无法列出GDB图层: {str(list_error)}")

        if not layer_names:
            logger.warning(f"GDB中没有发现任何图层: {gdb_path}")
            return layers

        for layer_name in layer_names:
            try:
                logger.info(f"正在解析图层: {layer_name}")
                with fiona.open(gdb_path, layer=layer_name) as src:
                    # 获取基本信息
                    feature_count = len(src)
                    geometry_type = src.schema.get('geometry', 'Unknown')
                    layer_type = get_layer_type_from_geom_name(geometry_type)

                    # 获取坐标系
                    coordinate_system = parse_coordinate_system_from_crs(src.crs)

                    # 获取字段信息
                    fields = []
                    if 'properties' in src.schema:
                        for field_name, field_type in src.schema['properties'].items():
                            field_info = {
                                'name': field_name,
                                'type': field_type,
                                'length': 0  # fiona不提供字段长度信息
                            }
                            fields.append(field_info)

                    # 检查要素集
                    feature_set = None
                    original_layer_name = layer_name
                    if '\\' in layer_name or '/' in layer_name:
                        parts = layer_name.replace('\\', '/').split('/')
                        if len(parts) > 1:
                            feature_set = parts[0]
                            layer_name = parts[-1]

                    layer_info = {
                        'name': layer_name,
                        'type': layer_type,
                        'feature_count': feature_count,
                        'geometry_type': geometry_type,
                        'feature_set': feature_set,
                        'coordinate_system': coordinate_system,
                        'fields': fields
                    }

                    layers.append(layer_info)
                    logger.info(f"成功解析图层: {layer_name}, 要素数: {feature_count}, 几何类型: {geometry_type}, 字段数: {len(fields)}")

            except Exception as layer_error:
                logger.error(f"解析图层 {layer_name} 失败: {str(layer_error)}")
                # 继续处理其他图层，不中断整个过程
                continue

        logger.info(f"fiona解析完成，共解析 {len(layers)} 个图层")
        return layers

    except ImportError:
        raise Exception("fiona库未安装")
    except Exception as e:
        logger.error(f"fiona解析GDB详细错误: {str(e)}")
        raise Exception(f"fiona解析GDB失败: {str(e)}")

def get_geometry_type_name(geom_type):
    """将GDAL几何类型转换为名称"""
    try:
        from osgeo import ogr
        geom_type_map = {
            ogr.wkbPoint: 'Point',
            ogr.wkbLineString: 'Polyline',
            ogr.wkbPolygon: 'Polygon',
            ogr.wkbMultiPoint: 'MultiPoint',
            ogr.wkbMultiLineString: 'MultiPolyline',
            ogr.wkbMultiPolygon: 'MultiPolygon',
            ogr.wkbPoint25D: 'Point',
            ogr.wkbLineString25D: 'Polyline',
            ogr.wkbPolygon25D: 'Polygon'
        }
        return geom_type_map.get(geom_type, 'Unknown')
    except:
        return 'Unknown'

def get_layer_type_from_geom(geom_type):
    """从几何类型获取图层类型"""
    try:
        from osgeo import ogr
        if geom_type in [ogr.wkbPoint, ogr.wkbMultiPoint, ogr.wkbPoint25D]:
            return 'point'
        elif geom_type in [ogr.wkbLineString, ogr.wkbMultiLineString, ogr.wkbLineString25D]:
            return 'line'
        elif geom_type in [ogr.wkbPolygon, ogr.wkbMultiPolygon, ogr.wkbPolygon25D]:
            return 'polygon'
        else:
            return 'unknown'
    except:
        return 'unknown'

def get_layer_type_from_geom_name(geom_name):
    """从几何类型名称获取图层类型"""
    geom_name = geom_name.lower()
    if 'point' in geom_name:
        return 'point'
    elif 'line' in geom_name or 'string' in geom_name:
        return 'line'
    elif 'polygon' in geom_name:
        return 'polygon'
    else:
        return 'unknown'

def get_field_type_name(field_type):
    """将GDAL字段类型转换为名称"""
    try:
        from osgeo import ogr
        field_type_map = {
            ogr.OFTInteger: 'Integer',
            ogr.OFTIntegerList: 'IntegerList',
            ogr.OFTReal: 'Double',
            ogr.OFTRealList: 'DoubleList',
            ogr.OFTString: 'String',
            ogr.OFTStringList: 'StringList',
            ogr.OFTWideString: 'String',
            ogr.OFTWideStringList: 'StringList',
            ogr.OFTBinary: 'Binary',
            ogr.OFTDate: 'Date',
            ogr.OFTTime: 'Time',
            ogr.OFTDateTime: 'DateTime',
            ogr.OFTInteger64: 'Integer64'
        }
        return field_type_map.get(field_type, 'Unknown')
    except:
        return 'Unknown'

def parse_coordinate_system(spatial_ref):
    """解析坐标系信息，简化显示"""
    if spatial_ref is None:
        return "未知"

    try:
        # 尝试获取EPSG代码
        epsg_code = spatial_ref.GetAuthorityCode(None)
        if epsg_code:
            return f"EPSG:{epsg_code}"

        # 获取坐标系的WKT字符串
        wkt = spatial_ref.ExportToWkt()
        if not wkt:
            return "未知"

        # 解析常见的坐标系
        coordinate_system = parse_wkt_coordinate_system(wkt)
        return coordinate_system

    except Exception as e:
        logger.warning(f"解析坐标系失败: {str(e)}")
        return "未知"

def parse_coordinate_system_from_crs(crs):
    """从fiona的CRS对象解析坐标系"""
    if not crs:
        return "未知"

    try:
        logger.info(f"解析CRS对象: {crs}")

        # 检查是否有EPSG代码
        if isinstance(crs, dict):
            if 'init' in crs:
                init_value = crs.get('init', '')
                if init_value.startswith('epsg:'):
                    epsg_code = init_value.split(':')[1]
                    return f"EPSG:{epsg_code}"

            # 检查其他EPSG格式
            if 'epsg' in crs:
                return f"EPSG:{crs['epsg']}"

            # 检查是否有wkt字符串
            if 'wkt' in crs:
                return parse_wkt_coordinate_system(crs['wkt'])

        # 如果CRS是字符串格式
        if isinstance(crs, str):
            if crs.startswith('EPSG:') or crs.startswith('epsg:'):
                return crs.upper()
            elif crs.startswith('PROJCS[') or crs.startswith('GEOGCS['):
                return parse_wkt_coordinate_system(crs)

        # 尝试转换为字符串并解析
        crs_str = str(crs)
        logger.info(f"CRS字符串: {crs_str}")

        # 检查常见的坐标系标识
        if 'epsg' in crs_str.lower():
            import re
            epsg_match = re.search(r'epsg[:\s]*(\d+)', crs_str, re.IGNORECASE)
            if epsg_match:
                return f"EPSG:{epsg_match.group(1)}"

        if 'PROJCS[' in crs_str or 'GEOGCS[' in crs_str:
            return parse_wkt_coordinate_system(crs_str)

        return "独立坐标"

    except Exception as e:
        logger.warning(f"解析CRS失败: {str(e)}")
        return "未知"

def parse_wkt_coordinate_system(wkt):
    """解析WKT格式的坐标系字符串"""
    try:
        # 提取EPSG代码
        if 'AUTHORITY["EPSG",' in wkt:
            import re
            epsg_match = re.search(r'AUTHORITY\["EPSG","(\d+)"\]', wkt)
            if epsg_match:
                return f"EPSG:{epsg_match.group(1)}"

        # 解析CGCS2000系列
        if 'CGCS2000' in wkt:
            if '3_Degree_GK' in wkt:
                # 提取中央经线
                import re
                cm_match = re.search(r'central_meridian["\s]*,\s*(\d+)', wkt)
                if cm_match:
                    cm = cm_match.group(1)
                    return f"CGCS2000 3°带 {cm}°E"
                else:
                    return "CGCS2000 3°带"
            elif '6_Degree_GK' in wkt:
                import re
                cm_match = re.search(r'central_meridian["\s]*,\s*(\d+)', wkt)
                if cm_match:
                    cm = cm_match.group(1)
                    return f"CGCS2000 6°带 {cm}°E"
                else:
                    return "CGCS2000 6°带"
            else:
                return "CGCS2000"

        # 解析Beijing 1954
        if 'Beijing_1954' in wkt or 'Beijing 1954' in wkt:
            if '3_Degree_GK' in wkt or '3 Degree GK' in wkt:
                return "Beijing54 3°带"
            elif '6_Degree_GK' in wkt or '6 Degree GK' in wkt:
                return "Beijing54 6°带"
            else:
                return "Beijing 1954"

        # 解析Xi'an 1980
        if 'Xian_1980' in wkt or "Xi'an 1980" in wkt:
            if '3_Degree_GK' in wkt or '3 Degree GK' in wkt:
                return "Xian80 3°带"
            elif '6_Degree_GK' in wkt or '6 Degree GK' in wkt:
                return "Xian80 6°带"
            else:
                return "Xi'an 1980"

        # 解析WGS84
        if 'WGS_1984' in wkt or 'WGS 1984' in wkt:
            if 'UTM' in wkt:
                import re
                zone_match = re.search(r'UTM[_\s]*Zone[_\s]*(\d+)', wkt)
                if zone_match:
                    zone = zone_match.group(1)
                    hemisphere = 'N' if 'North' in wkt else 'S'
                    return f"WGS84 UTM {zone}{hemisphere}"
                else:
                    return "WGS84 UTM"
            else:
                return "WGS84"

        # 解析Web Mercator
        if 'Web_Mercator' in wkt or 'WGS_1984_Web_Mercator' in wkt:
            return "Web Mercator"

        # 如果都不匹配，返回简化的名称
        if 'PROJCS[' in wkt:
            import re
            name_match = re.search(r'PROJCS\["([^"]+)"', wkt)
            if name_match:
                name = name_match.group(1)
                # 简化长名称
                if len(name) > 30:
                    return "投影坐标系"
                return name

        if 'GEOGCS[' in wkt:
            return "地理坐标系"

        return "独立坐标"

    except Exception as e:
        logger.warning(f"解析WKT坐标系失败: {str(e)}")
        return "未知"

def parse_proj_string(proj_string):
    """解析proj4格式的坐标系字符串"""
    try:
        if 'epsg' in proj_string.lower():
            import re
            epsg_match = re.search(r'epsg[:\s]*(\d+)', proj_string, re.IGNORECASE)
            if epsg_match:
                return f"EPSG:{epsg_match.group(1)}"

        return "独立坐标"

    except Exception as e:
        logger.warning(f"解析proj字符串失败: {str(e)}")
        return "未知"

def get_mock_gdb_layers(gdb_path):
    """获取模拟的GDB图层数据"""
    logger.info("使用模拟GDB图层数据")

    # 从GDB路径中提取一些信息来生成更真实的模拟数据
    import os
    gdb_name = os.path.basename(gdb_path)

    # 生成一些基于GDB名称的模拟图层
    layers = [
        {
            'name': f'{gdb_name}_道路',
            'type': 'line',
            'feature_count': 1250,
            'geometry_type': 'Polyline',
            'feature_set': '交通设施',
            'coordinate_system': 'CGCS2000_3_Degree_GK_CM_114E',
            'fields': [
                {'name': 'OBJECTID', 'type': 'Integer', 'length': 4},
                {'name': 'DLMC', 'type': 'String', 'length': 50},
                {'name': 'DLBM', 'type': 'String', 'length': 10},
                {'name': 'DLKD', 'type': 'Double', 'length': 8},
                {'name': 'SHAPE_Length', 'type': 'Double', 'length': 8}
            ]
        },
        {
            'name': f'{gdb_name}_建筑物',
            'type': 'polygon',
            'feature_count': 856,
            'geometry_type': 'Polygon',
            'feature_set': '建筑设施',
            'coordinate_system': 'CGCS2000_3_Degree_GK_CM_114E',
            'fields': [
                {'name': 'OBJECTID', 'type': 'Integer', 'length': 4},
                {'name': 'JZWMC', 'type': 'String', 'length': 100},
                {'name': 'JZWLX', 'type': 'String', 'length': 20},
                {'name': 'JZWGD', 'type': 'Double', 'length': 8},
                {'name': 'SHAPE_Area', 'type': 'Double', 'length': 8},
                {'name': 'SHAPE_Length', 'type': 'Double', 'length': 8}
            ]
        },
        {
            'name': f'{gdb_name}_控制点',
            'type': 'point',
            'feature_count': 45,
            'geometry_type': 'Point',
            'feature_set': '测量控制',
            'coordinate_system': 'CGCS2000_3_Degree_GK_CM_114E',
            'fields': [
                {'name': 'OBJECTID', 'type': 'Integer', 'length': 4},
                {'name': 'KDMC', 'type': 'String', 'length': 50},
                {'name': 'KDLX', 'type': 'String', 'length': 20},
                {'name': 'X', 'type': 'Double', 'length': 8},
                {'name': 'Y', 'type': 'Double', 'length': 8},
                {'name': 'Z', 'type': 'Double', 'length': 8},
                {'name': 'JDXX', 'type': 'String', 'length': 10}
            ]
        }
    ]

    return layers

@app.route('/api/quality/items', methods=['GET'])
def get_quality_items():
    """获取质检项字典"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取所有质检项及其配置规则，按一级分类和二级分类排序
        cursor.execute('''
        SELECT q.id, q.level1_name, q.level2_name, q.item_name, q.object_type,
               q.item_code, q.rule_content, q.param_type, q.param_name, q.example_img,
               r.config_type, r.layer_filter, r.parameter_config, r.ui_component, r.validation_rules
        FROM quality_item_dict q
        LEFT JOIN quality_item_config_rules r ON q.id = r.item_id
        ORDER BY q.level1_name, q.level2_name, q.item_name
        ''')

        items = []
        for row in cursor.fetchall():
            # 解析JSON字符串
            parameter_config = {}
            validation_rules = {}
            try:
                if row['parameter_config']:
                    import json
                    parameter_config = json.loads(row['parameter_config'])
                if row['validation_rules']:
                    validation_rules = json.loads(row['validation_rules'])
            except:
                pass

            items.append({
                'id': row['id'],
                'level1_name': row['level1_name'],
                'level2_name': row['level2_name'],
                'item_name': row['item_name'],
                'object_type': row['object_type'],
                'item_code': row['item_code'],
                'rule_content': row['rule_content'],
                'param_type': row['param_type'],
                'param_name': row['param_name'],
                'example_img': row['example_img'],
                'config': {
                    'config_type': row['config_type'],
                    'layer_filter': row['layer_filter'],
                    'parameter_config': parameter_config,
                    'ui_component': row['ui_component'],
                    'validation_rules': validation_rules
                }
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': items
        })
    except Exception as e:
        logger.error(f"获取质检项字典失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取质检项字典失败: {str(e)}'
        }), 500

@app.route('/api/quality/config/template', methods=['GET'])
def download_quality_template():
    """下载质检配置表模板"""
    try:
        # 创建Excel模板
        wb = Workbook()
        ws = wb.active
        ws.title = "质检配置表"
        
        # 设置表头
        headers = ['一级分类', '二级分类', '质检项名称', '对象类型', '质检项代码', '规则内容', '参数类型', '参数名称', '是否启用', '参数值']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 设置表头样式
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 保存到临时文件
        template_path = os.path.join(TEMP_DIR, 'quality_template.xlsx')
        wb.save(template_path)
        
        return send_file(
            template_path,
            as_attachment=True,
            download_name='质检配置表模板.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        logger.error(f"生成质检配置表模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'生成质检配置表模板失败: {str(e)}'
        }), 500

@app.route('/api/quality/config/save', methods=['POST'])
def save_quality_config():
    """保存质检配置"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        data = request.get_json()
        config_name = data.get('name')
        description = data.get('description')
        gdb_path = data.get('gdb_path')
        selected_layers = data.get('selected_layers', [])
        quality_items = data.get('quality_items', [])
        license_id = data.get('license_id')
        
        if not all([config_name, gdb_path, selected_layers, quality_items]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 生成Excel配置文件
        wb = Workbook()
        ws = wb.active
        ws.title = "质检配置表"
        
        # 设置表头
        headers = ['一级分类', '二级分类', '质检项名称', '对象类型', '质检项代码', '规则内容', '参数类型', '参数名称', '是否启用', '参数值']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 设置表头样式
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 填充数据
        row = 2
        for item in quality_items:
            if item.get('enabled', False):
                ws.cell(row=row, column=1, value=item.get('level1_name', ''))
                ws.cell(row=row, column=2, value=item.get('level2_name', ''))
                ws.cell(row=row, column=3, value=item.get('item_name', ''))
                ws.cell(row=row, column=4, value=item.get('object_type', ''))
                ws.cell(row=row, column=5, value=item.get('item_code', ''))
                ws.cell(row=row, column=6, value=item.get('rule_content', ''))
                ws.cell(row=row, column=7, value=item.get('param_type', ''))
                ws.cell(row=row, column=8, value=item.get('param_name', ''))
                ws.cell(row=row, column=9, value='是')
                ws.cell(row=row, column=10, value=item.get('param_value', ''))
                row += 1
        
        # 生成唯一文件名
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
        excel_filename = f"quality_config_{timestamp}_{random_suffix}.xlsx"
        
        # 保存Excel文件
        excel_dir = os.path.join(TEMP_DIR, 'quality_configs')
        if not os.path.exists(excel_dir):
            os.makedirs(excel_dir)
        
        excel_path = os.path.join(excel_dir, excel_filename)
        wb.save(excel_path)
        
        # 保存到数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
        INSERT INTO quality_config 
        (name, description, file_path, gdb_path, creator_id, creator_name, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ''', (
            config_name,
            description,
            excel_path,
            gdb_path,
            username,  # 这里应该获取用户ID，暂时用username
            username
        ))
        
        config_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'config_id': config_id,
                'excel_path': excel_path,
                'excel_filename': excel_filename
            }
        })
    except Exception as e:
        logger.error(f"保存质检配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'保存质检配置失败: {str(e)}'
        }), 500



@app.route('/api/quality/config/download/<int:config_id>', methods=['GET'])
def download_quality_config(config_id):
    """下载质检配置文件"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT file_path, name
        FROM quality_config
        WHERE id = ? AND creator_name = ?
        ''', (config_id, username))
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            return jsonify({'success': False, 'message': '配置不存在或无权限访问'}), 404
        
        file_path = result['file_path']
        config_name = result['name']
        
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'message': '配置文件不存在'}), 404
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f'{config_name}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        logger.error(f"下载质检配置文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'下载质检配置文件失败: {str(e)}'
        }), 500

@app.route('/api/quality/items/add', methods=['POST'])
def add_quality_item():
    """新增质检项"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        data = request.get_json()
        level1_name = data.get('level1_name')
        level2_name = data.get('level2_name')
        item_name = data.get('item_name')
        object_type = data.get('object_type', '通用')
        item_code = data.get('item_code')
        rule_content = data.get('rule_content')
        param_type = data.get('param_type', '无')
        param_name = data.get('param_name', '')
        example_img = data.get('example_img', '')

        # 校验必要字段
        if not all([level1_name, level2_name, item_name, item_code, rule_content]):
            return jsonify({'success': False, 'message': '缺少必要字段'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查质检项代码是否已存在
        cursor.execute('SELECT id FROM quality_item_dict WHERE item_code = ?', (item_code,))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '质检项代码已存在'}), 400

        # 插入新质检项
        cursor.execute('''
        INSERT INTO quality_item_dict
        (level1_name, level2_name, item_name, object_type, item_code, rule_content, param_type, param_name, example_img)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (level1_name, level2_name, item_name, object_type, item_code, rule_content, param_type, param_name, example_img))

        item_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"用户 {username} 新增质检项: {item_name} (ID: {item_id})")

        return jsonify({
            'success': True,
            'data': {
                'id': item_id,
                'message': '质检项添加成功'
            }
        })
    except Exception as e:
        logger.error(f"新增质检项失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'新增质检项失败: {str(e)}'
        }), 500

@app.route('/api/quality/items/update/<int:item_id>', methods=['PUT'])
def update_quality_item(item_id):
    """更新质检项"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        data = request.get_json()
        level1_name = data.get('level1_name')
        level2_name = data.get('level2_name')
        item_name = data.get('item_name')
        object_type = data.get('object_type', '通用')
        item_code = data.get('item_code')
        rule_content = data.get('rule_content')
        param_type = data.get('param_type', '无')
        param_name = data.get('param_name', '')
        example_img = data.get('example_img', '')

        # 校验必要字段
        if not all([level1_name, level2_name, item_name, item_code, rule_content]):
            return jsonify({'success': False, 'message': '缺少必要字段'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查质检项是否存在
        cursor.execute('SELECT id FROM quality_item_dict WHERE id = ?', (item_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '质检项不存在'}), 404

        # 检查质检项代码是否被其他项使用
        cursor.execute('SELECT id FROM quality_item_dict WHERE item_code = ? AND id != ?', (item_code, item_id))
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '质检项代码已被其他项使用'}), 400

        # 更新质检项
        cursor.execute('''
        UPDATE quality_item_dict
        SET level1_name = ?, level2_name = ?, item_name = ?, object_type = ?,
            item_code = ?, rule_content = ?, param_type = ?, param_name = ?, example_img = ?
        WHERE id = ?
        ''', (level1_name, level2_name, item_name, object_type, item_code, rule_content,
              param_type, param_name, example_img, item_id))

        conn.commit()
        conn.close()

        logger.info(f"用户 {username} 更新质检项: {item_name} (ID: {item_id})")

        return jsonify({
            'success': True,
            'data': {
                'message': '质检项更新成功'
            }
        })
    except Exception as e:
        logger.error(f"更新质检项失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新质检项失败: {str(e)}'
        }), 500

@app.route('/api/quality/items/delete/<int:item_id>', methods=['DELETE'])
def delete_quality_item(item_id):
    """删除质检项"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查质检项是否存在
        cursor.execute('SELECT item_name FROM quality_item_dict WHERE id = ?', (item_id,))
        result = cursor.fetchone()
        if not result:
            conn.close()
            return jsonify({'success': False, 'message': '质检项不存在'}), 404

        item_name = result['item_name']

        # 删除质检项
        cursor.execute('DELETE FROM quality_item_dict WHERE id = ?', (item_id,))
        conn.commit()
        conn.close()

        logger.info(f"用户 {username} 删除质检项: {item_name} (ID: {item_id})")

        return jsonify({
            'success': True,
            'data': {
                'message': '质检项删除成功'
            }
        })
    except Exception as e:
        logger.error(f"删除质检项失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除质检项失败: {str(e)}'
        }), 500

@app.route('/api/quality/config/upload', methods=['POST'])
def upload_quality_config():
    """上传质检配置文件"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400

        # 检查文件类型
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            return jsonify({'success': False, 'message': '只支持Excel格式文件'}), 400

        # 保存上传的文件
        upload_dir = os.path.join(TEMP_DIR, 'quality_uploads')
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        timestamp = int(time.time())
        random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))
        filename = f"config_{timestamp}_{random_suffix}.xlsx"
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)

        # 解析Excel文件
        try:
            from openpyxl import load_workbook
            wb = load_workbook(file_path)
            ws = wb.active

            # 解析配置数据
            items = []
            headers = []

            # 读取表头
            for col in range(1, ws.max_column + 1):
                header = ws.cell(row=1, column=col).value
                if header:
                    headers.append(header)

            # 读取数据行
            for row in range(2, ws.max_row + 1):
                row_data = {}
                for col, header in enumerate(headers, 1):
                    cell_value = ws.cell(row=row, column=col).value
                    row_data[header] = cell_value

                # 只处理启用的质检项
                if row_data.get('是否启用') in ['是', 'Y', 'YES', True, 1, '1']:
                    items.append({
                        'level1_name': row_data.get('一级分类', ''),
                        'level2_name': row_data.get('二级分类', ''),
                        'item_name': row_data.get('质检项名称', ''),
                        'object_type': row_data.get('对象类型', ''),
                        'item_code': row_data.get('质检项代码', ''),
                        'rule_content': row_data.get('规则内容', ''),
                        'param_type': row_data.get('参数类型', ''),
                        'param_name': row_data.get('参数名称', ''),
                        'param_value': row_data.get('参数值', '')
                    })

            # 生成配置名称
            config_name = f"上传配置_{timestamp}"

            return jsonify({
                'success': True,
                'data': {
                    'name': config_name,
                    'items': items,
                    'file_path': file_path
                }
            })

        except Exception as parse_error:
            logger.error(f"解析配置文件失败: {str(parse_error)}")
            return jsonify({
                'success': False,
                'message': f'配置文件格式错误: {str(parse_error)}'
            }), 400

    except Exception as e:
        logger.error(f"上传质检配置文件失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'上传配置文件失败: {str(e)}'
        }), 500

@app.route('/api/quality/tools/run_records', methods=['POST'])
def get_quality_run_records():
    """获取质检工具运行记录"""
    try:
        data = request.get_json()
        fmw_id = data.get('fmw_id', 'quality')  # 默认为quality
        username = data.get('username')
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))

        if not username:
            return jsonify({'success': False, 'message': '缺少用户名参数'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询总数
        cursor.execute('''
            SELECT COUNT(*) FROM task_records_all
            WHERE submitter = ? AND tool_name LIKE '%质检%'
        ''', (username,))
        total = cursor.fetchone()[0]

        # 查询记录
        cursor.execute('''
            SELECT task_id, tool_name, project, submitter, submit_time,
                   task_name, status, time_consuming, file_size, file_name, up_nums, error_message
            FROM task_records_all
            WHERE submitter = ? AND tool_name LIKE '%质检%'
            ORDER BY submit_time DESC
            LIMIT ? OFFSET ?
        ''', (username, page_size, offset))

        records = cursor.fetchall()
        conn.close()

        # 格式化返回数据
        result = []
        for record in records:
            result.append({
                'task_id': record[0],
                'tool_name': record[1],
                'project': record[2],
                'submitter': record[3],
                'submit_time': record[4],
                'task_name': record[5],
                'status': record[6],
                'time_consuming': record[7],
                'file_size': record[8],
                'file_name': record[9],
                'up_nums': record[10],
                'error_message': record[11]
            })

        return jsonify({
            'success': True,
            'data': {
                'records': result,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        })
    except Exception as e:
        logger.error(f"获取质检运行记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取运行记录失败: {str(e)}'
        }), 500

@app.route('/api/quality/tools/delete_result', methods=['POST'])
def delete_quality_result():
    """删除质检结果"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')

        if not task_id:
            return jsonify({'success': False, 'message': '缺少任务ID'}), 400

        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查任务是否存在且属于当前用户
        cursor.execute('''
            SELECT submitter FROM task_records_all
            WHERE task_id = ? AND tool_name LIKE '%质检%'
        ''', (task_id,))
        record = cursor.fetchone()

        if not record:
            conn.close()
            return jsonify({'success': False, 'message': '任务不存在'}), 404

        if record[0] != username:
            conn.close()
            return jsonify({'success': False, 'message': '无权限删除此任务'}), 403

        # 删除数据库记录
        cursor.execute('DELETE FROM task_records_all WHERE task_id = ?', (task_id,))
        cursor.execute('DELETE FROM task_records WHERE task_id = ?', (task_id,))

        # 删除Redis中的任务状态
        if redis_manager.is_connected():
            redis_manager.client.delete(f'task_status:{task_id}')

        # 删除文件系统中的结果文件
        try:
            # 构建结果文件路径
            result_dir = os.path.join(current_dir, 'temp', task_id)
            if os.path.exists(result_dir):
                shutil.rmtree(result_dir)
                logger.info(f"删除结果目录: {result_dir}")
        except Exception as e:
            logger.warning(f"删除结果文件失败: {str(e)}")

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '删除成功'})
    except Exception as e:
        logger.error(f"删除质检结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@app.route('/api/quality/task/submit', methods=['POST'])
def submit_quality_task():
    """提交质检任务"""
    try:
        # 获取当前用户
        username = request.headers.get('X-Username')
        if not username:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        data = request.get_json()
        config_id = data.get('config_id')
        license_id = data.get('license_id')
        
        if not all([config_id, license_id]):
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 这里应该调用FME模型执行质检任务
        # 暂时返回成功响应
        task_id = f"quality_{int(time.time())}_{random.randint(1000, 9999)}"
        
        return jsonify({
            'success': True,
            'data': {
                'task_id': task_id,
                'message': '质检任务提交成功'
            }
        })
    except Exception as e:
        logger.error(f"提交质检任务失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交质检任务失败: {str(e)}'
        }), 500

# ==================== 质检配置管理API ====================

@app.route('/api/quality/configs', methods=['GET'])
def get_quality_configs_list():
    """获取质检配置列表"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求质检配置列表")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询配置列表，包含质检项数量
        cursor.execute('''
        SELECT
            qc.id,
            qc.name,
            qc.description,
            qc.gdb_path,
            qc.created_by,
            qc.created_at,
            qc.updated_at,
            qc.gdb_filename,
            COUNT(qci.id) as item_count
        FROM quality_config qc
        LEFT JOIN quality_config_items qci ON qc.id = qci.config_id
        GROUP BY qc.id, qc.name, qc.description, qc.gdb_path, qc.created_by, qc.created_at, qc.updated_at, qc.gdb_filename
        ORDER BY qc.updated_at DESC
        ''')

        configs = []
        for row in cursor.fetchall():
            configs.append({
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'gdb_path': row[3],
                'created_by': row[4],
                'created_at': row[5],
                'updated_at': row[6],
                'gdb_filename': row[7],
                'item_count': row[8]
            })

        conn.close()
        logger.info(f"返回 {len(configs)} 个质检配置")
        return jsonify({"success": True, "data": configs})

    except Exception as e:
        logger.error(f"获取质检配置列表失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取配置列表失败: {str(e)}"}), 500

@app.route('/api/quality/configs/by-gdb/<gdb_filename>', methods=['GET'])
def get_quality_configs_by_gdb(gdb_filename):
    """根据GDB文件名获取相关的质检配置列表"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求GDB文件 {gdb_filename} 的质检配置列表")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询所有配置，但优先显示匹配的GDB文件配置
        cursor.execute('''
        SELECT
            qc.id,
            qc.name,
            qc.description,
            qc.gdb_path,
            qc.created_by,
            qc.created_at,
            qc.updated_at,
            qc.gdb_filename,
            COUNT(qci.id) as item_count
        FROM quality_config qc
        LEFT JOIN quality_config_items qci ON qc.id = qci.config_id
        GROUP BY qc.id, qc.name, qc.description, qc.gdb_path, qc.created_by, qc.created_at, qc.updated_at, qc.gdb_filename
        ORDER BY
            CASE
                WHEN qc.gdb_filename = ? THEN 0
                WHEN qc.gdb_filename IS NULL OR qc.gdb_filename = '' THEN 1
                ELSE 2
            END,
            qc.updated_at DESC
        ''', (gdb_filename,))

        configs = []
        for row in cursor.fetchall():
            configs.append({
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'gdb_path': row[3],
                'created_by': row[4],
                'created_at': row[5],
                'updated_at': row[6],
                'gdb_filename': row[7],
                'item_count': row[8],
                'is_matched': row[7] == gdb_filename  # 标记是否完全匹配
            })

        conn.close()
        logger.info(f"返回 {len(configs)} 个质检配置（GDB文件: {gdb_filename}）")
        return jsonify({"success": True, "data": configs})

    except Exception as e:
        logger.error(f"获取GDB相关配置列表失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取配置列表失败: {str(e)}"}), 500

@app.route('/api/client/ip-info', methods=['GET'])
@app.route('/gsi/api/client/ip-info', methods=['GET'])
def get_client_ip_info():
    """获取客户端IP信息并判断是否为局域网"""
    try:
        # 获取客户端真实IP
        client_ip = get_client_ip()

        # 判断是否为局域网IP
        is_local_network = is_local_ip(client_ip)

        # 获取服务器IP信息
        server_ip = request.host.split(':')[0]

        # 检查外网访问权限设置
        allow_external_access = False
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute('''
            SELECT setting_value FROM navigation_settings
            WHERE setting_key = 'coordinate_external_access'
            ''')
            result = cursor.fetchone()
            conn.close()
            allow_external_access = result[0] == 'true' if result else False
        except Exception as e:
            logger.warning(f"获取外网访问权限设置失败: {str(e)}")

        # 判断是否允许访问坐标转换功能
        can_access_coordinate = is_local_network or allow_external_access

        return jsonify({
            "success": True,
            "data": {
                "client_ip": client_ip,
                "server_ip": server_ip,
                "is_local_network": is_local_network,
                "allow_external_access": allow_external_access,
                "can_access_coordinate": can_access_coordinate,
                "access_type": "局域网访问" if is_local_network else "外网访问"
            }
        })

    except Exception as e:
        logger.error(f"获取客户端IP信息失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取IP信息失败: {str(e)}"
        }), 500

def get_client_ip():
    """获取客户端真实IP地址"""
    # 检查代理头部
    if 'X-Forwarded-For' in request.headers:
        # X-Forwarded-For 可能包含多个IP，取第一个
        ip = request.headers['X-Forwarded-For'].split(',')[0].strip()
        if ip:
            return ip

    if 'X-Real-IP' in request.headers:
        ip = request.headers['X-Real-IP'].strip()
        if ip:
            return ip

    if 'X-Forwarded-Proto' in request.headers:
        # 有些代理会设置这个头部
        pass

    # 如果没有代理头部，使用直接连接的IP
    return request.remote_addr

def is_local_ip(ip):
    """判断IP是否为局域网地址"""
    import ipaddress

    try:
        ip_obj = ipaddress.ip_address(ip)

        # 检查是否为私有网络地址
        if ip_obj.is_private:
            return True

        # 检查是否为本地回环地址
        if ip_obj.is_loopback:
            return True

        # 检查是否为链路本地地址
        if ip_obj.is_link_local:
            return True

        # 特殊情况：检查常见的局域网段
        if isinstance(ip_obj, ipaddress.IPv4Address):
            # 10.0.0.0/8
            if ip_obj in ipaddress.IPv4Network('10.0.0.0/8'):
                return True
            # **********/12
            if ip_obj in ipaddress.IPv4Network('**********/12'):
                return True
            # ***********/16
            if ip_obj in ipaddress.IPv4Network('***********/16'):
                return True
            # *********/8 (localhost)
            if ip_obj in ipaddress.IPv4Network('*********/8'):
                return True

        return False

    except Exception as e:
        logger.warning(f"IP地址解析失败: {ip}, 错误: {str(e)}")
        # 如果解析失败，为了安全起见，假设是外网
        return False

@app.route('/api/settings/coordinate-permission', methods=['GET'])
@app.route('/gsi/api/settings/coordinate-permission', methods=['GET'])
def get_coordinate_permission():
    """获取坐标转换权限设置"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询坐标转换权限设置
        cursor.execute('''
        SELECT setting_value FROM navigation_settings
        WHERE setting_key = 'coordinate_external_access'
        ''')

        result = cursor.fetchone()
        conn.close()

        # 默认为false（不允许外网访问）
        allow_external = result[0] == 'true' if result else False

        return jsonify({
            "success": True,
            "data": {
                "allow_external_access": allow_external
            }
        })

    except Exception as e:
        logger.error(f"获取坐标转换权限设置失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取设置失败: {str(e)}"
        }), 500

@app.route('/api/settings/coordinate-permission', methods=['PUT'])
@app.route('/gsi/api/settings/coordinate-permission', methods=['PUT'])
def update_coordinate_permission():
    """更新坐标转换权限设置"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        allow_external = data.get('allow_external_access', False)

        logger.info(f"用户 {username} 更新坐标转换权限设置: {allow_external}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 更新或插入设置
        cursor.execute('''
        INSERT OR REPLACE INTO navigation_settings
        (setting_key, setting_value, updated_by, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', ('coordinate_external_access', 'true' if allow_external else 'false', username))

        conn.commit()
        conn.close()

        return jsonify({
            "success": True,
            "message": "坐标转换权限设置更新成功"
        })

    except Exception as e:
        logger.error(f"更新坐标转换权限设置失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"更新设置失败: {str(e)}"
        }), 500

@app.route('/api/quality/configs', methods=['POST'])
def create_quality_config_new():
    """创建新的质检配置"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        gdb_path = data.get('gdb_path', '').strip()
        gdb_filename = data.get('gdb_filename', '').strip()
        global_tolerance = data.get('global_tolerance', 0.001)

        if not name:
            return jsonify({"success": False, "message": "配置名称不能为空"}), 400

        logger.info(f"用户 {username} 创建质检配置: {name}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查在同一GDB文件下名称是否已存在
        cursor.execute('SELECT id FROM quality_config WHERE name = ? AND gdb_filename = ?', (name, gdb_filename))
        if cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": f"在GDB文件 '{gdb_filename}' 下已存在同名配置"}), 400

        # 获取当前服务器时间
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 将绝对路径转换为相对路径（相对于后端目录）
        relative_gdb_path = gdb_path
        if os.path.isabs(gdb_path):
            try:
                # 获取后端目录
                backend_dir = os.path.dirname(os.path.abspath(__file__))
                # 转换为相对路径
                relative_gdb_path = os.path.relpath(gdb_path, backend_dir)
                # 统一使用正斜杠
                relative_gdb_path = relative_gdb_path.replace('\\', '/')
                logger.info(f"转换路径: {gdb_path} -> {relative_gdb_path}")
            except ValueError as e:
                # 跨磁盘驱动器的情况，使用绝对路径
                logger.warning(f"路径转换失败（跨磁盘驱动器），使用绝对路径: {str(e)}")
                relative_gdb_path = gdb_path.replace('\\', '/')
            except Exception as e:
                logger.warning(f"路径转换失败，使用原路径: {str(e)}")
                relative_gdb_path = gdb_path

        # 插入新配置（兼容旧表结构）
        cursor.execute('''
        INSERT INTO quality_config (name, description, gdb_path, created_by, file_path, creator_id, creator_name, gdb_filename, global_tolerance, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, description, relative_gdb_path, username, '', 1, username, gdb_filename, global_tolerance, current_time, current_time))

        config_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"质检配置创建成功，ID: {config_id}")
        return jsonify({
            "success": True,
            "data": {
                "id": config_id,
                "name": name,
                "description": description,
                "gdb_path": gdb_path
            }
        })

    except Exception as e:
        logger.error(f"创建质检配置失败: {str(e)}")
        return jsonify({"success": False, "message": f"创建配置失败: {str(e)}"}), 500

@app.route('/api/quality/configs/copy', methods=['POST'])
def copy_quality_config():
    """创建质检配置副本"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        source_config_id = data.get('source_config_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        gdb_path = data.get('gdb_path', '').strip()
        gdb_filename = data.get('gdb_filename', '').strip()

        if not source_config_id:
            return jsonify({"success": False, "message": "缺少源配置ID"}), 400

        if not name:
            return jsonify({"success": False, "message": "配置名称不能为空"}), 400

        logger.info(f"用户 {username} 创建配置副本: {name} (源配置ID: {source_config_id})")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查源配置是否存在
        cursor.execute('SELECT * FROM quality_config WHERE id = ?', (source_config_id,))
        source_config = cursor.fetchone()
        if not source_config:
            conn.close()
            return jsonify({"success": False, "message": "源配置不存在"}), 404

        # 检查在同一GDB文件下名称是否已存在
        cursor.execute('SELECT id FROM quality_config WHERE name = ? AND gdb_filename = ?', (name, gdb_filename))
        if cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": f"在GDB文件 '{gdb_filename}' 下已存在同名配置"}), 400

        # 获取当前服务器时间
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 将绝对路径转换为相对路径（相对于后端目录）
        relative_gdb_path = gdb_path
        if os.path.isabs(gdb_path):
            try:
                # 获取后端目录
                backend_dir = os.path.dirname(os.path.abspath(__file__))
                # 转换为相对路径
                relative_gdb_path = os.path.relpath(gdb_path, backend_dir)
                # 统一使用正斜杠
                relative_gdb_path = relative_gdb_path.replace('\\', '/')
                logger.info(f"转换路径: {gdb_path} -> {relative_gdb_path}")
            except ValueError as e:
                # 跨磁盘驱动器的情况，使用绝对路径
                logger.warning(f"路径转换失败（跨磁盘驱动器），使用绝对路径: {str(e)}")
                relative_gdb_path = gdb_path.replace('\\', '/')
            except Exception as e:
                logger.warning(f"路径转换失败，使用原路径: {str(e)}")
                relative_gdb_path = gdb_path

        # 创建配置副本
        cursor.execute('''
        INSERT INTO quality_config (name, description, gdb_path, created_by, file_path, creator_id, creator_name, gdb_filename, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, description, relative_gdb_path, username, '', 1, username, gdb_filename, current_time, current_time))

        new_config_id = cursor.lastrowid

        # 复制源配置的质检项
        cursor.execute('SELECT * FROM quality_config_items WHERE config_id = ?', (source_config_id,))
        source_items = cursor.fetchall()

        for item in source_items:
            cursor.execute('''
            INSERT INTO quality_config_items
            (config_id, item_id, item_name, level1_name, level2_name, object_type, param_value, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (new_config_id, item[2], item[3], item[4], item[5], item[6], item[7], current_time, current_time))

        conn.commit()
        conn.close()

        logger.info(f"配置副本创建成功，ID: {new_config_id}，复制了 {len(source_items)} 个质检项")
        return jsonify({
            "success": True,
            "message": "配置副本创建成功",
            "data": {"id": new_config_id}
        })

    except Exception as e:
        logger.error(f"创建配置副本失败: {str(e)}")
        return jsonify({"success": False, "message": f"创建配置副本失败: {str(e)}"}), 500

@app.route('/api/quality/template-upload', methods=['POST'])
def upload_quality_template():
    """上传质检模板文件"""
    try:
        username = request.headers.get('X-Username', 'unknown')

        if 'file' not in request.files:
            return jsonify({"success": False, "message": "没有上传文件"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "没有选择文件"}), 400

        # 检查文件类型
        allowed_extensions = {'.gdb', '.zip'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({"success": False, "message": "只支持 .gdb 或 .zip 格式的文件"}), 400

        # 创建模板存储目录
        template_dir = os.path.join(TEMP_DIR, 'templates')
        os.makedirs(template_dir, exist_ok=True)

        # 生成唯一文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"template_{timestamp}_{file.filename}"
        file_path = os.path.join(template_dir, unique_filename)

        # 保存文件
        file.save(file_path)

        # 转换为相对路径，处理跨磁盘驱动器的情况
        try:
            relative_path = os.path.relpath(file_path, os.path.dirname(os.path.abspath(__file__)))
            relative_path = relative_path.replace('\\', '/')
        except ValueError as e:
            # 跨磁盘驱动器的情况，使用绝对路径
            logger.warning(f"无法计算相对路径（跨磁盘驱动器），使用绝对路径: {str(e)}")
            relative_path = file_path.replace('\\', '/')

        logger.info(f"用户 {username} 上传模板文件: {file.filename} -> {relative_path}")

        return jsonify({
            "success": True,
            "message": "模板文件上传成功",
            "data": {
                "file_path": relative_path,
                "original_name": file.filename,
                "file_size": os.path.getsize(file_path)
            }
        })

    except Exception as e:
        logger.error(f"上传模板文件失败: {str(e)}")
        return jsonify({"success": False, "message": f"上传模板文件失败: {str(e)}"}), 500

@app.route('/api/quality/coordinate-systems', methods=['GET'])
def get_coordinate_systems():
    """获取坐标系列表"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT id, name, description, epsg_code
        FROM coordinate_systems
        WHERE is_active = 1
        ORDER BY name
        ''')

        coordinate_systems = []
        for row in cursor.fetchall():
            coordinate_systems.append({
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'epsg_code': row[3],
                'label': f"{row[1]} ({row[3]})" if row[3] else row[1],
                'value': row[3] if row[3] else row[1]
            })

        conn.close()

        return jsonify({
            "success": True,
            "data": coordinate_systems
        })

    except Exception as e:
        logger.error(f"获取坐标系列表失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取坐标系列表失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>', methods=['GET'])
def get_quality_config_detail_new(config_id):
    """获取质检配置详情"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求质检配置详情: {config_id}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询配置基本信息
        cursor.execute('''
        SELECT id, name, description, gdb_path, created_by, created_at, updated_at, global_tolerance
        FROM quality_config WHERE id = ?
        ''', (config_id,))

        config_row = cursor.fetchone()
        if not config_row:
            conn.close()
            return jsonify({"success": False, "message": "配置不存在"}), 404

        # 查询配置项
        cursor.execute('''
        SELECT
            qci.id,
            qci.item_id,
            qci.selected_layers,
            qci.parameters,
            qci.source_layers,
            qci.target_layers,
            qid.item_name,
            qid.item_code,
            qid.object_type,
            qid.rule_content,
            qid.param_name,
            qid.param_type
        FROM quality_config_items qci
        JOIN quality_item_dict qid ON qci.item_id = qid.id
        WHERE qci.config_id = ?
        ORDER BY qci.id
        ''', (config_id,))

        items = []
        for row in cursor.fetchall():
            items.append({
                'id': row[0],
                'item_id': row[1],
                'selected_layers': json.loads(row[2]) if row[2] else [],
                'parameters': json.loads(row[3]) if row[3] else {},
                'source_layers': json.loads(row[4]) if row[4] else [],
                'target_layers': json.loads(row[5]) if row[5] else [],
                'item_name': row[6],
                'item_code': row[7],
                'object_type': row[8],
                'rule_content': row[9],
                'param_name': row[10],
                'param_type': row[11]
            })

        config = {
            'id': config_row[0],
            'name': config_row[1],
            'description': config_row[2],
            'gdb_path': config_row[3],
            'created_by': config_row[4],
            'created_at': config_row[5],
            'updated_at': config_row[6],
            'global_tolerance': config_row[7] if len(config_row) > 7 else 0.001,
            'items': items
        }

        conn.close()
        logger.info(f"返回质检配置详情，包含 {len(items)} 个质检项")
        return jsonify({"success": True, "data": config})

    except Exception as e:
        logger.error(f"获取质检配置详情失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取配置详情失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>', methods=['DELETE'])
def delete_quality_config_new(config_id):
    """删除质检配置"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 删除质检配置: {config_id}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查配置是否存在
        cursor.execute('SELECT name FROM quality_config WHERE id = ?', (config_id,))
        config = cursor.fetchone()
        if not config:
            conn.close()
            return jsonify({"success": False, "message": "配置不存在"}), 404

        # 删除配置项
        cursor.execute('DELETE FROM quality_config_items WHERE config_id = ?', (config_id,))

        # 删除配置
        cursor.execute('DELETE FROM quality_config WHERE id = ?', (config_id,))

        conn.commit()
        conn.close()

        logger.info(f"质检配置删除成功: {config[0]}")
        return jsonify({"success": True, "message": "配置删除成功"})

    except Exception as e:
        logger.error(f"删除质检配置失败: {str(e)}")
        return jsonify({"success": False, "message": f"删除配置失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>/items/<int:item_id>', methods=['DELETE'])
def remove_config_item_new(config_id, item_id):
    """从配置中移除质检项"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 从配置 {config_id} 中移除质检项 {item_id}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 删除配置项
        cursor.execute('DELETE FROM quality_config_items WHERE id = ? AND config_id = ?', (item_id, config_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"success": False, "message": "配置项不存在"}), 404

        conn.commit()
        conn.close()

        logger.info(f"质检项移除成功")
        return jsonify({"success": True, "message": "质检项移除成功"})

    except Exception as e:
        logger.error(f"移除质检项失败: {str(e)}")
        return jsonify({"success": False, "message": f"移除质检项失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>/items', methods=['POST'])
def add_quality_config_item_new(config_id):
    """添加质检项到配置"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        item_id = data.get('item_id')
        selected_layers = data.get('selected_layers', [])
        parameters = data.get('parameters', {})
        source_layers = data.get('source_layers', [])
        target_layers = data.get('target_layers', [])

        if not item_id:
            return jsonify({"success": False, "message": "质检项ID不能为空"}), 400

        logger.info(f"用户 {username} 向配置 {config_id} 添加质检项 {item_id}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 验证配置是否存在
        cursor.execute('SELECT id FROM quality_config WHERE id = ?', (config_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": "配置不存在"}), 404

        # 验证质检项是否存在
        cursor.execute('SELECT id FROM quality_item_dict WHERE id = ?', (item_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": "质检项不存在"}), 404

        # 允许重复添加同一质检项（用于不同图层组合的检查）
        logger.info(f"允许重复添加质检项 {item_id} 到配置 {config_id}，用于不同图层组合的检查")

        # 添加配置项
        cursor.execute('''
        INSERT INTO quality_config_items
        (config_id, item_id, selected_layers, parameters, source_layers, target_layers)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            config_id, item_id,
            json.dumps(selected_layers),
            json.dumps(parameters),
            json.dumps(source_layers),
            json.dumps(target_layers)
        ))

        # 更新配置的修改时间（使用服务器时间）
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
        UPDATE quality_config
        SET updated_at = ?
        WHERE id = ?
        ''', (current_time, config_id))

        conn.commit()
        conn.close()

        logger.info(f"质检项添加成功")
        return jsonify({"success": True, "message": "质检项添加成功"})

    except Exception as e:
        logger.error(f"添加质检项失败: {str(e)}")
        return jsonify({"success": False, "message": f"添加质检项失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>/items/<int:item_id>', methods=['PUT'])
def update_quality_config_item_new(config_id, item_id):
    """更新质检配置项"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        selected_layers = data.get('selected_layers', [])
        parameters = data.get('parameters', {})
        source_layers = data.get('source_layers', [])
        target_layers = data.get('target_layers', [])

        logger.info(f"用户 {username} 更新配置 {config_id} 的质检项 {item_id}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 验证配置项是否存在
        cursor.execute('''
        SELECT id FROM quality_config_items
        WHERE config_id = ? AND id = ?
        ''', (config_id, item_id))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": "配置项不存在"}), 404

        # 更新配置项
        cursor.execute('''
        UPDATE quality_config_items
        SET selected_layers = ?, parameters = ?, source_layers = ?, target_layers = ?
        WHERE config_id = ? AND id = ?
        ''', (
            json.dumps(selected_layers),
            json.dumps(parameters),
            json.dumps(source_layers),
            json.dumps(target_layers),
            config_id,
            item_id
        ))

        # 更新配置的修改时间（使用服务器时间）
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
        UPDATE quality_config
        SET updated_at = ?
        WHERE id = ?
        ''', (current_time, config_id))

        conn.commit()
        conn.close()

        logger.info(f"质检项更新成功")
        return jsonify({"success": True, "message": "配置项更新成功"})

    except Exception as e:
        logger.error(f"更新质检项失败: {str(e)}")
        return jsonify({"success": False, "message": f"更新质检项失败: {str(e)}"}), 500

@app.route('/api/quality/configs/<int:config_id>', methods=['PUT'])
def update_quality_config_new(config_id):
    """更新质检配置"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        data = request.get_json()

        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        gdb_path = data.get('gdb_path', '').strip()

        if not name:
            return jsonify({"success": False, "message": "配置名称不能为空"}), 400

        logger.info(f"用户 {username} 更新质检配置 {config_id}: {name}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 验证配置是否存在
        cursor.execute('SELECT id FROM quality_config WHERE id = ?', (config_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"success": False, "message": "配置不存在"}), 404

        # 更新配置（使用服务器时间）
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute('''
        UPDATE quality_config
        SET name = ?, description = ?, gdb_path = ?, updated_at = ?
        WHERE id = ?
        ''', (name, description, gdb_path, current_time, config_id))

        conn.commit()
        conn.close()

        logger.info(f"质检配置更新成功")
        return jsonify({"success": True, "message": "配置更新成功"})

    except Exception as e:
        logger.error(f"更新质检配置失败: {str(e)}")
        return jsonify({"success": False, "message": f"更新质检配置失败: {str(e)}"}), 500

@app.route('/api/quality/history-gdb-files', methods=['GET'])
def get_history_gdb_files():
    """获取历史上传的GDB文件列表"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求历史GDB文件列表")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查新的历史表是否存在
        cursor.execute('''
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='gdb_upload_history'
        ''')

        if not cursor.fetchone():
            # 表不存在，返回空列表
            conn.close()
            logger.info("gdb_upload_history表不存在，返回空列表")
            return jsonify({"success": True, "data": []})

        # 查询历史GDB文件
        cursor.execute('''
        SELECT
            stored_path as path,
            COALESCE(unique_filename, original_filename) as name,
            original_filename as original_name,
            upload_time,
            databases_count,
            total_layers,
            total_features,
            file_size,
            uploaded_by,
            databases_info,
            layers_info
        FROM gdb_upload_history
        WHERE status = 'completed'
        ORDER BY upload_time DESC
        ''')

        files = []
        for row in cursor.fetchall():
            file_info = {
                'path': row[0],
                'name': row[1],
                'original_name': row[2],
                'upload_time': row[3],
                'databases_count': row[4] or 0,
                'total_layers': row[5] or 0,
                'total_features': row[6] or 0,
                'file_size': row[7] or 0,
                'uploaded_by': row[8] or 'unknown',
                'has_cached_info': bool(row[9] and row[10])  # 是否有缓存的解析信息
            }

            # 如果有缓存信息，添加数据库名称列表（用于显示）
            if row[9]:  # databases_info
                try:
                    import json
                    databases_info = json.loads(row[9])
                    file_info['database_names'] = [db.get('name', '') for db in databases_info]
                except:
                    file_info['database_names'] = []
            else:
                file_info['database_names'] = []

            files.append(file_info)

        conn.close()

        logger.info(f"返回 {len(files)} 个历史GDB文件")
        return jsonify({"success": True, "data": files})

    except Exception as e:
        logger.error(f"获取历史GDB文件失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取历史文件失败: {str(e)}"}), 500

@app.route('/api/quality/history-gdb-files/<path:file_path>', methods=['DELETE'])
def delete_history_gdb_file(file_path):
    """删除历史GDB文件"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求删除历史GDB文件: {file_path}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查文件是否存在于历史记录中
        cursor.execute('''
        SELECT id, stored_path, original_filename FROM gdb_upload_history
        WHERE stored_path = ?
        ''', (file_path,))

        file_record = cursor.fetchone()
        if not file_record:
            conn.close()
            return jsonify({"success": False, "message": "文件记录不存在"}), 404

        # 删除物理文件
        try:
            # 将相对路径转换为绝对路径
            absolute_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
            absolute_file_path = os.path.normpath(absolute_file_path)

            if os.path.exists(absolute_file_path):
                if os.path.isdir(absolute_file_path):
                    import shutil
                    shutil.rmtree(absolute_file_path)
                else:
                    os.remove(absolute_file_path)
                logger.info(f"物理文件删除成功: {absolute_file_path}")
            else:
                logger.warning(f"物理文件不存在: {absolute_file_path}")
        except Exception as e:
            logger.error(f"删除物理文件失败: {str(e)}")
            # 继续删除数据库记录，即使物理文件删除失败

        # 删除数据库记录
        cursor.execute('DELETE FROM gdb_upload_history WHERE id = ?', (file_record[0],))
        conn.commit()
        conn.close()

        logger.info(f"历史GDB文件删除成功: {file_record[2]}")
        return jsonify({"success": True, "message": "文件删除成功"})

    except Exception as e:
        logger.error(f"删除历史GDB文件失败: {str(e)}")
        return jsonify({"success": False, "message": f"删除文件失败: {str(e)}"}), 500

@app.route('/api/quality/history-gdb-files/<path:file_path>/details', methods=['GET'])
def get_history_gdb_details(file_path):
    """获取历史GDB文件的详细信息（数据库和图层）"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求历史GDB文件详细信息: {file_path}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 查询历史记录中的详细信息
        cursor.execute('''
        SELECT databases_info, layers_info, original_filename, upload_time
        FROM gdb_upload_history
        WHERE stored_path = ? AND status = 'completed'
        ''', (file_path,))

        record = cursor.fetchone()
        if not record:
            conn.close()
            return jsonify({"success": False, "message": "历史记录不存在"}), 404

        if not record[0] or not record[1]:
            conn.close()
            return jsonify({"success": False, "message": "该文件没有缓存的解析信息，请重新解析"}), 404

        try:
            import json
            databases_info = json.loads(record[0])
            layers_info = json.loads(record[1])

            result = {
                'path': file_path,
                'name': record[2],
                'upload_time': record[3],
                'databases': databases_info,
                'layers': layers_info,
                'from_cache': True
            }

            conn.close()
            logger.info(f"返回历史GDB文件详细信息: {len(databases_info)} 个数据库, {len(layers_info)} 个图层")
            return jsonify({"success": True, "data": result})

        except Exception as e:
            conn.close()
            logger.error(f"解析缓存数据失败: {str(e)}")
            return jsonify({"success": False, "message": "解析缓存数据失败"}), 500

    except Exception as e:
        logger.error(f"获取历史GDB文件详细信息失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取详细信息失败: {str(e)}"}), 500

@app.route('/api/quality/gdb-info/<path:gdb_path>', methods=['GET'])
def get_gdb_info(gdb_path):
    """获取指定GDB文件的详细信息"""
    try:
        username = request.headers.get('X-Username', 'unknown')
        logger.info(f"用户 {username} 请求GDB文件信息: {gdb_path}")

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 首先尝试从上传历史中获取缓存的解析结果
        cursor.execute('''
        SELECT databases_info, layers_info, original_filename
        FROM gdb_upload_history
        WHERE stored_path = ? AND status = 'completed'
        ''', (gdb_path,))

        cached_result = cursor.fetchone()

        if cached_result and cached_result[0] and cached_result[1]:
            # 使用缓存的解析结果
            try:
                import json
                databases_info = json.loads(cached_result[0])
                layers_info = json.loads(cached_result[1])

                logger.info(f"使用缓存的GDB解析结果: {cached_result[2]}")

                result = {
                    'path': gdb_path,
                    'name': cached_result[2],
                    'databases': databases_info
                }

                conn.close()
                logger.info(f"返回缓存的GDB文件信息: {len(databases_info)} 个数据库")
                return jsonify({"success": True, "data": result})

            except Exception as e:
                logger.warning(f"解析缓存数据失败，将重新解析: {str(e)}")

        # 如果没有缓存或缓存解析失败，则从文件系统重新解析
        logger.info(f"缓存不可用，从文件系统解析GDB: {gdb_path}")

        # 将相对路径转换为绝对路径
        absolute_gdb_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), gdb_path)
        absolute_gdb_path = os.path.normpath(absolute_gdb_path)

        # 检查文件是否存在
        if not os.path.exists(absolute_gdb_path):
            conn.close()
            logger.warning(f"GDB路径不存在: {absolute_gdb_path}")
            return jsonify({"success": False, "message": "GDB文件不存在"}), 404

        # 查找GDB文件夹
        gdb_folders = []
        if os.path.isdir(absolute_gdb_path):
            # 如果是目录，查找其中的.gdb文件夹
            for root, dirs, files in os.walk(absolute_gdb_path):
                for d in dirs:
                    if d.lower().endswith('.gdb'):
                        gdb_folder_path = os.path.join(root, d)
                        # 检查是否包含.gdbtable文件
                        has_gdbtable = False
                        for _r, _ds, _fs in os.walk(gdb_folder_path):
                            for _f in _fs:
                                if _f.lower().endswith('.gdbtable'):
                                    has_gdbtable = True
                                    break
                            if has_gdbtable:
                                break
                        if has_gdbtable:
                            gdb_folders.append(gdb_folder_path)

        if not gdb_folders:
            conn.close()
            logger.warning(f"在路径中未找到有效的GDB文件夹: {absolute_gdb_path}")
            return jsonify({"success": False, "message": "未找到有效的GDB文件夹"}), 404

        # 解析所有GDB文件夹
        databases = []
        for gdb_folder_path in gdb_folders:
            layers = parse_gdb_layers(gdb_folder_path)
            database = {
                'name': os.path.basename(gdb_folder_path).replace('.gdb', ''),
                'path': gdb_folder_path,
                'layers': layers,
                'totalFeatures': sum(layer.get('feature_count', 0) for layer in layers)
            }
            databases.append(database)

        conn.close()

        result = {
            'path': gdb_path,
            'databases': databases
        }

        logger.info(f"返回GDB文件信息: {len(databases)} 个数据库")
        return jsonify({"success": True, "data": result})

    except Exception as e:
        logger.error(f"获取GDB文件信息失败: {str(e)}")
        return jsonify({"success": False, "message": f"获取文件信息失败: {str(e)}"}), 500

def generate_unique_filename(original_filename, uploaded_by):
    """生成唯一文件名，避免同名文件冲突"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # 检查是否存在同名文件
        cursor.execute('''
        SELECT COUNT(*) FROM gdb_upload_history
        WHERE original_filename = ? AND uploaded_by = ?
        ''', (original_filename, uploaded_by))

        count = cursor.fetchone()[0]
        conn.close()

        if count == 0:
            # 没有同名文件，直接使用原始文件名
            return original_filename
        else:
            # 有同名文件，添加序号
            base_name, ext = os.path.splitext(original_filename)
            return f"{base_name}_{count + 1}{ext}"

    except Exception as e:
        logger.error(f"生成唯一文件名失败: {str(e)}")
        # 出错时使用时间戳作为后缀
        import time
        timestamp = int(time.time())
        base_name, ext = os.path.splitext(original_filename)
        return f"{base_name}_{timestamp}{ext}"

def cleanup_redis():
    """清理Redis服务器进程"""
    try:
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'redis-server' in proc.info['name'].lower():
                    logger.info(f"终止Redis服务器进程: {proc.info['pid']}")
                    proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except Exception as e:
        logger.error(f"清理Redis服务器进程失败: {str(e)}")

def cleanup_tasks():
    """
    清理所有运行中和队列中的任务：
    1. 终止所有FME进程
    2. 更新数据库中的任务状态
    3. 清理Redis中的任务数据
    """
    try:
        logger.info("开始清理任务...")
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有运行中的任务
        cursor.execute('''
        SELECT task_id FROM task_records 
        WHERE status = 'running'
        ''')
        running_tasks = cursor.fetchall()

        # 更新所有运行中和队列中的任务状态为失败
        cursor.execute('''
        UPDATE task_records 
        SET status = 'failed',
            error_message = '后端关闭导致任务终止'
        WHERE status IN ('running', 'pending')
        ''')
        
        cursor.execute('''
        UPDATE task_records_all 
        SET status = 'failed',
            error_message = '后端关闭导致任务终止'
        WHERE status IN ('running', 'pending')
        ''')

        # 终止所有FME进程
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'fme' in proc.info['name'].lower():
                    logger.info(f"终止FME进程: {proc.info['pid']}")
                    proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        # 清理Redis中的任务数据
        if redis_manager.is_connected():
            try:
                task_status_keys = redis_manager.client.keys('task_status:*')
                if task_status_keys:
                    redis_manager.client.delete(*task_status_keys)
                    logger.info(f"已清空 {len(task_status_keys)} 个task_status记录")
            except Exception as e:
                logger.error(f"清空task_status数据时出错: {str(e)}")

        conn.commit()
        conn.close()
        logger.info("任务清理完成")
    except Exception as e:
        logger.error(f"清理任务时出错: {str(e)}")

# 添加全局清理标志
_cleanup_executed = False

def cleanup():
    """统一的清理函数"""
    global _cleanup_executed
    if _cleanup_executed:
        logger.info("清理流程已经执行过，跳过重复清理")
        return
        
    _cleanup_executed = True
    logger.info("开始执行清理流程...")
    cleanup_tasks()  # 先清理任务
    cleanup_redis()  # 再清理Redis
    logger.info("清理流程完成")

# 注册信号处理函数
def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，开始清理...")
    cleanup()  # 使用统一的清理函数
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 注册退出时的清理函数
atexit.register(cleanup)  # 使用统一的清理函数

def hide_console():
    """隐藏控制台窗口"""
    try:
        hwnd = win32gui.GetForegroundWindow()
        win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
    except Exception as e:
        logger.error(f"隐藏控制台窗口失败: {str(e)}")

# 启动Flask应用
if __name__ == '__main__':
    # 启动任务队列处理线程
    task_queue_thread = threading.Thread(target=process_task_queue, daemon=True)
    task_queue_thread.start()
    logger.info("任务处理队列已启动")

    # 初始化数据库
    try:
        init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        sys.exit(1)

    # 获取服务器配置信息
    server_host = SERVER_CONFIG.get('host', '0.0.0.0')
    server_port = SERVER_CONFIG.get('port')
    
    if server_port is None:
        logger.error("未在配置文件中找到服务器端口设置！")
        sys.exit(1)
    
    logger.info(f"从配置文件读取的服务器配置 - 主机: {server_host}, 端口: {server_port}")
    
    # 调试输出已加载的路由
    
    # 打印前端静态文件配置
    logger.info(f"静态文件配置信息:")
    logger.info(f"- 静态文件夹路径: {app.static_folder}")
    logger.info(f"- 静态文件URL路径: {app.static_url_path}")
    logger.info(f"- index.html是否存在: {os.path.exists(os.path.join(app.static_folder, 'index.html'))}")

    # 为所有API路由添加GSI版本
    add_gsi_routes()

    # 在单独的线程中启动Flask应用
    def run_flask():
        app.run(host=server_host, port=server_port, debug=False, use_reloader=False)
    
    flask_thread = threading.Thread(target=run_flask, daemon=True)
    flask_thread.start()
    logger.info(f"服务器启动于 http://{server_host}:{server_port}")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，正在关闭服务器...")
        # 这里可以添加清理代码
        sys.exit(0)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    try:
        # 检查Redis连接
        if not redis_manager.is_connected():
            return jsonify({
                'success': False,
                'message': 'Redis连接失败'
            }), 503

        # 检查数据库连接
        try:
            conn = get_db_connection()
            conn.close()
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'数据库连接失败: {str(e)}'
            }), 503

        return jsonify({
            'success': True,
            'message': '服务正常',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'健康检查失败: {str(e)}'
        }), 500

@app.route('/api/debug/static-info', methods=['GET'])
def debug_static_info():
    """用于调试静态文件配置的接口"""
    try:
        static_folder = app.static_folder
        static_url_path = app.static_url_path
        index_exists = os.path.exists(os.path.join(static_folder, 'index.html'))
        assets_dir_exists = os.path.exists(os.path.join(static_folder, 'assets'))
        
        # 列出静态目录中的文件
        static_files = []
        if os.path.exists(static_folder):
            for root, dirs, files in os.walk(static_folder):
                rel_path = os.path.relpath(root, static_folder)
                if rel_path == '.':
                    rel_path = ''
                for file in files:
                    file_path = os.path.join(rel_path, file)
                    static_files.append(file_path)
        
        return jsonify({
            'success': True,
            'data': {
                'static_folder': static_folder,
                'static_url_path': static_url_path,
                'index_exists': index_exists,
                'assets_dir_exists': assets_dir_exists,
                'static_files': static_files[:20],  # 限制返回数量
                'total_files': len(static_files)
            }
        })
    except Exception as e:
        logger.error(f"获取静态文件信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取静态文件信息失败: {str(e)}'
        }), 500













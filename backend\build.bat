@echo off
chcp 65001 >nul
echo ====================================================
echo GeoStream Integration 构建脚本
echo ====================================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

:: 检查必要的依赖
echo 检查依赖库...
python -c "import flask, flask_cors, redis, psutil, fiona" >nul 2>&1
if errorlevel 1 (
    echo 警告: 某些依赖库可能未安装
    echo 尝试安装依赖...
    pip install flask flask-cors redis psutil fiona
)

:: 清理之前的构建
if exist "dist" (
    echo 清理之前的构建文件...
    rmdir /s /q dist
)
if exist "build" (
    rmdir /s /q build
)

:: 选择构建方式
echo.
echo 请选择构建方式:
echo 1. 完整构建 (包含所有功能和检查)
echo 2. 快速构建 (简化版本)
echo.
set /p choice=请输入选择 (1 或 2): 

if "%choice%"=="1" (
    echo 开始完整构建...
    python build.py
) else if "%choice%"=="2" (
    echo 开始快速构建...
    python build_simple.py
) else (
    echo 无效选择，使用快速构建...
    python build_simple.py
)

:: 检查构建结果
if exist "dist\*.exe" (
    echo.
    echo ====================================================
    echo 构建成功！
    echo ====================================================
    echo 生成的文件位于 dist 目录中
    dir dist\*.exe
    echo.
    echo 是否要打开dist目录？(Y/N)
    set /p open_dir=
    if /i "%open_dir%"=="Y" (
        explorer dist
    )
) else (
    echo.
    echo ====================================================
    echo 构建失败！
    echo ====================================================
    echo 请检查错误信息并重试
)

echo.
pause

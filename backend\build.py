import PyInstaller.__main__
import os
import sys
import importlib

def check_dependencies():
    """检查必要的依赖库是否已安装"""
    required_libs = {
        'flask': 'Flask Web框架',
        'flask_cors': 'Flask CORS支持',
        'redis': 'Redis客户端',
        'psutil': '系统进程管理',
        'fiona': 'GDB文件读取 (推荐)',
        'PIL': '图像处理',
        'requests': 'HTTP请求库'
    }

    optional_libs = {
        'osgeo': 'GDAL/OGR库 (GDB解析备用)',
        'shapely': '几何处理库'
    }

    print("检查依赖库...")
    missing_required = []
    missing_optional = []

    for lib, desc in required_libs.items():
        try:
            importlib.import_module(lib)
            print(f"✓ {lib} - {desc}")
        except ImportError:
            print(f"✗ {lib} - {desc} (缺失)")
            missing_required.append(lib)

    for lib, desc in optional_libs.items():
        try:
            importlib.import_module(lib)
            print(f"✓ {lib} - {desc}")
        except ImportError:
            print(f"○ {lib} - {desc} (可选，未安装)")
            missing_optional.append(lib)

    if missing_required:
        print(f"\n错误: 缺少必要的依赖库: {', '.join(missing_required)}")
        print("请运行: pip install " + " ".join(missing_required))
        return False

    if missing_optional:
        print(f"\n警告: 缺少可选依赖库: {', '.join(missing_optional)}")
        print("这些库可以提供更好的GDB解析支持")

    print("\n依赖检查完成!")
    return True

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 需要打包的Python文件
py_files = [
    'app.py',
    'parse_fmw.py',
    'config_reader.py',
    'redis_utils.py',
    'redis_config.py',
    'config.py'
]

# 构建完整的文件路径
py_files = [os.path.join(current_dir, f) for f in py_files]

# PyInstaller参数
options = [
    'app.py',  # 主程序入口
    '--name=GeoStream IntegrationV1.0.0',  # 生成的exe名称
    '--onefile',  # 打包成单个exe文件
    '--clean',  # 清理临时文件
    '--icon=logo/favicon.ico',  # 设置打包图标
    '--add-data=config.xml;.',  # 添加配置文件
    '--add-data=redis/redis-server.exe;redis',  # 添加Redis服务器
    '--add-data=redis/redis.windows.conf;redis',  # 添加Redis配置文件
    # 添加GDAL数据文件（如果存在）
    '--collect-data=fiona',  # 收集fiona数据文件
    '--collect-data=osgeo',  # 收集GDAL数据文件
    '--hidden-import=win32api',  # 添加隐藏导入
    '--hidden-import=win32file',
    '--hidden-import=win32gui',
    '--hidden-import=win32con',
    '--hidden-import=psutil',
    '--hidden-import=redis',
    '--hidden-import=flask',
    '--hidden-import=flask_cors',
    '--hidden-import=sqlite3',
    '--hidden-import=xml.etree.ElementTree',
    '--hidden-import=zipfile',
    '--hidden-import=threading',
    '--hidden-import=signal',
    '--hidden-import=atexit',
    '--hidden-import=requests',
    '--hidden-import=cryptography',
    '--hidden-import=urllib',
    '--hidden-import=urllib.parse',
    '--hidden-import=urllib.request',
    '--hidden-import=urllib.error',
    '--hidden-import=urllib.response',
    '--hidden-import=PIL',
    '--hidden-import=PIL.Image',
    '--hidden-import=PIL.ImageDraw',
    '--hidden-import=PIL.ImageFont',
    '--hidden-import=PIL.ImageFilter',
    '--hidden-import=PIL.ImageEnhance',
    '--hidden-import=PIL.ImageOps',
    '--hidden-import=PIL.ImageTransform',
    '--hidden-import=PIL.ImageStat',
    '--hidden-import=PIL.ImagePalette',
    # GDB解析相关库
    '--hidden-import=fiona',
    '--hidden-import=fiona.crs',
    '--hidden-import=fiona.schema',
    '--hidden-import=fiona.collection',
    '--hidden-import=fiona.env',
    '--hidden-import=fiona.errors',
    '--hidden-import=osgeo',
    '--hidden-import=osgeo.ogr',
    '--hidden-import=osgeo.osr',
    '--hidden-import=osgeo.gdal',
    '--hidden-import=osgeo.gdalconst',
    '--hidden-import=shapely',
    '--hidden-import=shapely.geometry',
    '--hidden-import=shapely.wkt',
    '--hidden-import=shapely.ops',
    '--collect-all=redis',  # 收集所有redis相关模块
    '--collect-all=flask',  # 收集所有flask相关模块
    '--collect-all=flask_cors',  # 收集所有flask_cors相关模块
    '--collect-all=win32api',  # 收集所有win32api相关模块
    '--collect-all=psutil',  # 收集所有psutil相关模块
    '--collect-all=werkzeug',  # 收集所有werkzeug相关模块
    '--collect-all=cryptography',  # 收集所有cryptography相关模块
    '--collect-all=requests',  # 收集所有requests相关模块
    '--collect-all=PIL',  # 收集所有PIL相关模块
    '--collect-all=pillow',  # 收集所有pillow相关模块
    '--collect-all=fiona',  # 收集所有fiona相关模块
    '--collect-all=osgeo',  # 收集所有GDAL/OGR相关模块
    '--collect-all=shapely',  # 收集所有shapely相关模块
    '--exclude-module=dist',  # 排除不需要的目录
    '--exclude-module=models',
    '--exclude-module=redis',
    '--exclude-module=requirement_submissions',
    '--exclude-module=temp',
    '--exclude-module=GeoStream.db',
    # 优化选项
    '--noupx',  # 不使用UPX压缩，避免某些库的兼容性问题
    '--noconfirm',  # 不询问确认，直接覆盖
    # 添加运行时钩子 (如果存在)
    '--runtime-hook=runtime_hooks.py' if os.path.exists(os.path.join(current_dir, 'runtime_hooks.py')) else '',
]

# 过滤空字符串
options = [opt for opt in options if opt]

# 主执行逻辑
if __name__ == "__main__":
    print("=" * 60)
    print("GeoStream Integration 构建脚本")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    print("\n开始构建GeoStream Integration...")
    print("包含的GDB解析库:")
    print("- fiona (推荐)")
    print("- GDAL/OGR (备用)")
    print("- shapely (几何处理)")
    print()

    # 运行PyInstaller
    try:
        print("正在执行PyInstaller...")
        PyInstaller.__main__.run(options)
        print("\n" + "=" * 60)
        print("构建完成！")
        print("生成的可执行文件位于 dist/ 目录中")
        print("=" * 60)

        # 显示构建信息
        dist_dir = os.path.join(current_dir, 'dist')
        if os.path.exists(dist_dir):
            files = os.listdir(dist_dir)
            if files:
                print(f"\n生成的文件:")
                for file in files:
                    file_path = os.path.join(dist_dir, file)
                    if os.path.isfile(file_path):
                        size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                        print(f"  {file} ({size:.1f} MB)")

    except Exception as e:
        print("\n" + "=" * 60)
        print(f"构建失败: {str(e)}")
        print("请检查依赖库是否正确安装")
        print("=" * 60)
        sys.exit(1)
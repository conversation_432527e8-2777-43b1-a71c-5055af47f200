('E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
 'IntegrationV1.0.0\\GeoStream IntegrationV1.0.0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('runtime_hooks',
   'E:\\GeoStream_Integration\\frontend\\backend\\runtime_hooks.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('app', 'E:\\GeoStream_Integration\\frontend\\backend\\app.py', 'PYSOURCE'),
  ('redis\\redis-server.exe',
   'E:\\GeoStream_Integration\\frontend\\backend\\redis\\redis-server.exe',
   'BINARY'),
  ('Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\geos_c-f79418bf8e5cda2d1933e2121ee44e49.dll',
   'BINARY'),
  ('Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\geos-a5c01bdebd805679c9540218609cd4b8.dll',
   'BINARY'),
  ('Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Shapely.libs\\msvcp140-8a79f4687fc453279df1092923244d9e.dll',
   'BINARY'),
  ('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yaml\\_yaml.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\lib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geos.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('shapely\\_geometry_helpers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geometry_helpers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\ogrext.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('fiona\\_vsiopener.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vsiopener.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_transform.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_transform.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_geometry.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_geometry.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_err.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_err.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\_env.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_env.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmorph.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingmorph.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingft.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\_cffi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\backend_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32process.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32process.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('fiona\\schema.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\schema.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('fiona\\crs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\crs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python313\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('win32\\win32file.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32file.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('fiona.libs\\gdal-3105cd430966b2574784cc5837b520a5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\gdal-3105cd430966b2574784cc5837b520a5.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Python313\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('fiona.libs\\pcre2-8-d813e6f0e805eab6d374671dc234e1ae.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\pcre2-8-d813e6f0e805eab6d374671dc234e1ae.dll',
   'BINARY'),
  ('fiona.libs\\json-c-a79da3ad89939d4d14a3819a7df4dfce.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\json-c-a79da3ad89939d4d14a3819a7df4dfce.dll',
   'BINARY'),
  ('fiona.libs\\iconv-2-2651e8d0d0179faf57777d2c71481e65.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\iconv-2-2651e8d0d0179faf57777d2c71481e65.dll',
   'BINARY'),
  ('fiona.libs\\netcdf-c388c80124356fa9f445a498e2550455.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\netcdf-c388c80124356fa9f445a498e2550455.dll',
   'BINARY'),
  ('fiona.libs\\Lerc-d43d8ee2be34b5c69bcf4d853338bdc2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\Lerc-d43d8ee2be34b5c69bcf4d853338bdc2.dll',
   'BINARY'),
  ('fiona.libs\\sqlite3-0cb9f6e67fd2491114f55ace5268f7aa.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\sqlite3-0cb9f6e67fd2491114f55ace5268f7aa.dll',
   'BINARY'),
  ('fiona.libs\\minizip-fa015f03fd057686544d654bf2c4ed9f.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\minizip-fa015f03fd057686544d654bf2c4ed9f.dll',
   'BINARY'),
  ('fiona.libs\\libexpat-dd2f1ae151570af75ab8a932d0849f1d.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libexpat-dd2f1ae151570af75ab8a932d0849f1d.dll',
   'BINARY'),
  ('fiona.libs\\geos_c-4d7a52e330cb609ee4db6bd3c1f7e7b5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geos_c-4d7a52e330cb609ee4db6bd3c1f7e7b5.dll',
   'BINARY'),
  ('fiona.libs\\libssl-3-x64-********************************.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libssl-3-x64-********************************.dll',
   'BINARY'),
  ('fiona.libs\\libcrypto-3-x64-a9282680054934d0c6826e7539f4f82b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libcrypto-3-x64-a9282680054934d0c6826e7539f4f82b.dll',
   'BINARY'),
  ('fiona.libs\\msvcp140-80cac3ebc22ff38cfcdee60334932058.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\msvcp140-80cac3ebc22ff38cfcdee60334932058.dll',
   'BINARY'),
  ('fiona.libs\\hdf5-fa46ddbc0e6f5f04b4dfa68ebf54461b.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\hdf5-fa46ddbc0e6f5f04b4dfa68ebf54461b.dll',
   'BINARY'),
  ('fiona.libs\\tiff-b54f5da03716c60a719064c5995ad1e3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\tiff-b54f5da03716c60a719064c5995ad1e3.dll',
   'BINARY'),
  ('fiona.libs\\spatialite-e03c16976c81110a807ddde663a483fb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\spatialite-e03c16976c81110a807ddde663a483fb.dll',
   'BINARY'),
  ('fiona.libs\\zlib1-6bb9d46bd47056cba4611655f416dee7.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\zlib1-6bb9d46bd47056cba4611655f416dee7.dll',
   'BINARY'),
  ('fiona.libs\\gif-72f5a534bc37066175f18a3a5d84eeae.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\gif-72f5a534bc37066175f18a3a5d84eeae.dll',
   'BINARY'),
  ('fiona.libs\\libpq-6fb79053aa253f672cf825d175de737c.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libpq-6fb79053aa253f672cf825d175de737c.dll',
   'BINARY'),
  ('fiona.libs\\qhull_r-ca74792ac17586b5053948c65dfa8fb1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\qhull_r-ca74792ac17586b5053948c65dfa8fb1.dll',
   'BINARY'),
  ('fiona.libs\\openjp2-fdb0b878068e74b970c325ed55edc3c5.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\openjp2-fdb0b878068e74b970c325ed55edc3c5.dll',
   'BINARY'),
  ('fiona.libs\\libcurl-077ab0f5dc0e8b5f1ddd883462d826de.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libcurl-077ab0f5dc0e8b5f1ddd883462d826de.dll',
   'BINARY'),
  ('fiona.libs\\geotiff-8b5e2dc8882862915ed2d750ed5319da.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geotiff-8b5e2dc8882862915ed2d750ed5319da.dll',
   'BINARY'),
  ('fiona.libs\\libpng16-90b1d680f42f640415853ff0cd0b3490.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libpng16-90b1d680f42f640415853ff0cd0b3490.dll',
   'BINARY'),
  ('fiona.libs\\libwebp-37f6ef74c6b7f8a08b41c1dc3407cc63.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libwebp-37f6ef74c6b7f8a08b41c1dc3407cc63.dll',
   'BINARY'),
  ('fiona.libs\\libxml2-1dc62481c9c5a2c7dfb648c92ddebe81.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libxml2-1dc62481c9c5a2c7dfb648c92ddebe81.dll',
   'BINARY'),
  ('fiona.libs\\liblzma-20b935dffc1aa59c3a410e536b235e0a.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\liblzma-20b935dffc1aa59c3a410e536b235e0a.dll',
   'BINARY'),
  ('fiona.libs\\zstd-1f2b45f957344521af92666b31d73de1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\zstd-1f2b45f957344521af92666b31d73de1.dll',
   'BINARY'),
  ('fiona.libs\\jpeg62-b1a7291a72814164156a5130fdc64eef.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\jpeg62-b1a7291a72814164156a5130fdc64eef.dll',
   'BINARY'),
  ('fiona.libs\\proj-3f82123c6982a15ab288894283554e44.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\proj-3f82123c6982a15ab288894283554e44.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('fiona.libs\\hdf5_hl-222f136c52a099fe4b41216a1ded1bcc.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\hdf5_hl-222f136c52a099fe4b41216a1ded1bcc.dll',
   'BINARY'),
  ('fiona.libs\\geos-cf4fccef073a506b3207c8a036a9d063.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\geos-cf4fccef073a506b3207c8a036a9d063.dll',
   'BINARY'),
  ('fiona.libs\\szip-d0425ee11b05cea2ef5907b56ab33522.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\szip-d0425ee11b05cea2ef5907b56ab33522.dll',
   'BINARY'),
  ('fiona.libs\\freexl-1-3d1f53ba38e08cfcb484f86c84d51fbb.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\freexl-1-3d1f53ba38e08cfcb484f86c84d51fbb.dll',
   'BINARY'),
  ('fiona.libs\\libsharpyuv-cd7565f442be556cb3a5480d7bc360f8.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona.libs\\libsharpyuv-cd7565f442be556cb3a5480d7bc360f8.dll',
   'BINARY'),
  ('Flask-2.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('Flask-2.0.1.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\LICENSE.rst',
   'DATA'),
  ('Flask-2.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\METADATA',
   'DATA'),
  ('Flask-2.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\RECORD',
   'DATA'),
  ('Flask-2.0.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('Flask-2.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\WHEEL',
   'DATA'),
  ('Flask-2.0.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('Flask-2.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask-2.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\INSTALLER',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\LICENSE',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\METADATA',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\RECORD',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\REQUESTED',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\WHEEL',
   'DATA'),
  ('Flask_Cors-3.0.10.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Flask_Cors-3.0.10.dist-info\\top_level.txt',
   'DATA'),
  ('PIL\\AvifImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\AvifImagePlugin.py',
   'DATA'),
  ('PIL\\BdfFontFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BdfFontFile.py',
   'DATA'),
  ('PIL\\BlpImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BlpImagePlugin.py',
   'DATA'),
  ('PIL\\BmpImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BmpImagePlugin.py',
   'DATA'),
  ('PIL\\BufrStubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'DATA'),
  ('PIL\\ContainerIO.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ContainerIO.py',
   'DATA'),
  ('PIL\\CurImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\CurImagePlugin.py',
   'DATA'),
  ('PIL\\DcxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DcxImagePlugin.py',
   'DATA'),
  ('PIL\\DdsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\DdsImagePlugin.py',
   'DATA'),
  ('PIL\\EpsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\EpsImagePlugin.py',
   'DATA'),
  ('PIL\\ExifTags.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ExifTags.py',
   'DATA'),
  ('PIL\\FitsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FitsImagePlugin.py',
   'DATA'),
  ('PIL\\FliImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FliImagePlugin.py',
   'DATA'),
  ('PIL\\FontFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FontFile.py',
   'DATA'),
  ('PIL\\FpxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FpxImagePlugin.py',
   'DATA'),
  ('PIL\\FtexImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\FtexImagePlugin.py',
   'DATA'),
  ('PIL\\GbrImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GbrImagePlugin.py',
   'DATA'),
  ('PIL\\GdImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GdImageFile.py',
   'DATA'),
  ('PIL\\GifImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GifImagePlugin.py',
   'DATA'),
  ('PIL\\GimpGradientFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpGradientFile.py',
   'DATA'),
  ('PIL\\GimpPaletteFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GimpPaletteFile.py',
   'DATA'),
  ('PIL\\GribStubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\GribStubImagePlugin.py',
   'DATA'),
  ('PIL\\Hdf5StubImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'DATA'),
  ('PIL\\IcnsImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcnsImagePlugin.py',
   'DATA'),
  ('PIL\\IcoImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IcoImagePlugin.py',
   'DATA'),
  ('PIL\\ImImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImImagePlugin.py',
   'DATA'),
  ('PIL\\Image.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Image.py',
   'DATA'),
  ('PIL\\ImageChops.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageChops.py',
   'DATA'),
  ('PIL\\ImageCms.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageCms.py',
   'DATA'),
  ('PIL\\ImageColor.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageColor.py',
   'DATA'),
  ('PIL\\ImageDraw.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw.py',
   'DATA'),
  ('PIL\\ImageDraw2.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageDraw2.py',
   'DATA'),
  ('PIL\\ImageEnhance.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageEnhance.py',
   'DATA'),
  ('PIL\\ImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFile.py',
   'DATA'),
  ('PIL\\ImageFilter.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFilter.py',
   'DATA'),
  ('PIL\\ImageFont.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageFont.py',
   'DATA'),
  ('PIL\\ImageGrab.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageGrab.py',
   'DATA'),
  ('PIL\\ImageMath.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMath.py',
   'DATA'),
  ('PIL\\ImageMode.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMode.py',
   'DATA'),
  ('PIL\\ImageMorph.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageMorph.py',
   'DATA'),
  ('PIL\\ImageOps.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageOps.py',
   'DATA'),
  ('PIL\\ImagePalette.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePalette.py',
   'DATA'),
  ('PIL\\ImagePath.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImagePath.py',
   'DATA'),
  ('PIL\\ImageQt.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageQt.py',
   'DATA'),
  ('PIL\\ImageSequence.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageSequence.py',
   'DATA'),
  ('PIL\\ImageShow.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageShow.py',
   'DATA'),
  ('PIL\\ImageStat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageStat.py',
   'DATA'),
  ('PIL\\ImageTk.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageTk.py',
   'DATA'),
  ('PIL\\ImageTransform.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageTransform.py',
   'DATA'),
  ('PIL\\ImageWin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImageWin.py',
   'DATA'),
  ('PIL\\ImtImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\ImtImagePlugin.py',
   'DATA'),
  ('PIL\\IptcImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\IptcImagePlugin.py',
   'DATA'),
  ('PIL\\Jpeg2KImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'DATA'),
  ('PIL\\JpegImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegImagePlugin.py',
   'DATA'),
  ('PIL\\JpegPresets.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\JpegPresets.py',
   'DATA'),
  ('PIL\\McIdasImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\McIdasImagePlugin.py',
   'DATA'),
  ('PIL\\MicImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MicImagePlugin.py',
   'DATA'),
  ('PIL\\MpegImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpegImagePlugin.py',
   'DATA'),
  ('PIL\\MpoImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MpoImagePlugin.py',
   'DATA'),
  ('PIL\\MspImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\MspImagePlugin.py',
   'DATA'),
  ('PIL\\PSDraw.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PSDraw.py',
   'DATA'),
  ('PIL\\PaletteFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PaletteFile.py',
   'DATA'),
  ('PIL\\PalmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PalmImagePlugin.py',
   'DATA'),
  ('PIL\\PcdImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcdImagePlugin.py',
   'DATA'),
  ('PIL\\PcfFontFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcfFontFile.py',
   'DATA'),
  ('PIL\\PcxImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PcxImagePlugin.py',
   'DATA'),
  ('PIL\\PdfImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfImagePlugin.py',
   'DATA'),
  ('PIL\\PdfParser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PdfParser.py',
   'DATA'),
  ('PIL\\PixarImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PixarImagePlugin.py',
   'DATA'),
  ('PIL\\PngImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PngImagePlugin.py',
   'DATA'),
  ('PIL\\PpmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PpmImagePlugin.py',
   'DATA'),
  ('PIL\\PsdImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\PsdImagePlugin.py',
   'DATA'),
  ('PIL\\QoiImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\QoiImagePlugin.py',
   'DATA'),
  ('PIL\\SgiImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SgiImagePlugin.py',
   'DATA'),
  ('PIL\\SpiderImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SpiderImagePlugin.py',
   'DATA'),
  ('PIL\\SunImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\SunImagePlugin.py',
   'DATA'),
  ('PIL\\TarIO.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TarIO.py',
   'DATA'),
  ('PIL\\TgaImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TgaImagePlugin.py',
   'DATA'),
  ('PIL\\TiffImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffImagePlugin.py',
   'DATA'),
  ('PIL\\TiffTags.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\TiffTags.py',
   'DATA'),
  ('PIL\\WalImageFile.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WalImageFile.py',
   'DATA'),
  ('PIL\\WebPImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WebPImagePlugin.py',
   'DATA'),
  ('PIL\\WmfImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\WmfImagePlugin.py',
   'DATA'),
  ('PIL\\XVThumbImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'DATA'),
  ('PIL\\XbmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XbmImagePlugin.py',
   'DATA'),
  ('PIL\\XpmImagePlugin.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\XpmImagePlugin.py',
   'DATA'),
  ('PIL\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\__init__.py',
   'DATA'),
  ('PIL\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\__main__.py',
   'DATA'),
  ('PIL\\_avif.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_avif.pyi',
   'DATA'),
  ('PIL\\_binary.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_binary.py',
   'DATA'),
  ('PIL\\_deprecate.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_deprecate.py',
   'DATA'),
  ('PIL\\_imaging.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imaging.pyi',
   'DATA'),
  ('PIL\\_imagingcms.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingcms.pyi',
   'DATA'),
  ('PIL\\_imagingft.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingft.pyi',
   'DATA'),
  ('PIL\\_imagingmath.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingmath.pyi',
   'DATA'),
  ('PIL\\_imagingmorph.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingmorph.pyi',
   'DATA'),
  ('PIL\\_imagingtk.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_imagingtk.pyi',
   'DATA'),
  ('PIL\\_tkinter_finder.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_tkinter_finder.py',
   'DATA'),
  ('PIL\\_typing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_typing.py',
   'DATA'),
  ('PIL\\_util.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_util.py',
   'DATA'),
  ('PIL\\_version.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_version.py',
   'DATA'),
  ('PIL\\_webp.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\_webp.pyi',
   'DATA'),
  ('PIL\\features.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\features.py',
   'DATA'),
  ('PIL\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\py.typed',
   'DATA'),
  ('PIL\\report.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PIL\\report.py',
   'DATA'),
  ('config.xml',
   'E:\\GeoStream_Integration\\frontend\\backend\\config.xml',
   'DATA'),
  ('fiona-1.10.1.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('fiona-1.10.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\INSTALLER',
   'DATA'),
  ('fiona-1.10.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('fiona-1.10.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\METADATA',
   'DATA'),
  ('fiona-1.10.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\RECORD',
   'DATA'),
  ('fiona-1.10.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\REQUESTED',
   'DATA'),
  ('fiona-1.10.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\WHEEL',
   'DATA'),
  ('fiona-1.10.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\entry_points.txt',
   'DATA'),
  ('fiona-1.10.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona-1.10.1.dist-info\\top_level.txt',
   'DATA'),
  ('fiona\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\__init__.py',
   'DATA'),
  ('fiona\\_cpl.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_cpl.pxd',
   'DATA'),
  ('fiona\\_csl.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_csl.pxd',
   'DATA'),
  ('fiona\\_env.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_env.pxd',
   'DATA'),
  ('fiona\\_err.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_err.pxd',
   'DATA'),
  ('fiona\\_geometry.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_geometry.pxd',
   'DATA'),
  ('fiona\\_path.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_path.py',
   'DATA'),
  ('fiona\\_show_versions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_show_versions.py',
   'DATA'),
  ('fiona\\_vendor\\munch\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vendor\\munch\\__init__.py',
   'DATA'),
  ('fiona\\_vendor\\snuggs.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vendor\\snuggs.py',
   'DATA'),
  ('fiona\\_vsiopener.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\_vsiopener.pxd',
   'DATA'),
  ('fiona\\abc.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\abc.py',
   'DATA'),
  ('fiona\\collection.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\collection.py',
   'DATA'),
  ('fiona\\compat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\compat.py',
   'DATA'),
  ('fiona\\crs.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\crs.pxd',
   'DATA'),
  ('fiona\\drvsupport.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\drvsupport.py',
   'DATA'),
  ('fiona\\enums.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\enums.py',
   'DATA'),
  ('fiona\\env.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\env.py',
   'DATA'),
  ('fiona\\errors.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\errors.py',
   'DATA'),
  ('fiona\\features.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\features.py',
   'DATA'),
  ('fiona\\fio\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\__init__.py',
   'DATA'),
  ('fiona\\fio\\bounds.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\bounds.py',
   'DATA'),
  ('fiona\\fio\\calc.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\calc.py',
   'DATA'),
  ('fiona\\fio\\cat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\cat.py',
   'DATA'),
  ('fiona\\fio\\collect.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\collect.py',
   'DATA'),
  ('fiona\\fio\\distrib.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\distrib.py',
   'DATA'),
  ('fiona\\fio\\dump.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\dump.py',
   'DATA'),
  ('fiona\\fio\\env.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\env.py',
   'DATA'),
  ('fiona\\fio\\features.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\features.py',
   'DATA'),
  ('fiona\\fio\\helpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\helpers.py',
   'DATA'),
  ('fiona\\fio\\info.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\info.py',
   'DATA'),
  ('fiona\\fio\\insp.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\insp.py',
   'DATA'),
  ('fiona\\fio\\load.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\load.py',
   'DATA'),
  ('fiona\\fio\\ls.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\ls.py',
   'DATA'),
  ('fiona\\fio\\main.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\main.py',
   'DATA'),
  ('fiona\\fio\\options.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\options.py',
   'DATA'),
  ('fiona\\fio\\rm.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\fio\\rm.py',
   'DATA'),
  ('fiona\\gdal.pxi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal.pxi',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets-debug.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets-release.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDAL-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDAL-targets.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDALConfig.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALConfig.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDALConfigVersion.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALConfigVersion.cmake',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoBW.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoBW.svg',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoColor.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoColor.svg',
   'DATA'),
  ('fiona\\gdal_data\\GDALLogoGS.svg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\GDALLogoGS.svg',
   'DATA'),
  ('fiona\\gdal_data\\LICENSE.TXT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\LICENSE.TXT',
   'DATA'),
  ('fiona\\gdal_data\\MM_m_idofic.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\MM_m_idofic.csv',
   'DATA'),
  ('fiona\\gdal_data\\bag_template.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\bag_template.xml',
   'DATA'),
  ('fiona\\gdal_data\\copyright',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\copyright',
   'DATA'),
  ('fiona\\gdal_data\\cubewerx_extra.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\cubewerx_extra.wkt',
   'DATA'),
  ('fiona\\gdal_data\\default.rsc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\default.rsc',
   'DATA'),
  ('fiona\\gdal_data\\ecw_cs.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ecw_cs.wkt',
   'DATA'),
  ('fiona\\gdal_data\\eedaconf.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\eedaconf.json',
   'DATA'),
  ('fiona\\gdal_data\\epsg.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\epsg.wkt',
   'DATA'),
  ('fiona\\gdal_data\\esri_StatePlane_extra.wkt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\esri_StatePlane_extra.wkt',
   'DATA'),
  ('fiona\\gdal_data\\gdalicon.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalicon.png',
   'DATA'),
  ('fiona\\gdal_data\\gdalinfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalinfo_output.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\gdalmdiminfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalmdiminfo_output.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\gdaltileindex.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdaltileindex.xsd',
   'DATA'),
  ('fiona\\gdal_data\\gdalvrt.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gdalvrt.xsd',
   'DATA'),
  ('fiona\\gdal_data\\gfs.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gfs.xsd',
   'DATA'),
  ('fiona\\gdal_data\\gml_registry.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gml_registry.xml',
   'DATA'),
  ('fiona\\gdal_data\\gml_registry.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gml_registry.xsd',
   'DATA'),
  ('fiona\\gdal_data\\grib2_center.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_center.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_process.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_process.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_subcenter.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_subcenter.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_13.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_13.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_14.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_14.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_15.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_15.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_16.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_16.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_17.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_17.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_18.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_18.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_19.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_19.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_190.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_190.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_191.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_191.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_20.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_20.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_21.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_21.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_0_7.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_0_7.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_191.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_191.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_10_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_10_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_1_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_1_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_20_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_20_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_2_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_2_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_3_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_3_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_0.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_0.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_1.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_1.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_10.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_10.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_2.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_2.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_3.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_3.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_4.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_4.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_6.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_6.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_7.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_7.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_8.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_8.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_4_9.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_4_9.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_Canada.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_Canada.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_HPC.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_HPC.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_MRMS.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_MRMS.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_NCEP.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_NCEP.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_NDFD.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_NDFD.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_2_local_index.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_2_local_index.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_4_5.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_4_5.csv',
   'DATA'),
  ('fiona\\gdal_data\\grib2_table_versions.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\grib2_table_versions.csv',
   'DATA'),
  ('fiona\\gdal_data\\gt_datum.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gt_datum.csv',
   'DATA'),
  ('fiona\\gdal_data\\gt_ellips.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\gt_ellips.csv',
   'DATA'),
  ('fiona\\gdal_data\\header.dxf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\header.dxf',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_BasicPropertyUnit.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_BasicPropertyUnit.gfs',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralBoundary.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralBoundary.gfs',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralParcel.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralParcel.gfs',
   'DATA'),
  ('fiona\\gdal_data\\inspire_cp_CadastralZoning.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\inspire_cp_CadastralZoning.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_AdmPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_AdmPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_BldA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_BldA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_BldL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_BldL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_Cntr.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_Cntr.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_CommBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_CommBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_CommPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_CommPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_Cstline.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_Cstline.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_ElevPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_ElevPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_GCP.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_GCP.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_LeveeEdge.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_LeveeEdge.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RailCL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RailCL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdASL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdASL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdCompt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdCompt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdEdg.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdEdg.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdMgtBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdMgtBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RdSgmtA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RdSgmtA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_RvrMgtBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_RvrMgtBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBAPt.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBAPt.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBArea.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBArea.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_SBBdry.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_SBBdry.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WStrA.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WStrA.gfs',
   'DATA'),
  ('fiona\\gdal_data\\jpfgdgml_WStrL.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\jpfgdgml_WStrL.gfs',
   'DATA'),
  ('fiona\\gdal_data\\netcdf_config.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\netcdf_config.xsd',
   'DATA'),
  ('fiona\\gdal_data\\nitf_spec.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\nitf_spec.xml',
   'DATA'),
  ('fiona\\gdal_data\\nitf_spec.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\nitf_spec.xsd',
   'DATA'),
  ('fiona\\gdal_data\\ogrinfo_output.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ogrinfo_output.schema.json',
   'DATA'),
  ('fiona\\gdal_data\\ogrvrt.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ogrvrt.xsd',
   'DATA'),
  ('fiona\\gdal_data\\osmconf.ini',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\osmconf.ini',
   'DATA'),
  ('fiona\\gdal_data\\ozi_datum.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ozi_datum.csv',
   'DATA'),
  ('fiona\\gdal_data\\ozi_ellips.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ozi_ellips.csv',
   'DATA'),
  ('fiona\\gdal_data\\pci_datum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pci_datum.txt',
   'DATA'),
  ('fiona\\gdal_data\\pci_ellips.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pci_ellips.txt',
   'DATA'),
  ('fiona\\gdal_data\\pdfcomposition.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pdfcomposition.xsd',
   'DATA'),
  ('fiona\\gdal_data\\pds4_template.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\pds4_template.xml',
   'DATA'),
  ('fiona\\gdal_data\\plscenesconf.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\plscenesconf.json',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_ob_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_ob_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_st_uvoh_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_st_uvoh_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_st_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_st_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\ruian_vf_v1.gfs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\ruian_vf_v1.gfs',
   'DATA'),
  ('fiona\\gdal_data\\s57agencies.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57agencies.csv',
   'DATA'),
  ('fiona\\gdal_data\\s57attributes.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57attributes.csv',
   'DATA'),
  ('fiona\\gdal_data\\s57expectedinput.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57expectedinput.csv',
   'DATA'),
  ('fiona\\gdal_data\\s57objectclasses.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\s57objectclasses.csv',
   'DATA'),
  ('fiona\\gdal_data\\seed_2d.dgn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\seed_2d.dgn',
   'DATA'),
  ('fiona\\gdal_data\\seed_3d.dgn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\seed_3d.dgn',
   'DATA'),
  ('fiona\\gdal_data\\stateplane.csv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\stateplane.csv',
   'DATA'),
  ('fiona\\gdal_data\\tms_LINZAntarticaMapTileGrid.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_LINZAntarticaMapTileGrid.json',
   'DATA'),
  ('fiona\\gdal_data\\tms_MapML_APSTILE.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_MapML_APSTILE.json',
   'DATA'),
  ('fiona\\gdal_data\\tms_MapML_CBMTILE.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_MapML_CBMTILE.json',
   'DATA'),
  ('fiona\\gdal_data\\tms_NZTM2000.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\tms_NZTM2000.json',
   'DATA'),
  ('fiona\\gdal_data\\trailer.dxf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\trailer.dxf',
   'DATA'),
  ('fiona\\gdal_data\\usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\usage',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg-cmake-wrapper.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg-cmake-wrapper.cmake',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg.spdx.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg.spdx.json',
   'DATA'),
  ('fiona\\gdal_data\\vcpkg_abi_info.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vcpkg_abi_info.txt',
   'DATA'),
  ('fiona\\gdal_data\\vdv452.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vdv452.xml',
   'DATA'),
  ('fiona\\gdal_data\\vdv452.xsd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vdv452.xsd',
   'DATA'),
  ('fiona\\gdal_data\\vicar.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\gdal_data\\vicar.json',
   'DATA'),
  ('fiona\\inspector.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\inspector.py',
   'DATA'),
  ('fiona\\io.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\io.py',
   'DATA'),
  ('fiona\\logutils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\logutils.py',
   'DATA'),
  ('fiona\\meta.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\meta.py',
   'DATA'),
  ('fiona\\model.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\model.py',
   'DATA'),
  ('fiona\\ogrext1.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext1.pxd',
   'DATA'),
  ('fiona\\ogrext2.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext2.pxd',
   'DATA'),
  ('fiona\\ogrext3.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\ogrext3.pxd',
   'DATA'),
  ('fiona\\path.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\path.py',
   'DATA'),
  ('fiona\\proj_data\\CH',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\CH',
   'DATA'),
  ('fiona\\proj_data\\GL27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\GL27',
   'DATA'),
  ('fiona\\proj_data\\ITRF2000',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2000',
   'DATA'),
  ('fiona\\proj_data\\ITRF2008',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2008',
   'DATA'),
  ('fiona\\proj_data\\ITRF2014',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\ITRF2014',
   'DATA'),
  ('fiona\\proj_data\\copyright',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\copyright',
   'DATA'),
  ('fiona\\proj_data\\deformation_model.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\deformation_model.schema.json',
   'DATA'),
  ('fiona\\proj_data\\nad.lst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad.lst',
   'DATA'),
  ('fiona\\proj_data\\nad27',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad27',
   'DATA'),
  ('fiona\\proj_data\\nad83',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\nad83',
   'DATA'),
  ('fiona\\proj_data\\other.extra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\other.extra',
   'DATA'),
  ('fiona\\proj_data\\proj-config-version.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-config-version.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj-config.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-config.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets-debug.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets-release.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj-targets.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj.db',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj.db',
   'DATA'),
  ('fiona\\proj_data\\proj.ini',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj.ini',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets-debug.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets-debug.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets-release.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets-release.cmake',
   'DATA'),
  ('fiona\\proj_data\\proj4-targets.cmake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\proj4-targets.cmake',
   'DATA'),
  ('fiona\\proj_data\\projjson.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\projjson.schema.json',
   'DATA'),
  ('fiona\\proj_data\\triangulation.schema.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\triangulation.schema.json',
   'DATA'),
  ('fiona\\proj_data\\usage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\usage',
   'DATA'),
  ('fiona\\proj_data\\vcpkg.spdx.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\vcpkg.spdx.json',
   'DATA'),
  ('fiona\\proj_data\\vcpkg_abi_info.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\vcpkg_abi_info.txt',
   'DATA'),
  ('fiona\\proj_data\\world',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\proj_data\\world',
   'DATA'),
  ('fiona\\rfc3339.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\rfc3339.py',
   'DATA'),
  ('fiona\\session.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\session.py',
   'DATA'),
  ('fiona\\transform.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\transform.py',
   'DATA'),
  ('fiona\\vfs.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\fiona\\vfs.py',
   'DATA'),
  ('flask\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\__init__.py',
   'DATA'),
  ('flask\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\__main__.py',
   'DATA'),
  ('flask\\app.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\app.py',
   'DATA'),
  ('flask\\blueprints.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\blueprints.py',
   'DATA'),
  ('flask\\cli.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\cli.py',
   'DATA'),
  ('flask\\config.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\config.py',
   'DATA'),
  ('flask\\ctx.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\ctx.py',
   'DATA'),
  ('flask\\debughelpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\debughelpers.py',
   'DATA'),
  ('flask\\globals.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\globals.py',
   'DATA'),
  ('flask\\helpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\helpers.py',
   'DATA'),
  ('flask\\json\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\__init__.py',
   'DATA'),
  ('flask\\json\\tag.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\json\\tag.py',
   'DATA'),
  ('flask\\logging.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\logging.py',
   'DATA'),
  ('flask\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\py.typed',
   'DATA'),
  ('flask\\scaffold.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\scaffold.py',
   'DATA'),
  ('flask\\sessions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\sessions.py',
   'DATA'),
  ('flask\\signals.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\signals.py',
   'DATA'),
  ('flask\\templating.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\templating.py',
   'DATA'),
  ('flask\\testing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\testing.py',
   'DATA'),
  ('flask\\typing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\typing.py',
   'DATA'),
  ('flask\\views.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\views.py',
   'DATA'),
  ('flask\\wrappers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask\\wrappers.py',
   'DATA'),
  ('flask_cors\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_cors\\__init__.py',
   'DATA'),
  ('flask_cors\\core.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_cors\\core.py',
   'DATA'),
  ('flask_cors\\decorator.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_cors\\decorator.py',
   'DATA'),
  ('flask_cors\\extension.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_cors\\extension.py',
   'DATA'),
  ('flask_cors\\version.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask_cors\\version.py',
   'DATA'),
  ('pillow-11.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('pillow-11.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\METADATA',
   'DATA'),
  ('pillow-11.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\RECORD',
   'DATA'),
  ('pillow-11.2.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\REQUESTED',
   'DATA'),
  ('pillow-11.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\WHEEL',
   'DATA'),
  ('pillow-11.2.1.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pillow-11.2.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\top_level.txt',
   'DATA'),
  ('pillow-11.2.1.dist-info\\zip-safe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pillow-11.2.1.dist-info\\zip-safe',
   'DATA'),
  ('psutil-5.9.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\INSTALLER',
   'DATA'),
  ('psutil-5.9.5.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\LICENSE',
   'DATA'),
  ('psutil-5.9.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\METADATA',
   'DATA'),
  ('psutil-5.9.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\RECORD',
   'DATA'),
  ('psutil-5.9.5.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\REQUESTED',
   'DATA'),
  ('psutil-5.9.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\WHEEL',
   'DATA'),
  ('psutil-5.9.5.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil-5.9.5.dist-info\\top_level.txt',
   'DATA'),
  ('psutil\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\__init__.py',
   'DATA'),
  ('psutil\\_common.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_common.py',
   'DATA'),
  ('psutil\\_compat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_compat.py',
   'DATA'),
  ('psutil\\_psaix.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psaix.py',
   'DATA'),
  ('psutil\\_psbsd.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psbsd.py',
   'DATA'),
  ('psutil\\_pslinux.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_pslinux.py',
   'DATA'),
  ('psutil\\_psosx.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psosx.py',
   'DATA'),
  ('psutil\\_psposix.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psposix.py',
   'DATA'),
  ('psutil\\_pssunos.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_pssunos.py',
   'DATA'),
  ('psutil\\_pswindows.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_pswindows.py',
   'DATA'),
  ('psutil\\tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\__init__.py',
   'DATA'),
  ('psutil\\tests\\__main__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\__main__.py',
   'DATA'),
  ('psutil\\tests\\runner.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\runner.py',
   'DATA'),
  ('psutil\\tests\\test_aix.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_aix.py',
   'DATA'),
  ('psutil\\tests\\test_bsd.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_bsd.py',
   'DATA'),
  ('psutil\\tests\\test_connections.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_connections.py',
   'DATA'),
  ('psutil\\tests\\test_contracts.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_contracts.py',
   'DATA'),
  ('psutil\\tests\\test_linux.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_linux.py',
   'DATA'),
  ('psutil\\tests\\test_memleaks.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_memleaks.py',
   'DATA'),
  ('psutil\\tests\\test_misc.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_misc.py',
   'DATA'),
  ('psutil\\tests\\test_osx.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_osx.py',
   'DATA'),
  ('psutil\\tests\\test_posix.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_posix.py',
   'DATA'),
  ('psutil\\tests\\test_process.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_process.py',
   'DATA'),
  ('psutil\\tests\\test_sunos.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_sunos.py',
   'DATA'),
  ('psutil\\tests\\test_system.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_system.py',
   'DATA'),
  ('psutil\\tests\\test_testutils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_testutils.py',
   'DATA'),
  ('psutil\\tests\\test_unicode.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_unicode.py',
   'DATA'),
  ('psutil\\tests\\test_windows.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\tests\\test_windows.py',
   'DATA'),
  ('pywin32-307.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\INSTALLER',
   'DATA'),
  ('pywin32-307.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\METADATA',
   'DATA'),
  ('pywin32-307.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\RECORD',
   'DATA'),
  ('pywin32-307.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\REQUESTED',
   'DATA'),
  ('pywin32-307.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\WHEEL',
   'DATA'),
  ('pywin32-307.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32-307.dist-info\\top_level.txt',
   'DATA'),
  ('redis-4.5.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('redis-4.5.4.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\LICENSE',
   'DATA'),
  ('redis-4.5.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\METADATA',
   'DATA'),
  ('redis-4.5.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\RECORD',
   'DATA'),
  ('redis-4.5.4.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\REQUESTED',
   'DATA'),
  ('redis-4.5.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\WHEEL',
   'DATA'),
  ('redis-4.5.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis-4.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('redis\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\__init__.py',
   'DATA'),
  ('redis\\asyncio\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\__init__.py',
   'DATA'),
  ('redis\\asyncio\\client.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\client.py',
   'DATA'),
  ('redis\\asyncio\\cluster.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\cluster.py',
   'DATA'),
  ('redis\\asyncio\\connection.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\connection.py',
   'DATA'),
  ('redis\\asyncio\\lock.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\lock.py',
   'DATA'),
  ('redis\\asyncio\\parser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\parser.py',
   'DATA'),
  ('redis\\asyncio\\retry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\retry.py',
   'DATA'),
  ('redis\\asyncio\\sentinel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\sentinel.py',
   'DATA'),
  ('redis\\asyncio\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\asyncio\\utils.py',
   'DATA'),
  ('redis\\backoff.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\backoff.py',
   'DATA'),
  ('redis\\client.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\client.py',
   'DATA'),
  ('redis\\cluster.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\cluster.py',
   'DATA'),
  ('redis\\commands\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\__init__.py',
   'DATA'),
  ('redis\\commands\\bf\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\bf\\__init__.py',
   'DATA'),
  ('redis\\commands\\bf\\commands.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\bf\\commands.py',
   'DATA'),
  ('redis\\commands\\bf\\info.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\bf\\info.py',
   'DATA'),
  ('redis\\commands\\cluster.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\cluster.py',
   'DATA'),
  ('redis\\commands\\core.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\core.py',
   'DATA'),
  ('redis\\commands\\graph\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\__init__.py',
   'DATA'),
  ('redis\\commands\\graph\\commands.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\commands.py',
   'DATA'),
  ('redis\\commands\\graph\\edge.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\edge.py',
   'DATA'),
  ('redis\\commands\\graph\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\exceptions.py',
   'DATA'),
  ('redis\\commands\\graph\\execution_plan.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\execution_plan.py',
   'DATA'),
  ('redis\\commands\\graph\\node.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\node.py',
   'DATA'),
  ('redis\\commands\\graph\\path.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\path.py',
   'DATA'),
  ('redis\\commands\\graph\\query_result.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\graph\\query_result.py',
   'DATA'),
  ('redis\\commands\\helpers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\helpers.py',
   'DATA'),
  ('redis\\commands\\json\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\json\\__init__.py',
   'DATA'),
  ('redis\\commands\\json\\_util.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\json\\_util.py',
   'DATA'),
  ('redis\\commands\\json\\commands.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\json\\commands.py',
   'DATA'),
  ('redis\\commands\\json\\decoders.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\json\\decoders.py',
   'DATA'),
  ('redis\\commands\\json\\path.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\json\\path.py',
   'DATA'),
  ('redis\\commands\\parser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\parser.py',
   'DATA'),
  ('redis\\commands\\redismodules.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\redismodules.py',
   'DATA'),
  ('redis\\commands\\search\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\__init__.py',
   'DATA'),
  ('redis\\commands\\search\\_util.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\_util.py',
   'DATA'),
  ('redis\\commands\\search\\aggregation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\aggregation.py',
   'DATA'),
  ('redis\\commands\\search\\commands.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\commands.py',
   'DATA'),
  ('redis\\commands\\search\\document.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\document.py',
   'DATA'),
  ('redis\\commands\\search\\field.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\field.py',
   'DATA'),
  ('redis\\commands\\search\\indexDefinition.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\indexDefinition.py',
   'DATA'),
  ('redis\\commands\\search\\query.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\query.py',
   'DATA'),
  ('redis\\commands\\search\\querystring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\querystring.py',
   'DATA'),
  ('redis\\commands\\search\\reducers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\reducers.py',
   'DATA'),
  ('redis\\commands\\search\\result.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\result.py',
   'DATA'),
  ('redis\\commands\\search\\suggestion.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\search\\suggestion.py',
   'DATA'),
  ('redis\\commands\\sentinel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\sentinel.py',
   'DATA'),
  ('redis\\commands\\timeseries\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\timeseries\\__init__.py',
   'DATA'),
  ('redis\\commands\\timeseries\\commands.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\timeseries\\commands.py',
   'DATA'),
  ('redis\\commands\\timeseries\\info.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\timeseries\\info.py',
   'DATA'),
  ('redis\\commands\\timeseries\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\commands\\timeseries\\utils.py',
   'DATA'),
  ('redis\\compat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\compat.py',
   'DATA'),
  ('redis\\connection.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\connection.py',
   'DATA'),
  ('redis\\crc.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\crc.py',
   'DATA'),
  ('redis\\credentials.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\credentials.py',
   'DATA'),
  ('redis\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\exceptions.py',
   'DATA'),
  ('redis\\lock.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\lock.py',
   'DATA'),
  ('redis\\ocsp.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\ocsp.py',
   'DATA'),
  ('redis\\redis.windows.conf',
   'E:\\GeoStream_Integration\\frontend\\backend\\redis\\redis.windows.conf',
   'DATA'),
  ('redis\\retry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\retry.py',
   'DATA'),
  ('redis\\sentinel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\sentinel.py',
   'DATA'),
  ('redis\\typing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\typing.py',
   'DATA'),
  ('redis\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\redis\\utils.py',
   'DATA'),
  ('requests-2.32.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\INSTALLER',
   'DATA'),
  ('requests-2.32.3.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\LICENSE',
   'DATA'),
  ('requests-2.32.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\METADATA',
   'DATA'),
  ('requests-2.32.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\RECORD',
   'DATA'),
  ('requests-2.32.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\REQUESTED',
   'DATA'),
  ('requests-2.32.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\WHEEL',
   'DATA'),
  ('requests-2.32.3.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests-2.32.3.dist-info\\top_level.txt',
   'DATA'),
  ('requests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\__init__.py',
   'DATA'),
  ('requests\\__version__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\__version__.py',
   'DATA'),
  ('requests\\_internal_utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\_internal_utils.py',
   'DATA'),
  ('requests\\adapters.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\adapters.py',
   'DATA'),
  ('requests\\api.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\api.py',
   'DATA'),
  ('requests\\auth.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\auth.py',
   'DATA'),
  ('requests\\certs.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\certs.py',
   'DATA'),
  ('requests\\compat.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\compat.py',
   'DATA'),
  ('requests\\cookies.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\cookies.py',
   'DATA'),
  ('requests\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\exceptions.py',
   'DATA'),
  ('requests\\help.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\help.py',
   'DATA'),
  ('requests\\hooks.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\hooks.py',
   'DATA'),
  ('requests\\models.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\models.py',
   'DATA'),
  ('requests\\packages.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\packages.py',
   'DATA'),
  ('requests\\sessions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\sessions.py',
   'DATA'),
  ('requests\\status_codes.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\status_codes.py',
   'DATA'),
  ('requests\\structures.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\structures.py',
   'DATA'),
  ('requests\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\requests\\utils.py',
   'DATA'),
  ('shapely-2.1.1.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('shapely-2.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('shapely-2.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\METADATA',
   'DATA'),
  ('shapely-2.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\RECORD',
   'DATA'),
  ('shapely-2.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\WHEEL',
   'DATA'),
  ('shapely-2.1.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('shapely-2.1.1.dist-info\\licenses\\LICENSE_GEOS',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\licenses\\LICENSE_GEOS',
   'DATA'),
  ('shapely-2.1.1.dist-info\\licenses\\LICENSE_win32',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\licenses\\LICENSE_win32',
   'DATA'),
  ('shapely-2.1.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely-2.1.1.dist-info\\top_level.txt',
   'DATA'),
  ('shapely\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\__init__.py',
   'DATA'),
  ('shapely\\_coverage.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_coverage.py',
   'DATA'),
  ('shapely\\_enum.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_enum.py',
   'DATA'),
  ('shapely\\_geometry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geometry.py',
   'DATA'),
  ('shapely\\_geos.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_geos.pxd',
   'DATA'),
  ('shapely\\_pygeos_api.pxd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_pygeos_api.pxd',
   'DATA'),
  ('shapely\\_ragged_array.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_ragged_array.py',
   'DATA'),
  ('shapely\\_version.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\_version.py',
   'DATA'),
  ('shapely\\affinity.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\affinity.py',
   'DATA'),
  ('shapely\\algorithms\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\__init__.py',
   'DATA'),
  ('shapely\\algorithms\\_oriented_envelope.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\_oriented_envelope.py',
   'DATA'),
  ('shapely\\algorithms\\cga.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\cga.py',
   'DATA'),
  ('shapely\\algorithms\\polylabel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\algorithms\\polylabel.py',
   'DATA'),
  ('shapely\\conftest.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\conftest.py',
   'DATA'),
  ('shapely\\constructive.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\constructive.py',
   'DATA'),
  ('shapely\\coordinates.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\coordinates.py',
   'DATA'),
  ('shapely\\coords.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\coords.py',
   'DATA'),
  ('shapely\\creation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\creation.py',
   'DATA'),
  ('shapely\\decorators.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\decorators.py',
   'DATA'),
  ('shapely\\errors.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\errors.py',
   'DATA'),
  ('shapely\\geometry\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\__init__.py',
   'DATA'),
  ('shapely\\geometry\\base.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\base.py',
   'DATA'),
  ('shapely\\geometry\\collection.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\collection.py',
   'DATA'),
  ('shapely\\geometry\\geo.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\geo.py',
   'DATA'),
  ('shapely\\geometry\\linestring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\linestring.py',
   'DATA'),
  ('shapely\\geometry\\multilinestring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multilinestring.py',
   'DATA'),
  ('shapely\\geometry\\multipoint.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multipoint.py',
   'DATA'),
  ('shapely\\geometry\\multipolygon.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\multipolygon.py',
   'DATA'),
  ('shapely\\geometry\\point.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\point.py',
   'DATA'),
  ('shapely\\geometry\\polygon.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geometry\\polygon.py',
   'DATA'),
  ('shapely\\geos.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\geos.py',
   'DATA'),
  ('shapely\\io.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\io.py',
   'DATA'),
  ('shapely\\linear.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\linear.py',
   'DATA'),
  ('shapely\\measurement.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\measurement.py',
   'DATA'),
  ('shapely\\ops.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\ops.py',
   'DATA'),
  ('shapely\\plotting.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\plotting.py',
   'DATA'),
  ('shapely\\predicates.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\predicates.py',
   'DATA'),
  ('shapely\\prepared.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\prepared.py',
   'DATA'),
  ('shapely\\set_operations.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\set_operations.py',
   'DATA'),
  ('shapely\\speedups.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\speedups.py',
   'DATA'),
  ('shapely\\strtree.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\strtree.py',
   'DATA'),
  ('shapely\\testing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\testing.py',
   'DATA'),
  ('shapely\\tests\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\__init__.py',
   'DATA'),
  ('shapely\\tests\\common.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\common.py',
   'DATA'),
  ('shapely\\tests\\geometry\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\__init__.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_collection.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_collection.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_coords.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_coords.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_decimal.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_decimal.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_emptiness.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_emptiness.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_equality.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_equality.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_format.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_format.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_geometry_base.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_geometry_base.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_hash.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_hash.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_linestring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_linestring.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_multi.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_multi.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_multilinestring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_multilinestring.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_multipoint.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_multipoint.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_multipolygon.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_multipolygon.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_point.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_point.py',
   'DATA'),
  ('shapely\\tests\\geometry\\test_polygon.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\geometry\\test_polygon.py',
   'DATA'),
  ('shapely\\tests\\legacy\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\__init__.py',
   'DATA'),
  ('shapely\\tests\\legacy\\conftest.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\conftest.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_affinity.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_affinity.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_box.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_box.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_buffer.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_buffer.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_cga.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_cga.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_clip_by_rect.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_clip_by_rect.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_create_inconsistent_dimensionality.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_create_inconsistent_dimensionality.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_delaunay.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_delaunay.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_empty_polygons.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_empty_polygons.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_equality.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_equality.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_geointerface.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_geointerface.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_invalid_geometries.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_invalid_geometries.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_linear_referencing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_linear_referencing.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_linemerge.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_linemerge.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_locale.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_locale.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_make_valid.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_make_valid.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_mapping.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_mapping.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_minimum_clearance.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_minimum_clearance.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_ndarrays.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_ndarrays.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_nearest.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_nearest.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_operations.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_operations.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_operators.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_operators.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_orient.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_orient.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_parallel_offset.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_parallel_offset.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_persist.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_persist.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_pickle.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_pickle.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_polygonize.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_polygonize.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_polylabel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_polylabel.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_predicates.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_predicates.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_prepared.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_prepared.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_products_z.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_products_z.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_shape.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_shape.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_shared_paths.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_shared_paths.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_singularity.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_singularity.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_snap.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_snap.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_split.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_split.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_substring.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_substring.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_svg.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_svg.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_transform.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_transform.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_union.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_union.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_validation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_validation.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_vectorized.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_vectorized.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_voronoi_diagram.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_voronoi_diagram.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_wkb.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_wkb.py',
   'DATA'),
  ('shapely\\tests\\legacy\\test_wkt.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\test_wkt.py',
   'DATA'),
  ('shapely\\tests\\legacy\\threading_test.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\legacy\\threading_test.py',
   'DATA'),
  ('shapely\\tests\\test_constructive.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_constructive.py',
   'DATA'),
  ('shapely\\tests\\test_coordinates.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_coordinates.py',
   'DATA'),
  ('shapely\\tests\\test_coverage.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_coverage.py',
   'DATA'),
  ('shapely\\tests\\test_creation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_creation.py',
   'DATA'),
  ('shapely\\tests\\test_creation_indices.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_creation_indices.py',
   'DATA'),
  ('shapely\\tests\\test_decorators.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_decorators.py',
   'DATA'),
  ('shapely\\tests\\test_geometry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_geometry.py',
   'DATA'),
  ('shapely\\tests\\test_io.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_io.py',
   'DATA'),
  ('shapely\\tests\\test_linear.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_linear.py',
   'DATA'),
  ('shapely\\tests\\test_measurement.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_measurement.py',
   'DATA'),
  ('shapely\\tests\\test_misc.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_misc.py',
   'DATA'),
  ('shapely\\tests\\test_plotting.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_plotting.py',
   'DATA'),
  ('shapely\\tests\\test_predicates.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_predicates.py',
   'DATA'),
  ('shapely\\tests\\test_ragged_array.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_ragged_array.py',
   'DATA'),
  ('shapely\\tests\\test_set_operations.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_set_operations.py',
   'DATA'),
  ('shapely\\tests\\test_strtree.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_strtree.py',
   'DATA'),
  ('shapely\\tests\\test_testing.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\tests\\test_testing.py',
   'DATA'),
  ('shapely\\validation.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\validation.py',
   'DATA'),
  ('shapely\\vectorized\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\vectorized\\__init__.py',
   'DATA'),
  ('shapely\\wkb.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\wkb.py',
   'DATA'),
  ('shapely\\wkt.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\shapely\\wkt.py',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\__init__.py',
   'DATA'),
  ('werkzeug\\_internal.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_internal.py',
   'DATA'),
  ('werkzeug\\_reloader.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\_reloader.py',
   'DATA'),
  ('werkzeug\\datastructures\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'DATA'),
  ('werkzeug\\datastructures\\accept.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\accept.py',
   'DATA'),
  ('werkzeug\\datastructures\\accept.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\accept.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\auth.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\auth.py',
   'DATA'),
  ('werkzeug\\datastructures\\cache_control.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'DATA'),
  ('werkzeug\\datastructures\\cache_control.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\cache_control.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\csp.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\csp.py',
   'DATA'),
  ('werkzeug\\datastructures\\csp.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\csp.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\etag.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\etag.py',
   'DATA'),
  ('werkzeug\\datastructures\\etag.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\etag.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\file_storage.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'DATA'),
  ('werkzeug\\datastructures\\file_storage.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\file_storage.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\headers.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\headers.py',
   'DATA'),
  ('werkzeug\\datastructures\\headers.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\headers.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\mixins.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'DATA'),
  ('werkzeug\\datastructures\\mixins.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\mixins.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\range.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\range.py',
   'DATA'),
  ('werkzeug\\datastructures\\range.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\range.pyi',
   'DATA'),
  ('werkzeug\\datastructures\\structures.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\structures.py',
   'DATA'),
  ('werkzeug\\datastructures\\structures.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\datastructures\\structures.pyi',
   'DATA'),
  ('werkzeug\\debug\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\__init__.py',
   'DATA'),
  ('werkzeug\\debug\\console.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\console.py',
   'DATA'),
  ('werkzeug\\debug\\repr.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\repr.py',
   'DATA'),
  ('werkzeug\\debug\\shared\\ICON_LICENSE.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\ICON_LICENSE.md',
   'DATA'),
  ('werkzeug\\debug\\shared\\console.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\console.png',
   'DATA'),
  ('werkzeug\\debug\\shared\\debugger.js',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\debugger.js',
   'DATA'),
  ('werkzeug\\debug\\shared\\less.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\less.png',
   'DATA'),
  ('werkzeug\\debug\\shared\\more.png',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\more.png',
   'DATA'),
  ('werkzeug\\debug\\shared\\style.css',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\shared\\style.css',
   'DATA'),
  ('werkzeug\\debug\\tbtools.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\debug\\tbtools.py',
   'DATA'),
  ('werkzeug\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\exceptions.py',
   'DATA'),
  ('werkzeug\\formparser.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\formparser.py',
   'DATA'),
  ('werkzeug\\http.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\http.py',
   'DATA'),
  ('werkzeug\\local.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\local.py',
   'DATA'),
  ('werkzeug\\middleware\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\__init__.py',
   'DATA'),
  ('werkzeug\\middleware\\dispatcher.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\dispatcher.py',
   'DATA'),
  ('werkzeug\\middleware\\http_proxy.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\http_proxy.py',
   'DATA'),
  ('werkzeug\\middleware\\lint.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\lint.py',
   'DATA'),
  ('werkzeug\\middleware\\profiler.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\profiler.py',
   'DATA'),
  ('werkzeug\\middleware\\proxy_fix.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\proxy_fix.py',
   'DATA'),
  ('werkzeug\\middleware\\shared_data.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'DATA'),
  ('werkzeug\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\py.typed',
   'DATA'),
  ('werkzeug\\routing\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\__init__.py',
   'DATA'),
  ('werkzeug\\routing\\converters.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\converters.py',
   'DATA'),
  ('werkzeug\\routing\\exceptions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\exceptions.py',
   'DATA'),
  ('werkzeug\\routing\\map.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\map.py',
   'DATA'),
  ('werkzeug\\routing\\matcher.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\matcher.py',
   'DATA'),
  ('werkzeug\\routing\\rules.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\routing\\rules.py',
   'DATA'),
  ('werkzeug\\sansio\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\__init__.py',
   'DATA'),
  ('werkzeug\\sansio\\http.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\http.py',
   'DATA'),
  ('werkzeug\\sansio\\multipart.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\multipart.py',
   'DATA'),
  ('werkzeug\\sansio\\request.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\request.py',
   'DATA'),
  ('werkzeug\\sansio\\response.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\response.py',
   'DATA'),
  ('werkzeug\\sansio\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\sansio\\utils.py',
   'DATA'),
  ('werkzeug\\security.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\security.py',
   'DATA'),
  ('werkzeug\\serving.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\serving.py',
   'DATA'),
  ('werkzeug\\test.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\test.py',
   'DATA'),
  ('werkzeug\\testapp.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\testapp.py',
   'DATA'),
  ('werkzeug\\urls.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\urls.py',
   'DATA'),
  ('werkzeug\\user_agent.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\user_agent.py',
   'DATA'),
  ('werkzeug\\utils.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\utils.py',
   'DATA'),
  ('werkzeug\\wrappers\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'DATA'),
  ('werkzeug\\wrappers\\request.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\request.py',
   'DATA'),
  ('werkzeug\\wrappers\\response.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wrappers\\response.py',
   'DATA'),
  ('werkzeug\\wsgi.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug\\wsgi.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('pypinyin\\runner.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\runner.pyi',
   'DATA'),
  ('pypinyin\\__init__.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\__init__.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\pinyin_dict.json',
   'DATA'),
  ('pypinyin\\exceptions.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\exceptions.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_convert.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\finals.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\finals.pyi',
   'DATA'),
  ('pypinyin\\constants.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\constants.pyi',
   'DATA'),
  ('pypinyin\\style\\cyrillic.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\cyrillic.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_sandhi.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\tone_sandhi.pyi',
   'DATA'),
  ('pypinyin\\style\\_constants.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\_constants.pyi',
   'DATA'),
  ('pypinyin\\style\\others.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\others.pyi',
   'DATA'),
  ('pypinyin\\compat.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\compat.pyi',
   'DATA'),
  ('pypinyin\\core.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\core.pyi',
   'DATA'),
  ('pypinyin\\phonetic_symbol.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\phonetic_symbol.pyi',
   'DATA'),
  ('pypinyin\\contrib\\_tone_rule.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\utils.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\utils.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_convert.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\_tone_convert.pyi',
   'DATA'),
  ('pypinyin\\tools\\toneconvert.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\tools\\toneconvert.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\pinyin_dict.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\phrases_dict.pyi',
   'DATA'),
  ('pypinyin\\style\\gwoyeu.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\gwoyeu.pyi',
   'DATA'),
  ('pypinyin\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\py.typed',
   'DATA'),
  ('pypinyin\\contrib\\mmseg.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\style\\__init__.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\_utils.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\_utils.pyi',
   'DATA'),
  ('pypinyin\\converter.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\converter.pyi',
   'DATA'),
  ('pypinyin\\contrib\\neutral_tone.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\neutral_tone.pyi',
   'DATA'),
  ('pypinyin\\style\\tone.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\tone.pyi',
   'DATA'),
  ('pypinyin\\seg\\simpleseg.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\seg\\simpleseg.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_rule.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\style\\bopomofo.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\bopomofo.pyi',
   'DATA'),
  ('pypinyin\\standard.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\standard.pyi',
   'DATA'),
  ('pypinyin\\contrib\\uv.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\contrib\\uv.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\phrases_dict.json',
   'DATA'),
  ('pypinyin\\seg\\mmseg.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\seg\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\style\\initials.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\initials.pyi',
   'DATA'),
  ('pypinyin\\style\\wadegiles.pyi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pypinyin\\style\\wadegiles.pyi',
   'DATA'),
  ('numpy-2.2.5.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.5.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.5.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.5.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.5.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.5.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\numpy-2.2.5.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'E:\\GeoStream_Integration\\frontend\\backend\\build\\GeoStream '
   'IntegrationV1.0.0\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)

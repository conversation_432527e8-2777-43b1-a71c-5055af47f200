"""
简化的构建脚本
用于快速构建GeoStream Integration，包含GDB解析功能
"""
import PyInstaller.__main__
import os

def main():
    print("GeoStream Integration 快速构建")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 简化的PyInstaller选项
    options = [
        'app.py',  # 主程序入口
        '--name=GeoStream_Integration',  # 生成的exe名称
        '--onefile',  # 打包成单个exe文件
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问确认
        '--noupx',  # 不使用UPX压缩
        
        # 基础配置文件
        '--add-data=config.xml;.',
        '--add-data=redis/redis-server.exe;redis',
        '--add-data=redis/redis.windows.conf;redis',
        
        # 核心库导入
        '--hidden-import=flask',
        '--hidden-import=flask_cors',
        '--hidden-import=redis',
        '--hidden-import=psutil',
        '--hidden-import=sqlite3',
        
        # GDB解析库
        '--hidden-import=fiona',
        '--hidden-import=osgeo',
        '--hidden-import=osgeo.ogr',
        '--hidden-import=osgeo.osr',
        
        # 收集相关模块
        '--collect-all=fiona',
        '--collect-all=osgeo',
        '--collect-all=flask',
        '--collect-all=redis',
        
        # 排除不需要的模块
        '--exclude-module=tkinter',
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
    ]
    
    try:
        print("开始构建...")
        PyInstaller.__main__.run(options)
        print("\n构建完成！")
        
        # 检查生成的文件
        dist_dir = os.path.join(current_dir, 'dist')
        if os.path.exists(dist_dir):
            files = [f for f in os.listdir(dist_dir) if f.endswith('.exe')]
            if files:
                exe_file = files[0]
                exe_path = os.path.join(dist_dir, exe_file)
                size = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"生成文件: {exe_file} ({size:.1f} MB)")
                print(f"位置: {exe_path}")
        
    except Exception as e:
        print(f"构建失败: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()

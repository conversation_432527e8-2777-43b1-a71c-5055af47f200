import sqlite3
import os
from config import DB_PATH

def check_db():
    # 检查数据库文件是否存在
    if not os.path.exists(DB_PATH):
        print(f"错误：数据库文件 {DB_PATH} 不存在")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 检查用户表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("错误：users表不存在")
            return False
            
        # 检查登录用户表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='login_users'")
        if not cursor.fetchone():
            print("错误：login_users表不存在")
            return False
            
        # 检查表结构
        cursor.execute("PRAGMA table_info(users)")
        columns = [col[1] for col in cursor.fetchall()]
        required_columns = ['id', 'username', 'password', 'created_at']
        if not all(col in columns for col in required_columns):
            print("错误：users表结构不完整")
            return False
            
        cursor.execute("PRAGMA table_info(login_users)")
        columns = [col[1] for col in cursor.fetchall()]
        required_columns = ['id', 'user_id', 'token', 'created_at']
        if not all(col in columns for col in required_columns):
            print("错误：login_users表结构不完整")
            return False
            
        print("数据库检查通过！")
        return True
        
    except sqlite3.Error as e:
        print(f"数据库错误：{e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    check_db() 
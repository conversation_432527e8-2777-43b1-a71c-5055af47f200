import os
import xml.etree.ElementTree as ET
import sys
import logging

logger = logging.getLogger(__name__)

def read_config():
    # 获取当前文件所在目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，使用exe所在目录
        current_dir = os.path.dirname(sys.executable)
    else:
        # 在开发环境下，使用当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 从config.xml读取配置
    config_path = os.path.join(current_dir, 'config.xml')
    logger.info(f"正在读取配置文件: {config_path}")
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
        
    tree = ET.parse(config_path)
    root = tree.getroot()
    
    # 读取CORS配置
    cors_config = {
        'origins': [],
        'methods': [],
        'allow_headers': [],
        'expose_headers': [],
        'max_age': 3600,
        'supports_credentials': True
    }
    
    cors = root.find('CORS')
    if cors is not None:
        # 读取origins
        origins = cors.find('Origins')
        if origins is not None:
            cors_config['origins'] = [origin.text for origin in origins.findall('Origin')]
        
        # 读取methods
        methods = cors.find('Methods')
        if methods is not None:
            cors_config['methods'] = [method.text for method in methods.findall('Method')]
        
        # 读取allow_headers
        allow_headers = cors.find('AllowHeaders')
        if allow_headers is not None:
            cors_config['allow_headers'] = [header.text for header in allow_headers.findall('Header')]
        
        # 读取expose_headers
        expose_headers = cors.find('ExposeHeaders')
        if expose_headers is not None:
            cors_config['expose_headers'] = [header.text for header in expose_headers.findall('Header')]
        
        # 读取max_age
        max_age = cors.find('MaxAge')
        if max_age is not None:
            cors_config['max_age'] = int(max_age.text)
        
        # 读取supports_credentials
        supports_credentials = cors.find('SupportsCredentials')
        if supports_credentials is not None:
            cors_config['supports_credentials'] = supports_credentials.text.lower() == 'true'
    
    # 获取其他配置
    config = {
        'fme_home': root.find('FmeHome').text,
        'fme_dir': root.find('FmeDir').text,
        'dates': root.find('dates').text,
        'limit': int(root.find('limits').text) if root.find('limits') is not None else 1,
        'cors_config': cors_config
    }
    
    # 读取服务器配置
    server = root.find('Server')
    if server is not None:
        config['server'] = {
            'host': server.find('Host').text,
            'port': int(server.find('Port').text),
            'debug': server.find('Debug').text.lower() == 'true'
        }
    
    # 读取前端配置
    frontend = root.find('Frontend')
    if frontend is not None:
        config['frontend'] = {
            'ip': frontend.find('IP').text,
            'port': int(frontend.find('Port').text) if frontend.find('Port').text else None
        }
    
    return config

# 读取配置
config = read_config()

# 获取当前目录
if getattr(sys, 'frozen', False):
    # 如果是打包后的exe，使用exe所在目录
    current_dir = os.path.dirname(sys.executable)
else:
    # 在开发环境下，使用当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

# 数据库配置 - 使用当前目录
DB_PATH = os.path.join(current_dir, 'GeoStream.db')

# 服务器配置
SERVER_CONFIG = config.get('server', {})
if not SERVER_CONFIG:
    logger.error("未在配置文件中找到服务器配置！")
    sys.exit(1)

# CORS配置
CORS_CONFIG = config['cors_config']

# 前端配置
FRONTEND_CONFIG = config.get('frontend', {})
if not FRONTEND_CONFIG:
    logger.error("未在配置文件中找到前端配置！")
    sys.exit(1)

# 其他配置可以在这里添加 
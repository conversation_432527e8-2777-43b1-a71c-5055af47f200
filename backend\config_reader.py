import xml.etree.ElementTree as ET

def read_config():
    try:
        tree = ET.parse('backend/config.xml')
        root = tree.getroot()
        
        config = {
            'server': {
                'host': root.findtext('Server/Host', '').strip(),
                'port': int(root.findtext('Server/Port', '')),
                'debug': root.findtext('Server/Debug', 'true').lower() == 'true'
            },
            'frontend': {
                'ip': root.findtext('Frontend/IP', '').strip(),
                'port': int(root.findtext('Frontend/Port', ''))
            },
            'theme': root.findtext('theme', 'default').strip()
        }
        
        print(f"Server配置: {config['server']}")
        print(f"Frontend配置: {config['frontend']}")
        print(f"主题配置: {config['theme']}")
        
        return config
    except Exception as e:
        print(f"读取配置文件失败: {e}")
        return None

if __name__ == '__main__':
    read_config() 
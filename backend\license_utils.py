import os
import json
from datetime import datetime

LICENSE_FILE = os.path.join(os.path.dirname(__file__), 'license.lic')
TRUSTED_TIME_FILE = os.path.join(os.path.dirname(__file__), '.trusted_time')

def load_license():
    """加载 license 文件"""
    if not os.path.exists(LICENSE_FILE):
        return None
    with open(LICENSE_FILE, 'r', encoding='utf-8') as f:
        try:
            return json.load(f)
        except Exception:
            return None

def get_trusted_time():
    """获取首次启动可信时间（防止系统时间回拨）"""
    if os.path.exists(TRUSTED_TIME_FILE):
        with open(TRUSTED_TIME_FILE, 'r') as f:
            return f.read().strip()
    # 首次启动，记录当前时间
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open(TRUSTED_TIME_FILE, 'w') as f:
        f.write(now)
    return now

def is_time_valid(valid_from, valid_to):
    """校验当前时间是否在授权范围内，并检测系统时间回拨"""
    trusted_time_str = get_trusted_time()
    trusted_time = datetime.strptime(trusted_time_str, '%Y-%m-%d %H:%M:%S')
    now = datetime.now()
    # 检查系统时间是否回拨
    if now < trusted_time:
        return False, '检测到系统时间回拨，许可失效'
    # 检查授权有效期
    if valid_from and now < datetime.strptime(valid_from, '%Y-%m-%d'):
        return False, '授权尚未生效'
    if valid_to and now > datetime.strptime(valid_to, '%Y-%m-%d'):
        return False, '授权已过期'
    return True, ''

def check_license(module_name):
    """
    校验指定模块的授权情况
    :param module_name: 'tools'/'cad2gis'/'coordinate'/'quality'
    :return: (bool, str) 是否授权, 失败原因
    """
    lic = load_license()
    if not lic:
        return False, '未找到 license 文件'
    modules = lic.get('modules', {})
    if not modules.get(module_name, False):
        return False, f'未授权 {module_name} 功能'
    valid_from = lic.get('valid_from')
    valid_to = lic.get('valid_to')
    ok, msg = is_time_valid(valid_from, valid_to)
    if not ok:
        return False, msg
    return True, ''
# Redis配置
REDIS_CONFIG = {
    'host': '127.0.0.1',  # 本地Redis服务器地址
    'port': 6379,        # Redis端口
    'db': 0,            # 使用的数据库编号
    'decode_responses': True,  # 自动解码响应
    'socket_timeout': 5,      # 连接超时时间
    'socket_connect_timeout': 5,  # 连接超时时间
    'retry_on_timeout': True,    # 超时时重试
    'max_connections': 10,        # 最大连接数
    'password': 'geostream123'    # Redis密码
}

# Redis键前缀
REDIS_KEYS = {
    'task_queue': 'task_queue',           # 任务队列
    'task_prefix': 'task:',               # 任务详情前缀
    'task_status_prefix': 'task_status:', # 任务状态前缀
    'task_progress_prefix': 'progress:',  # 任务进度前缀
}

# Redis过期时间（秒）
REDIS_EXPIRE = {
    'task': 86400,        # 任务详情保存24小时
    'task_status': 86400, # 任务状态保存24小时
    'progress': 3600      # 任务进度保存1小时
} 
import redis
from redis.exceptions import RedisError
import json
import logging
from redis_config import REDIS_CONFIG, REDIS_KEYS, REDIS_EXPIRE
import time

logger = logging.getLogger(__name__)

class RedisManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.client = None
        self.connect()
    
    def connect(self):
        """连接到Redis服务器"""
        max_retries = 10
        retry_count = 0
        retry_delay = 5  # 重试间隔（秒）
        
        while retry_count < max_retries:
            try:
                self.client = redis.Redis(**REDIS_CONFIG)
                self.client.ping()  # 测试连接
                logger.info("Redis连接成功")
                return
            except RedisError as e:
                retry_count += 1
                logger.error(f"Redis连接尝试 {retry_count}/{max_retries} 失败: {str(e)}")
                if retry_count < max_retries:
                    time.sleep(retry_delay)
                else:
                    logger.error("Redis连接失败，已达到最大重试次数")
                    self.client = None
    
    def is_connected(self):
        """检查Redis是否已连接"""
        if not self.client:
            return False
        try:
            self.client.ping()
            return True
        except RedisError:
            # 尝试重新连接
            self.connect()
            return self.client is not None
    
    def add_task(self, task_id, task_data):
        """添加任务到队列"""
        if not self.is_connected():
            return False
            
        try:
            # 将任务添加到队列
            self.client.rpush(REDIS_KEYS['task_queue'], json.dumps(task_data))
            
            # 存储任务详情
            task_key = f"{REDIS_KEYS['task_prefix']}{task_id}"
            self.client.set(task_key, json.dumps(task_data))
            self.client.expire(task_key, REDIS_EXPIRE['task'])
            
            # 设置任务状态
            status_key = f"{REDIS_KEYS['task_status_prefix']}{task_id}"
            self.client.set(status_key, 'pending')
            self.client.expire(status_key, REDIS_EXPIRE['task_status'])
            
            return True
        except RedisError as e:
            logger.error(f"添加任务到Redis失败: {str(e)}")
            return False
    
    def get_task(self):
        """从队列中获取一个任务"""
        if not self.is_connected():
            return None
            
        try:
            # 从队列中获取任务
            task_data = self.client.lpop(REDIS_KEYS['task_queue'])
            if task_data:
                return json.loads(task_data)
            return None
        except RedisError as e:
            logger.error(f"从Redis获取任务失败: {str(e)}")
            return None
    
    def update_task_status(self, task_id, status):
        """更新任务状态"""
        if not self.is_connected():
            return False
            
        try:
            status_key = f"{REDIS_KEYS['task_status_prefix']}{task_id}"
            self.client.set(status_key, status)
            self.client.expire(status_key, REDIS_EXPIRE['task_status'])
            return True
        except RedisError as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        if not self.is_connected():
            return None
            
        try:
            status_key = f"{REDIS_KEYS['task_status_prefix']}{task_id}"
            return self.client.get(status_key)
        except RedisError as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return None
    
    def update_task_progress(self, task_id, progress):
        """更新任务进度"""
        if not self.is_connected():
            return False
            
        try:
            progress_key = f"{REDIS_KEYS['task_progress_prefix']}{task_id}"
            self.client.set(progress_key, str(progress))
            self.client.expire(progress_key, REDIS_EXPIRE['progress'])
            return True
        except RedisError as e:
            logger.error(f"更新任务进度失败: {str(e)}")
            return False
    
    def get_task_progress(self, task_id):
        """获取任务进度"""
        if not self.is_connected():
            return None
            
        try:
            progress_key = f"{REDIS_KEYS['task_progress_prefix']}{task_id}"
            progress = self.client.get(progress_key)
            return int(progress) if progress else 0
        except RedisError as e:
            logger.error(f"获取任务进度失败: {str(e)}")
            return None
    
    def delete_task(self, task_id):
        """删除任务"""
        if not self.is_connected():
            return False
            
        try:
            # 删除任务详情
            task_key = f"{REDIS_KEYS['task_prefix']}{task_id}"
            self.client.delete(task_key)
            
            # 删除任务状态
            status_key = f"{REDIS_KEYS['task_status_prefix']}{task_id}"
            self.client.delete(status_key)
            
            # 删除任务进度
            progress_key = f"{REDIS_KEYS['task_progress_prefix']}{task_id}"
            self.client.delete(progress_key)
            
            return True
        except RedisError as e:
            logger.error(f"删除任务失败: {str(e)}")
            return False
    
    def get_queue_length(self):
        """获取队列长度"""
        if not self.is_connected():
            return 0
            
        try:
            return self.client.llen(REDIS_KEYS['task_queue'])
        except RedisError as e:
            logger.error(f"获取队列长度失败: {str(e)}")
            return 0
    
    def get_queue_tasks(self):
        """获取队列中的所有任务"""
        if not self.is_connected():
            return []
            
        try:
            # 获取队列中的所有任务（不删除）
            tasks = []
            queue_length = self.client.llen(REDIS_KEYS['task_queue'])
            
            if queue_length > 0:
                # 使用LRANGE获取所有任务，不删除
                task_data_list = self.client.lrange(REDIS_KEYS['task_queue'], 0, -1)
                for task_data in task_data_list:
                    try:
                        task_dict = json.loads(task_data)
                        tasks.append(task_dict)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析队列任务数据失败: {str(e)}")
                        continue
            
            return tasks
        except RedisError as e:
            logger.error(f"获取队列任务失败: {str(e)}")
            return []
    
    def clear_queue(self):
        """清空队列"""
        if not self.is_connected():
            return False
            
        try:
            self.client.delete(REDIS_KEYS['task_queue'])
            return True
        except RedisError as e:
            logger.error(f"清空队列失败: {str(e)}")
            return False 
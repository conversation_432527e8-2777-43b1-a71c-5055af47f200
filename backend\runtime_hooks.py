"""
运行时钩子文件
用于在PyInstaller打包的应用程序启动时设置必要的环境变量和路径
"""
import os
import sys

def setup_gdal_environment():
    """设置GDAL环境变量"""
    try:
        # 获取应用程序的基础路径
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的临时目录
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        # 设置GDAL数据路径
        gdal_data_path = os.path.join(base_path, 'gdal-data')
        if os.path.exists(gdal_data_path):
            os.environ['GDAL_DATA'] = gdal_data_path
        
        # 设置PROJ数据路径
        proj_data_path = os.path.join(base_path, 'proj-data')
        if os.path.exists(proj_data_path):
            os.environ['PROJ_LIB'] = proj_data_path
        
        # 设置GDAL驱动路径
        gdal_driver_path = os.path.join(base_path, 'gdal-plugins')
        if os.path.exists(gdal_driver_path):
            os.environ['GDAL_DRIVER_PATH'] = gdal_driver_path
        
        print(f"GDAL环境设置完成:")
        print(f"  GDAL_DATA: {os.environ.get('GDAL_DATA', '未设置')}")
        print(f"  PROJ_LIB: {os.environ.get('PROJ_LIB', '未设置')}")
        print(f"  GDAL_DRIVER_PATH: {os.environ.get('GDAL_DRIVER_PATH', '未设置')}")
        
    except Exception as e:
        print(f"设置GDAL环境时出错: {str(e)}")

def setup_fiona_environment():
    """设置Fiona环境变量"""
    try:
        # Fiona通常会自动处理环境设置，但我们可以添加一些优化
        os.environ['FIONA_ENV'] = 'production'
        
        # 禁用一些不需要的警告
        os.environ['CPL_LOG_ERRORS'] = 'OFF'
        os.environ['CPL_DEBUG'] = 'OFF'
        
        print("Fiona环境设置完成")
        
    except Exception as e:
        print(f"设置Fiona环境时出错: {str(e)}")

# 在模块导入时自动执行环境设置
if __name__ == "__main__":
    print("正在设置GDB解析库环境...")
    setup_gdal_environment()
    setup_fiona_environment()
    print("环境设置完成")
else:
    # 作为钩子被调用时
    setup_gdal_environment()
    setup_fiona_environment()

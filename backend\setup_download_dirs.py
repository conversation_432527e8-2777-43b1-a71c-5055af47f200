#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置下载目录结构的脚本
用于确保生产环境下的下载功能正常工作
"""

import os
import sys

def setup_download_directories():
    """创建下载目录结构"""
    
    # 获取当前目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe，使用exe所在目录
        base_dir = os.path.dirname(sys.executable)
    else:
        # 在开发环境下，使用当前文件所在目录
        base_dir = os.path.dirname(os.path.abspath(__file__))
    
    print(f"基础目录: {base_dir}")
    
    # 定义需要创建的目录结构
    download_dirs = [
        'download',
        'download/fme',
        'download/gispro', 
        'download/GSI'
    ]
    
    # 创建目录
    for dir_path in download_dirs:
        full_path = os.path.join(base_dir, dir_path)
        if not os.path.exists(full_path):
            os.makedirs(full_path)
            print(f"创建目录: {full_path}")
        else:
            print(f"目录已存在: {full_path}")
    
    # 检查是否存在下载文件
    download_files = {
        'download/fme/FME.zip': 'FME桌面端安装包',
        'download/gispro/GISPRO.zip': 'ArcGIS Pro安装包',
        'download/GSI/GSI.zip': '平台离线部署客户端'
    }
    
    print("\n检查下载文件:")
    for file_path, description in download_files.items():
        full_path = os.path.join(base_dir, file_path)
        if os.path.exists(full_path):
            file_size = os.path.getsize(full_path)
            print(f"✓ {description}: {full_path} ({file_size} bytes)")
        else:
            print(f"✗ {description}: {full_path} (文件不存在)")
    
    print("\n目录结构设置完成!")
    print("注意: 如果下载文件不存在，请手动将对应的安装包放置到相应目录中")

if __name__ == '__main__':
    setup_download_directories()

#!/usr/bin/env python3
"""
测试质检配置API
"""

import requests
import json
import sys

def test_quality_config_apis():
    """测试质检配置相关API"""
    base_url = 'http://localhost:8488'
    
    print("测试质检配置API...")
    
    # 1. 测试创建配置
    print("\n1. 测试创建配置")
    try:
        config_data = {
            'name': '测试配置_' + str(int(time.time())),
            'description': '这是一个测试配置',
            'gdb_path': 'temp/test/test.gdb',
            'gdb_filename': 'test.gdb'
        }
        
        response = requests.post(f"{base_url}/api/quality/configs", 
                               json=config_data,
                               headers={'X-Username': 'test'}, 
                               timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✓ 配置创建成功，ID: {data.get('data', {}).get('id')}")
                config_id = data.get('data', {}).get('id')
            else:
                print(f"   ✗ 配置创建失败: {data.get('message')}")
                return False
        else:
            print(f"   ✗ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   异常: {e}")
        return False
    
    # 2. 测试获取配置列表
    print("\n2. 测试获取配置列表")
    try:
        response = requests.get(f"{base_url}/api/quality/configs", 
                               headers={'X-Username': 'test'}, 
                               timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                configs = data.get('data', [])
                print(f"   ✓ 获取到 {len(configs)} 个配置")
                for config in configs[:3]:  # 只显示前3个
                    print(f"     - {config.get('name')} (GDB: {config.get('gdb_filename')})")
            else:
                print(f"   ✗ 获取配置列表失败: {data.get('message')}")
        else:
            print(f"   ✗ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试根据GDB文件名获取配置
    print("\n3. 测试根据GDB文件名获取配置")
    try:
        gdb_filename = 'test.gdb'
        response = requests.get(f"{base_url}/api/quality/configs/by-gdb/{gdb_filename}", 
                               headers={'X-Username': 'test'}, 
                               timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                configs = data.get('data', [])
                print(f"   ✓ 获取到 {len(configs)} 个相关配置")
                for config in configs:
                    print(f"     - {config.get('name')} (匹配: {config.get('is_matched')})")
            else:
                print(f"   ✗ 获取配置列表失败: {data.get('message')}")
        else:
            print(f"   ✗ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"   异常: {e}")
    
    return True

if __name__ == "__main__":
    import time
    success = test_quality_config_apis()
    if success:
        print("\n✓ 测试完成")
    else:
        print("\n✗ 测试失败")
        sys.exit(1)

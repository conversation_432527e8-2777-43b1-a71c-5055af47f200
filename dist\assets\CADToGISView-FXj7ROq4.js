import{d as Ha,u as Xa,r as u,M as G,O as pe,o as Pe,a as st,i as C,E as i,aq as Ya,c as B,e as a,w as s,f as m,Y as ot,P as I,Z as W,b as o,J as Za,n as Te,m as _,W as Ja,y as Fe,U as nt,A as d,D as rt,C as k,ad as Qa,B as Ka,F as v,ab as it,a5 as Ue,a0 as el,s as ce,ar as tl,a1 as ut,Q as al,R as Ft,H as dt,I as pt,T as Ge,l as y,a7 as Q,j as ll,_ as sl}from"./index-E0SsINqw.js";import{f as ol}from"./format-CBpsKyOP.js";const nl={class:"tools-container"},rl={key:"convert"},il={class:"steps-progress"},ul={class:"start-container"},dl={class:"start-content"},pl={class:"tool-description"},cl={class:"description-header"},fl={class:"description-content"},ml={class:"feature-list"},vl={class:"feature-item"},_l={class:"feature-item"},gl={class:"feature-item"},yl={class:"feature-item"},wl={class:"process-steps"},bl={class:"step-content"},hl={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},Cl={style:{display:"none"}},xl={class:"step-footer"},kl={class:"step-content"},Dl={class:"upload-container"},Tl={class:"upload-sections"},Ul={class:"upload-section"},Vl={class:"section-title"},Ll={class:"el-upload-list__item custom-upload-item"},Al={class:"el-upload-list__item-name"},Sl={class:"custom-status-label"},El={class:"upload-section"},$l={class:"section-title"},Rl={class:"el-upload-list__item custom-upload-item"},zl={class:"el-upload-list__item-name",style:{"text-align":"left",display:"block"}},Ol={class:"custom-status-label"},Il={class:"file-list-table"},Ml={class:"table-header",style:{"justify-content":"flex-start","flex-direction":"row","align-items":"center"}},Pl={class:"license-tags",style:{display:"flex","align-items":"center","margin-top":"0px"}},Fl={class:"step-footer"},Gl={class:"step-content"},jl={class:"output-settings-container"},Nl={class:"info-confirmation"},Bl={class:"section-card"},Wl={class:"section-card"},ql={class:"output-types"},Hl={class:"section-card"},Xl={class:"geometry-types"},Yl={class:"button-group"},Zl={class:"tip-text"},Jl={class:"step-footer"},Ql={key:"history"},Kl={class:"table-container"},es={style:{display:"flex","justify-content":"center"}},ts={class:"pagination-container"},as={key:0,class:"message-content"},ls={key:6,class:"color-picker-wrapper"},ss={class:"color-value"},os={key:1,class:"no-params"},ns={class:"dialog-footer"},rs={class:"dialog-footer"},is={class:"dialog-footer"},us={style:{display:"flex","justify-content":"center"}},ds={class:"pagination-container"},ps={class:"error-log-content"},cs={class:"dialog-footer"},fs={class:"error-message"},ms={class:"error-message"},vs={class:"error-message"},_s={class:"error-message"},gs={class:"error-message"},ys={class:"dialog-footer"},ws=5e3,bs=Ha({__name:"CADToGISView",setup(hs){const h=Xa(),Gt=u(!1),K=u([]),ee=u([]),ct=u(""),ft=u(""),te=u("convert"),fe=u(1),jt=u(1),Nt=u(1),mt=u(10),vt=u(10),Ve=u(10),me=u(0);u(0);const S=u(localStorage.getItem("token")),U={GET_TOOLS_LIST:"/api/cad2gis/tools/list",GET_TOOLS_COUNT:"/api/cad2gis/tools/count",UPLOAD_TOOL:"/api/cad2gis/tools/upload",DELETE_TOOL:"/api/cad2gis/tools/delete",APPLY_TOOL:"/api/cad2gis/tools/apply",APPROVE_TOOL:"/api/cad2gis/tools/approve",REJECT_TOOL:"/api/cad2gis/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/cad2gis/tools/my-approvals",RUN_TOOL:"/api/cad2gis/tools/run",GET_RUN_RECORDS:"/api/cad2gis/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result",PARSE_TOOL:"/api/cad2gis/tools/parse",DELETE_RESULT:"/api/cad2gis/tools/delete_result",DELETE_APPLICATION:"/api/tools/delete-application",WITHDRAW_APPLICATION:"/api/tools/withdraw-application",RUN_FME:"/api/cad2gis/run_fme",UPDATE_RUN_TIMES:"/api/cad2gis/tools/update-run-times",TOOL_DETAIL:"/api/tools/detail",UPDATE_USAGE_COUNT:"/api/cad2gis/tools/update_usacount",DELETE_FILE:"/api/cad2gis/delete"},Bt=t=>window.location.pathname.startsWith("/gsi/")?`/gsi${t}`:`${C.defaults.baseURL}${t}`,Wt=t=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${t}`:`${window.location.protocol}//${window.location.host}${t}`,qt=t=>{t.name==="history"&&ue()},Ht=G(()=>K.value.filter(t=>t.fmw_name.toLowerCase().includes(ct.value.toLowerCase())||(t.user_project||"").toLowerCase().includes(ct.value.toLowerCase()))),Xt=G(()=>ee.value.filter(t=>t.fmw_name.toLowerCase().includes(ft.value.toLowerCase())||t.project.toLowerCase().includes(ft.value.toLowerCase())));G(()=>{const t=(Nt.value-1)*mt.value,e=t+mt.value;return Ht.value.slice(t,e)}),G(()=>{const t=(jt.value-1)*vt.value,e=t+vt.value;return Xt.value.slice(t,e)});const Le=async()=>{var t;try{if(!((t=h.user)!=null&&t.username)){K.value=[];return}const e=await C.post(U.GET_TOOLS_LIST,{username:h.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});e.data.success?K.value=e.data.data.map(n=>({...n,user_project:n.user_project||n.project||"未指定项目",created_at:n.created_at&&!isNaN(new Date(n.created_at).getTime())?n.created_at:new Date().toISOString()})):(i.error(e.data.message||"获取工具列表失败"),K.value=[])}catch(e){console.error("获取工具列表失败:",e),K.value=[]}},Yt=async()=>{var t;try{if(!((t=h.user)!=null&&t.username)){ee.value=[];return}const e=await C.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{"X-Username":h.user.username}});e.data.success?ee.value=e.data.data.filter(n=>n.status==="已通过").map(n=>({...n,count:parseInt(n.count)||0,usage_count:parseInt(n.usage_count)||0,remaining_count:(parseInt(n.usage_count)||0)-(parseInt(n.count)||0),user_project:n.user_project||"未指定项目",end_date:n.end_date||null,created_at:n.created_at&&!isNaN(new Date(n.created_at).getTime())?n.created_at:new Date().toISOString()})):(i.error(e.data.message||"获取申请列表失败"),ee.value=[])}catch{ee.value=[]}},Ae=(t,e="yyyy-MM-dd HH:mm")=>{if(!t)return"--";try{return ol(new Date(t),e)}catch{return"--"}};pe(()=>h.user,t=>{t!=null&&t.username?Le():(K.value=[],ee.value=[])},{immediate:!0});const ve=u(!1),$=u(null),ae=u([]),g=u({}),E=u(null),je=u({}),Zt=u(!1);u(!1);const Ne=u(!1),le=u([]);pe(Ne,t=>{t||(fe.value=1)});const Jt=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Qt=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,_t=t=>{if(!t)return"-";const e=Math.floor(t/60),n=t%60;return`${e}分${n}秒`},Kt=async t=>{try{await Ge.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=le.value.findIndex(n=>n.task_id===t.task_id);e!==-1&&(le.value.splice(e,1),me.value--),C.post(U.DELETE_RESULT,{task_id:t.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}}).then(n=>{n.data.success?i.success("删除成功"):(le.value.splice(e,0,t),me.value++,i.error(n.data.message||"删除失败"))}).catch(n=>{le.value.splice(e,0,t),me.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&i.error("删除失败，请稍后重试")}},gt=async()=>{try{if(!$.value)return;const t=await C.post(U.GET_RUN_RECORDS,{fmw_id:$.value.fmw_id,username:h.user.username,page:fe.value,page_size:Ve.value},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});t.data.success?(le.value=t.data.data.records,me.value=t.data.data.pagination.total):i.error(t.data.message||"获取运行记录失败")}catch(t){console.error("获取运行记录失败:",t),i.error("获取运行记录失败，请检查网络连接")}},ea=t=>{console.log("页码改变:",t),console.log("当前工具信息:",$.value),fe.value=t,gt()};Pe(()=>{Le(),Y.value=Ye(),$.value={fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw"}}),st(()=>{});const _e=u(!1),ge=u(!1),L=u({fmw_name:"",project:"",description:"",file:null}),q=u({fmw_id:"",file:null}),ta={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},H=u(null),X=u(null),aa=()=>{ve.value=!1},la=()=>{var t;g.value={},ae.value=[],E.value&&E.value.resetFields(),(t=E.value)!=null&&t.$el&&E.value.$el.querySelectorAll(".el-upload").forEach(n=>{var p;const c=(p=n.__vueParentComponent)==null?void 0:p.ctx;c&&typeof c.clearFiles=="function"&&c.clearFiles()})},sa=()=>{_e.value=!1},oa=()=>{j.value&&j.value.resetFields(),L.value={fmw_name:"",project:"",description:"",file:null},H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles()},na=()=>{ge.value=!1},ra=()=>{Se.value&&Se.value.resetFields(),q.value={fmw_id:"",file:null},X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles()},j=u(),Se=u();pe(()=>g.value,t=>{const e={};ae.value.forEach(n=>{Be(n)&&je.value[n.prop]&&(e[n.prop]=je.value[n.prop])}),E.value&&(E.value.clearValidate(),E.value.rules=e)},{deep:!0});const se=u(!1),ia=()=>{Ge.confirm("确定要提交任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{se.value=!0,ka().finally(()=>{se.value=!1})})},ua=async()=>{var t;se.value=!0;try{if(!$.value){i.error("工具信息不完整"),se.value=!1;return}const e=ae.value.filter(r=>Be(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!g.value[r.prop]){let w="";r.type==="file"||r.type==="upload"?w=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?w=`请选择${r.label}`:w=`请填写${r.label}`,i.error(w);return}const c={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:$.value.fmw_id,fmw_name:$.value.fmw_name,fmw_path:$.value.fmw_path,params:{}};for(const r of e){const w=g.value[r.prop];w!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?c.params[r.prop]=g.value[`${r.prop}_value`]:c.params[r.prop]=w)}if(console.log("提交的请求数据:",c),Zt.value)try{const r=await C.post(U.UPDATE_COUNT,{id:$.value.id,username:h.user.username});if(!r.data.success){i.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),i.error("更新使用次数失败，请稍后重试");return}const p=await C.post(U.RUN_FME,c,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(p.data.success){try{const r=await C.post(U.UPDATE_RUN_TIMES,{},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}i.success("任务提交成功"),g.value={},ae.value=[],E.value&&E.value.resetFields(),(t=E.value)!=null&&t.$el&&E.value.$el.querySelectorAll(".el-upload").forEach(w=>{var f;const V=(f=w.__vueParentComponent)==null?void 0:f.ctx;V&&typeof V.clearFiles=="function"&&V.clearFiles()}),ve.value=!1,await Yt(),te.value="history",await ue("cad2gis")}else i.error(p.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),i.error("提交失败，请稍后重试")}finally{se.value=!1}},da=async()=>{if(j.value)try{if(await j.value.validate(),!L.value.file){i.error("请上传工具文件");return}const t=new FormData;t.append("file",L.value.file);const e=await C.post(`${U.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(!e.data.success){i.error(e.data.message||"文件上传失败");return}const n={fmw_id,fmw_name:L.value.fmw_name,project:L.value.project,description:L.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},c=await C.post(`${U.UPLOAD_TOOL}`,n,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});c.data.success?(i.success("工具上传成功"),_e.value=!1,L.value={fmw_name:"",project:"",description:"",file:null},j.value&&j.value.resetFields(),H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles(),await Le()):i.error(c.data.message||"上传失败")}catch(t){console.error("上传工具失败:",t),i.error("参数未填写完整")}},pa=t=>t.name.toLowerCase().endsWith(".fmw")?t.size/1024/1024/1024<10?!0:(i.error("文件大小不能超过10GB"),!1):(i.error("只能上传FMW文件"),!1),ca=t=>(q.value.file=t.raw,!1),fa=async()=>{if(!q.value.file){i.error("请选择更新文件");return}try{const t=new FormData;t.append("file",q.value.file);const e=await C.post(`${U.UPLOAD_FILE}`,t,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(!e.data.success){i.error(e.data.message||"文件上传失败");return}const n={fmw_id:q.value.fmw_id,file_path:e.data.data.path},c=await C.post(`${U.UPDATE_TOOL}`,n,{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});c.data.success?(i.success("工具更新成功"),ge.value=!1,q.value={fmw_id:"",file:null},Se.value&&Se.value.resetFields(),X.value&&typeof X.value.clearFiles=="function"&&X.value.clearFiles(),await Le()):i.error(c.data.message||"更新失败")}catch(t){console.error("更新工具失败:",t),i.error("更新失败，请检查网络连接")}},ma=(t,e)=>{if(!e){g.value[t]="rgb(255, 255, 255)";return}g.value[t]=e;const n=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(n){const[,c,p,r]=n;g.value[`${t}_value`]=`${c},${p},${r}`}else g.value[`${t}_value`]="255,255,255"},Be=t=>{var n;if(!((n=t.component)!=null&&n.visibility))return!0;const e=t.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const c of e.if){const{condition:p,then:r}=c;let w=!1;if(p.allOf)w=p.allOf.every(V=>{if(V.equals){const{parameter:f,value:F}=V.equals;return g.value[f]===F}else if(V.isEnabled){const{parameter:f}=V.isEnabled;return!!g.value[f]}return!1});else if(p.equals){const{parameter:V,value:f}=p.equals;w=g.value[V]===f}else if(p.isEnabled){const{parameter:V}=p.isEnabled;w=!!g.value[V]}if(w)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},Ee=u(!1),yt=u(""),va=t=>{Ve.value=t,gt()},oe=u(!1),ye=u(),We=u(!1),wt=u([]),D=u({tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),_a={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},ga=t=>t.getTime()<Date.now()-864e5,ya=()=>{oe.value=!0,wa()},wa=async()=>{try{const t=await C.get("/api/admin-users");t.data.success?wt.value=t.data.data:i.error("获取审批人失败")}catch{i.error("获取审批人失败")}},ba=async()=>{ye.value&&await ye.value.validate(async t=>{if(t){We.value=!0;try{const e=await C.post(U.APPLY_TOOL,{fmw_id:"cad2gis",fmw_name:"CAD转GIS",applicant:h.user.username,reason:D.value.reason,end_date:D.value.end_date,usage_count:D.value.usage_count,user_project:D.value.user_project,reviewer:D.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});e.data.success?(i.success("申请提交成功"),oe.value=!1,bt(),await Re()):i.error(e.data.message||"申请提交失败")}catch{i.error("申请提交失败")}finally{We.value=!1}}})},bt=()=>{ye.value&&ye.value.resetFields(),Object.assign(D.value,{tool_name:"CAD转GIS",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},$e=u([]),qe=u(!1),M=u(null),b=u(0),ht=u(0),ha=()=>{b.value>0&&b.value--},Ca=()=>{if(b.value===0)b.value++;else if(b.value===1){if(!M.value){i.warning("请先选择一个可用的许可");return}b.value++}else if(b.value===2){if(T.value.length===0){i.warning("请先上传要转换的文件");return}b.value++}else if(b.value===3&&P.value.length===0){i.warning("请至少选择一种输出几何类型");return}};pe(b,t=>{ht.value=t});const R=u(!1),N=async t=>{if(!R.value){R.value=!0;try{t==="next"?await Ca():t==="prev"&&await ha()}catch(e){console.error("步骤切换失败:",e),i.error("步骤切换失败，请重试")}finally{setTimeout(()=>{R.value=!1},250)}}},Ct=t=>{t.key==="ArrowLeft"&&b.value>0?(t.preventDefault(),N("prev")):t.key==="ArrowRight"&&b.value<3&&(t.preventDefault(),N("next"))};Pe(()=>{Re(),document.addEventListener("keydown",Ct)}),st(()=>{document.removeEventListener("keydown",Ct)});const Re=async()=>{qe.value=!0;try{const t=await C.get("/api/tools/my-applications",{params:{source:"cad2gisView",fmw_id:"cad2gis"},headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});if(t.data.success){$e.value=t.data.data;const e=$e.value.find(n=>xt(n));e&&(M.value=e.id)}else i.error(t.data.message||"获取许可列表失败")}catch(t){console.error("获取许可列表失败:",t),i.error("获取许可列表失败")}finally{qe.value=!1}};function we(t){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[t]||{type:"default",text:t}}const ne=u(!1),xa=Ya(async()=>{if(!ne.value){ne.value=!0;try{await Re()}finally{ne.value=!1}}},1e3,{leading:!0,trailing:!1}),ka=async()=>{if(b.value===3)try{const t=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e={点:"Point",多点:"Multipoint",线:"Line",面:"Area",文本:"Text",多文本:"Multitext",曲线:"Curve",圆弧:"Arc",椭圆:"Ellipse",其他:"Other"},n={task_id:t,fmw_id:"cad2gis",fmw_name:"CAD转GIS",fmw_path:"tools/cad2gis/cad2gis.fmw",params:{dwg_path:`temp/${Y.value}`,output_Choice:P.value.join(" "),save_path:`tools/cad2gis/output/${t}`},up_nums:T.value.length},c=sessionStorage.getItem("user");if(!c){i.error("未登录,请先登录");return}try{const p=JSON.parse(c);if(!p.username){i.error("用户信息不完整,请重新登录");return}const r=await C.post(U.RUN_FME,n,{headers:{"X-Username":p.username}});if(r.data.success){const w=await C.post(U.UPDATE_USAGE_COUNT,{id:M.value,username:p.username,file_count:T.value.length},{headers:{"X-Username":p.username}});w.data.success||console.error("更新使用次数失败:",w.data.message),i.success("任务提交成功"),await new Promise(V=>setTimeout(V,1e3)),await ue("cad2gis"),Ia(),b.value=0,M.value=null,T.value=[],P.value=[],Y.value=Ye(),He.value=[],Xe.value=!1,ne.value=!1,await Re(),te.value="history"}else i.error(r.data.message||"任务提交失败")}catch(p){console.error("解析用户信息失败:",p),i.error("用户信息解析失败,请重新登录")}}catch(t){console.error("提交任务失败:",t),i.error("任务提交失败，请重试")}else{if(b.value===2&&!Aa.value){i.warning("请先上传DWG文件");return}b.value++}},Da=t=>{if(we(t.status).text!=="已通过")return;const e=new Date(t.end_date),n=new Date;if(n.setHours(0,0,0,0),e<n){i.warning("该许可已过期");return}if(t.count>=t.usage_count){i.warning("该许可使用次数已达上限");return}if(b.value===2&&T.value.length>0&&(t.usage_count||0)-(t.count||0)-T.value.length<0){i.warning(`当前选择的许可剩余次数不足，无法处理 ${T.value.length} 个文件`);return}M.value=t.id},Ta=({row:t})=>{if(we(t.status).text!=="已通过")return"disabled-row";const e=new Date(t.end_date),n=new Date;return n.setHours(0,0,0,0),e<n||t.count>=t.usage_count?"disabled-row":"clickable-row"},xt=t=>{if(we(t.status).text!=="已通过")return!1;const e=new Date(t.end_date),n=new Date;return n.setHours(0,0,0,0),!(e<n||t.count>=t.usage_count)},He=u([]),T=u([]),Xe=u(!1),Y=u(""),Ye=()=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>t.charAt(Math.floor(Math.random()*t.length))).join("")};Pe(()=>{Y.value=Ye()});const Ua=t=>t.name.toLowerCase().endsWith(".dwg")?!0:(i.error("只能上传DWG格式的文件！"),!1),Va=t=>[".zip",".rar",".7z"].some(n=>t.name.toLowerCase().endsWith(n))?!0:(i.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),kt=(t,e)=>{t.success?([".zip",".rar",".7z"].some(c=>e.name.toLowerCase().endsWith(c))&&(e.extracting=!1,e.parsing=!1,e.parsed=!0,e.dwgCount=t.files.length),T.value=[...T.value,...t.files],i.success("文件上传成功")):i.error(t.message||"文件上传失败")},Dt=t=>{console.error("Upload error:",t),i.error("文件上传失败")},Ze=async t=>{try{const e=await C.post(U.DELETE_FILE,{folderId:Y.value,fileName:t.name});if(e.success){const n=T.value.findIndex(c=>c.name===t.name);n!==-1&&T.value.splice(n,1),i.success("文件删除成功")}else i.error(e.message||"文件删除失败")}catch(e){console.error("Delete error:",e),i.error("文件删除失败")}},La=t=>{Ge.confirm(`确定要移除文件 "${t.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Ze({name:t.name})}).catch(()=>{})},Tt=t=>{if(!t)return"-";if(typeof t=="string")return t;const e=["B","KB","MB","GB"];let n=0,c=t;for(;c>=1024&&n<e.length-1;)c/=1024,n++;return`${c.toFixed(2)} ${e[n]}`},Aa=G(()=>T.value.length>0),re=G(()=>M.value?$e.value.find(t=>t.id===M.value):null),be=G(()=>{var t,e;return(((t=re.value)==null?void 0:t.usage_count)||0)-(((e=re.value)==null?void 0:e.count)||0)-T.value.length}),Je=G(()=>be.value>=0),P=u([]),Qe=["点（Point）","富点（MultiPoint）","线（Line）","面（Area）","文本（Text）","富文本（MultiText）","曲线（Curve）","圆弧（Arc）","椭圆（Ellipse）","其他（Other）"],Sa=()=>{P.value=[...Qe]},Ea=()=>{const t=new Set(P.value);P.value=Qe.filter(e=>!t.has(e))},Z=u([]),Ke=u(!1),ie=u(1),he=u(10),Ce=u(0),ze=u(null),et=u(!1),$a=t=>({running:"warning",success:"success",failed:"danger"})[t]||"info",Ra=t=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[t]||t,za=t=>{he.value=t,ie.value=1},Oa=t=>{ie.value=t},ue=async(t=(e=>(e=$.value)==null?void 0:e.fmw_id)()||"cad2gis")=>{try{Ke.value=!0;const n=await C.post(U.GET_RUN_RECORDS,{fmw_id:t,username:h.user.username,page:ie.value,page_size:he.value},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}});n.data.success?(Z.value=n.data.data.records,Ce.value=n.data.data.pagination.total):i.error(n.data.message||"获取历史记录失败")}catch(n){console.error("获取历史记录失败:",n),i.error("获取历史记录失败")}finally{Ke.value=!1}},Ia=()=>{et.value||(et.value=!0,ze.value=setInterval(async()=>{Z.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await ue()):(console.log("没有运行中的任务，停止轮询"),Ut())},ws))},Ut=()=>{ze.value&&(clearInterval(ze.value),ze.value=null),et.value=!1},Ma=async t=>{try{await Ge.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=Z.value.findIndex(n=>n.task_id===t.task_id);e!==-1&&(Z.value.splice(e,1),Ce.value--),C.post(U.DELETE_RESULT,{task_id:t.task_id},{headers:{Authorization:`Bearer ${S.value}`,"X-Username":h.user.username}}).then(n=>{n.data.success?i.success("删除成功"):(Z.value.splice(e,0,t),Ce.value++,i.error(n.data.message||"删除失败"))}).catch(n=>{Z.value.splice(e,0,t),Ce.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),i.error("删除历史记录失败"))}},Vt=async t=>{try{const e=`tools/cad2gis/output/${t.task_id}/${t.file_name}`,n=`${U.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,c=document.createElement("a");c.style.display="none",document.body.appendChild(c),c.href=Wt(n),c.download=t.file_name,c.click(),setTimeout(()=>{document.body.removeChild(c)},100),i.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),i.error("下载失败，请稍后重试")}},Lt=async t=>{try{t.error_message?(yt.value=t.error_message,Ee.value=!0):i.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),i.error("显示日志失败")}};pe([ie,he],()=>{ue()}),Pe(()=>{ue()}),st(()=>{Ut()});const Oe=u(null);pe(b,async()=>{if(await ll(),Oe.value){const t=Oe.value.querySelector('.step-content[v-show="true"]');t&&(Oe.value.style.height=`${t.scrollHeight}px`)}});const Pa=G(()=>P.value.length>0);return(t,e)=>{var Mt,Pt;const n=m("el-step"),c=m("el-steps"),p=m("el-icon"),r=m("el-button"),w=m("el-skeleton-item"),V=m("el-skeleton"),f=m("el-table-column"),F=m("el-tag"),Fa=m("el-radio"),xe=m("el-table"),tt=m("ArrowLeft"),ke=m("el-upload"),Ie=m("el-descriptions-item"),Ga=m("el-descriptions"),ja=m("el-alert"),z=m("el-checkbox"),Na=m("el-checkbox-group"),At=m("el-card"),St=m("el-tab-pane"),Ba=m("el-tooltip"),Et=m("el-button-group"),$t=m("el-pagination"),Wa=m("el-tabs"),Rt=m("el-option"),zt=m("el-select"),Ot=m("el-input-number"),It=m("el-date-picker"),qa=m("el-color-picker"),J=m("el-input"),O=m("el-form-item"),at=m("el-form"),de=m("el-dialog"),lt=al("loading");return y(),B("div",nl,[a(Wa,{modelValue:te.value,"onUpdate:modelValue":e[10]||(e[10]=l=>te.value=l),class:"cad-tabs",onTabClick:qt},{default:s(()=>[a(St,{label:"转换工具",name:"convert"},{default:s(()=>[a(ot,{name:"slide-fade",mode:"out-in"},{default:s(()=>[I(o("div",rl,[a(At,{class:"license-card",style:{"margin-top":"0"}},{default:s(()=>[a(c,{active:b.value,"finish-status":"success",simple:""},{default:s(()=>[a(n,{title:"开始"}),a(n,{title:"选择许可"}),a(n,{title:"上传文件"}),a(n,{title:"设置输出类型"})]),_:1},8,["active"]),o("div",il,[o("div",{class:"progress-bar",style:Za({width:`${ht.value/4*100}%`})},null,4)]),o("div",{class:Te(["step-content-container",{transitioning:R.value}]),ref_key:"stepContentRef",ref:Oe},[a(ot,{name:"step-fade",mode:"out-in"},{default:s(()=>[(y(),B("div",{key:b.value},[I(o("div",ul,[o("div",dl,[o("div",pl,[o("div",cl,[a(p,{class:"description-icon"},{default:s(()=>[a(_(Ja))]),_:1}),e[34]||(e[34]=o("h3",null,"CAD转GIS工具",-1))]),o("div",fl,[e[44]||(e[44]=o("p",{class:"main-description"}," CAD转GIS工具可以将CAD格式文件（DWG、DXF、DGN）转换为GIS格式数据，支持多种几何类型输出。 ",-1)),o("div",ml,[o("div",vl,[a(p,{class:"feature-icon"},{default:s(()=>[a(_(Fe))]),_:1}),e[35]||(e[35]=o("span",null,"支持DWG、DXF、DGN格式转换",-1))]),o("div",_l,[a(p,{class:"feature-icon"},{default:s(()=>[a(_(Fe))]),_:1}),e[36]||(e[36]=o("span",null,"多种几何类型输出选择",-1))]),o("div",gl,[a(p,{class:"feature-icon"},{default:s(()=>[a(_(Fe))]),_:1}),e[37]||(e[37]=o("span",null,"保持原始图层结构",-1))]),o("div",yl,[a(p,{class:"feature-icon"},{default:s(()=>[a(_(Fe))]),_:1}),e[38]||(e[38]=o("span",null,"批量文件处理能力",-1))])]),o("div",wl,[e[39]||(e[39]=o("p",{class:"steps-title"},"转换流程：",-1)),e[40]||(e[40]=o("span",{class:"step-tag"},"选择许可",-1)),a(p,{class:"arrow-icon"},{default:s(()=>[a(_(nt))]),_:1}),e[41]||(e[41]=o("span",{class:"step-tag"},"上传文件",-1)),a(p,{class:"arrow-icon"},{default:s(()=>[a(_(nt))]),_:1}),e[42]||(e[42]=o("span",{class:"step-tag"},"设置输出",-1)),a(p,{class:"arrow-icon"},{default:s(()=>[a(_(nt))]),_:1}),e[43]||(e[43]=o("span",{class:"step-tag"},"执行转换",-1))])])]),a(r,{type:"primary",size:"large",class:"start-button",onClick:e[0]||(e[0]=l=>N("next")),disabled:R.value},{default:s(()=>[a(p,null,{default:s(()=>[a(_(rt))]),_:1}),e[45]||(e[45]=d(" 开始转换 "))]),_:1},8,["disabled"])])],512),[[W,b.value===0]]),I(o("div",bl,[o("div",hl,[a(r,{type:"primary",onClick:ya},{default:s(()=>[a(p,null,{default:s(()=>[a(_(Qa))]),_:1}),e[46]||(e[46]=d(" 申请许可 "))]),_:1}),a(r,{type:"primary",onClick:_(xa),disabled:ne.value},{default:s(()=>[a(p,{class:Te({"refresh-rotate":ne.value})},{default:s(()=>[a(_(Ka))]),_:1},8,["class"]),e[47]||(e[47]=d(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),qe.value?(y(),k(V,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:s(()=>[a(w,{variant:"text",style:{width:"80px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"180px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(w,{variant:"text",style:{width:"80px"}})]),_:1})):(y(),k(xe,{key:1,data:$e.value,style:{width:"100%"},onRowClick:Da,"row-class-name":Ta},{default:s(()=>[a(f,{type:"index",label:"序号",width:"80",align:"center"}),a(f,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),a(f,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),a(f,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),a(f,{prop:"count",label:"已用次数",width:"100",align:"center"}),a(f,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:s(({row:l})=>[d(v(Ae(l.end_date,"yyyy-MM-dd")),1)]),_:1}),a(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[a(F,{type:we(l.status).type},{default:s(()=>[d(v(we(l.status).text),1)]),_:2},1032,["type"])]),_:1}),a(f,{label:"选择",width:"80",align:"center"},{default:s(({row:l})=>[a(Fa,{modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=A=>M.value=A),label:l.id,disabled:!xt(l),class:"custom-radio"},{default:s(()=>[o("span",Cl,v(l.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),o("div",xl,[a(r,{onClick:e[2]||(e[2]=l=>N("prev")),disabled:R.value},{default:s(()=>[a(p,null,{default:s(()=>[a(tt)]),_:1}),e[48]||(e[48]=d(" 上一步 "))]),_:1},8,["disabled"]),a(r,{type:"primary",onClick:e[3]||(e[3]=l=>N("next")),disabled:!M.value||R.value||be.value<0},{default:s(()=>[e[49]||(e[49]=d(" 下一步 ")),a(p,null,{default:s(()=>[a(_(rt))]),_:1})]),_:1},8,["disabled"])])],512),[[W,b.value===1]]),I(o("div",kl,[o("div",Dl,[e[60]||(e[60]=o("div",{style:{"margin-bottom":"20px",display:"flex","justify-content":"space-between","align-items":"center"}},[o("h3",{style:{margin:"0"}},"上传文件")],-1)),o("div",Tl,[o("div",Ul,[o("div",Vl,[a(p,null,{default:s(()=>[a(_(it))]),_:1}),e[50]||(e[50]=o("span",null,"直接上传DWG文件",-1))]),e[53]||(e[53]=o("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),a(ke,{class:"upload-component",drag:"",action:_(C).defaults.baseURL+"/api/cad2gis/upload","auto-upload":!0,"on-success":kt,"on-error":Dt,"on-remove":Ze,"file-list":He.value,"before-upload":Ua,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[51]||(e[51]=[o("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),file:s(({file:l})=>[o("div",Ll,[o("span",Al,v(l.name),1),o("span",Sl,v(l.status==="success"?"上传成功":l.status==="uploading"?`上传中 ${Math.round(l.percentage)}%`:"上传中"),1)])]),default:s(()=>[a(p,{class:"el-icon--upload"},{default:s(()=>[a(_(Ue))]),_:1}),e[52]||(e[52]=o("div",{class:"el-upload__text"},[d(" 将DWG文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])]),o("div",El,[o("div",$l,[a(p,null,{default:s(()=>[a(_(el))]),_:1}),e[54]||(e[54]=o("span",null,"上传压缩包",-1))]),e[57]||(e[57]=o("div",{class:"section-desc"},"支持上传包含DWG文件的ZIP/RAR/7Z压缩包，将自动解压并提取DWG文件",-1)),a(ke,{class:"upload-component",drag:"",action:Bt("/api/cad2gis/upload"),"auto-upload":!0,"on-success":kt,"on-error":Dt,"on-remove":Ze,"file-list":He.value,"before-upload":Va,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:Y.value}},{tip:s(()=>e[55]||(e[55]=[o("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),file:s(({file:l})=>[o("div",Rl,[o("span",zl,v(l.name),1),o("span",Ol,v(l.status==="success"?l.extracting?"解压中":l.parsing?"解析中":l.parsed?`找到${l.dwgCount||0}个DWG文件`:"解压中":l.status==="uploading"?`上传中 ${Math.round(l.percentage)}%`:"上传中"),1)])]),default:s(()=>[a(p,{class:"el-icon--upload"},{default:s(()=>[a(_(Ue))]),_:1}),e[56]||(e[56]=o("div",{class:"el-upload__text"},[d(" 将压缩包拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])])]),o("div",Il,[o("div",Ml,[e[58]||(e[58]=o("span",{class:"table-title"},"解析后CAD文件",-1)),o("div",Pl,[a(F,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var l;return[d("当前许可次数："+v(((l=re.value)==null?void 0:l.usage_count)||0),1)]}),_:1}),a(F,{type:"info",style:{"margin-right":"10px"}},{default:s(()=>{var l;return[d("已用次数："+v(((l=re.value)==null?void 0:l.count)||0),1)]}),_:1}),a(F,{type:"warning",style:{"margin-right":"10px"}},{default:s(()=>[d("消耗次数："+v(T.value.length),1)]),_:1}),a(F,{type:Je.value?"success":"danger",style:{"margin-right":"10px"}},{default:s(()=>[d(" 消耗后剩余次数："+v(be.value)+" ",1),Je.value?ce("",!0):(y(),k(p,{key:0,style:{"margin-left":"4px"}},{default:s(()=>[a(_(tl))]),_:1}))]),_:1},8,["type"])])]),I((y(),k(xe,{data:T.value,style:{width:"100%"},border:""},{default:s(()=>[a(f,{type:"index",label:"序号",width:"80",align:"center"}),a(f,{prop:"name",label:"文件名","min-width":"200","show-overflow-tooltip":""}),a(f,{prop:"size",label:"文件大小",width:"120",align:"center"},{default:s(({row:l})=>[d(v(Tt(l.size)),1)]),_:1}),a(f,{label:"操作",width:"120",align:"center"},{default:s(({row:l})=>[a(r,{type:"danger",size:"small",onClick:A=>La(l),disabled:Xe.value},{default:s(()=>[a(p,null,{default:s(()=>[a(_(ut))]),_:1}),e[59]||(e[59]=d(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[lt,Xe.value]])])]),o("div",Fl,[a(r,{onClick:e[4]||(e[4]=l=>N("prev")),disabled:R.value},{default:s(()=>[a(p,null,{default:s(()=>[a(tt)]),_:1}),e[61]||(e[61]=d(" 上一步 "))]),_:1},8,["disabled"]),a(r,{type:"primary",onClick:e[5]||(e[5]=l=>N("next")),disabled:T.value.length===0||R.value||be.value<0},{default:s(()=>[e[62]||(e[62]=d(" 下一步 ")),a(p,null,{default:s(()=>[a(_(rt))]),_:1})]),_:1},8,["disabled"])])],512),[[W,b.value===2]]),I(o("div",Gl,[o("div",jl,[o("div",Nl,[o("div",Bl,[e[63]||(e[63]=o("h3",null,"待转换文件",-1)),a(xe,{data:T.value,style:{width:"100%"},size:"small"},{default:s(()=>[a(f,{prop:"name",label:"文件名"}),a(f,{prop:"size",label:"大小",width:"120"},{default:s(l=>[d(v(Tt(l.row.size)),1)]),_:1})]),_:1},8,["data"])]),o("div",Wl,[e[64]||(e[64]=o("h3",null,"许可信息",-1)),a(Ga,{column:1,border:""},{default:s(()=>[a(Ie,{label:"当前许可次数"},{default:s(()=>{var l;return[d(v(((l=re.value)==null?void 0:l.usage_count)||0),1)]}),_:1}),a(Ie,{label:"已用次数"},{default:s(()=>{var l;return[d(v(((l=re.value)==null?void 0:l.count)||0),1)]}),_:1}),a(Ie,{label:"消耗次数"},{default:s(()=>[d(v(T.value.length),1)]),_:1}),a(Ie,{label:"消耗后剩余次数"},{default:s(()=>[o("span",{class:Te({"text-danger":!Je.value})},v(be.value),3)]),_:1})]),_:1})])]),o("div",ql,[o("div",Hl,[e[77]||(e[77]=o("h3",null,"选择输出Geometry类型",-1)),o("div",Xl,[o("div",Yl,[a(r,{type:"primary",onClick:Sa},{default:s(()=>e[65]||(e[65]=[d("全选")])),_:1}),a(r,{onClick:Ea},{default:s(()=>e[66]||(e[66]=[d("反选")])),_:1})]),I(o("div",Zl,[a(ja,{title:"为了避免图形丢失，建议选择全部类型",type:"warning",closable:!1,"show-icon":""})],512),[[W,P.value.length!==Qe.length]]),a(Na,{modelValue:P.value,"onUpdate:modelValue":e[6]||(e[6]=l=>P.value=l)},{default:s(()=>[a(z,{label:"点（Point）"},{default:s(()=>e[67]||(e[67]=[d("点（Point）")])),_:1}),a(z,{label:"富点（MultiPoint）"},{default:s(()=>e[68]||(e[68]=[d("富点（MultiPoint）")])),_:1}),a(z,{label:"线（Line）"},{default:s(()=>e[69]||(e[69]=[d("线（Line）")])),_:1}),a(z,{label:"面（Area）"},{default:s(()=>e[70]||(e[70]=[d("面（Area）")])),_:1}),a(z,{label:"文本（Text）"},{default:s(()=>e[71]||(e[71]=[d("文本（Text）")])),_:1}),a(z,{label:"富文本（MultiText）"},{default:s(()=>e[72]||(e[72]=[d("富文本（MultiText）")])),_:1}),a(z,{label:"曲线（Curve）"},{default:s(()=>e[73]||(e[73]=[d("曲线（Curve）")])),_:1}),a(z,{label:"圆弧（Arc）"},{default:s(()=>e[74]||(e[74]=[d("圆弧（Arc）")])),_:1}),a(z,{label:"椭圆（Ellipse）"},{default:s(()=>e[75]||(e[75]=[d("椭圆（Ellipse）")])),_:1}),a(z,{label:"其他（Other）"},{default:s(()=>e[76]||(e[76]=[d("其他（Other）")])),_:1})]),_:1},8,["modelValue"])])])])]),o("div",Jl,[a(r,{onClick:e[7]||(e[7]=l=>N("prev")),disabled:R.value},{default:s(()=>[a(p,null,{default:s(()=>[a(tt)]),_:1}),e[78]||(e[78]=d(" 上一步 "))]),_:1},8,["disabled"]),b.value===3?(y(),k(r,{key:0,type:"primary",loading:se.value,onClick:ia,disabled:!Pa.value||R.value},{default:s(()=>e[79]||(e[79]=[d("提交任务")])),_:1},8,["loading","disabled"])):ce("",!0)])],512),[[W,b.value===3]])]))]),_:1})],2)]),_:1})],512),[[W,te.value==="convert"]])]),_:1})]),_:1}),a(St,{label:"历史记录",name:"history"},{default:s(()=>[a(ot,{name:"slide-fade",mode:"out-in"},{default:s(()=>[I(o("div",Ql,[a(At,{class:"history-card",style:{"margin-top":"0px"}},{header:s(()=>e[80]||(e[80]=[])),default:s(()=>[o("div",Kl,[I((y(),k(xe,{data:Z.value,style:{width:"100%"}},{default:s(()=>[a(f,{type:"index",label:"序号",width:"80",align:"center"}),a(f,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:s(({row:l})=>[a(Ba,{content:Ae(l.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:s(()=>[o("span",null,v(Ae(l.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),a(f,{prop:"status",label:"状态",width:"100",align:"center"},{default:s(({row:l})=>[a(F,{type:$a(l.status)},{default:s(()=>[d(v(Ra(l.status)),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:s(({row:l})=>[d(v(_t(l.time_consuming)),1)]),_:1}),a(f,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:s(({row:l})=>[d(v(l.file_size),1)]),_:1}),a(f,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),a(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[o("div",es,[a(Et,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(y(),k(r,{key:0,type:"success",size:"small",onClick:A=>Vt(l),disabled:l.status!=="success"},{default:s(()=>[a(p,null,{default:s(()=>[a(_(Ft))]),_:1}),e[81]||(e[81]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):ce("",!0),l.error_message?(y(),k(r,{key:1,type:"info",size:"small",onClick:A=>Lt(l)},{default:s(()=>[a(p,null,{default:s(()=>[a(_(it))]),_:1}),e[82]||(e[82]=d(" 日志 "))]),_:2},1032,["onClick"])):ce("",!0),a(r,{type:"danger",size:"small",onClick:A=>Ma(l)},{default:s(()=>[a(p,null,{default:s(()=>[a(_(ut))]),_:1}),e[83]||(e[83]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[lt,Ke.value]]),o("div",ts,[a($t,{"current-page":ie.value,"onUpdate:currentPage":e[8]||(e[8]=l=>ie.value=l),"page-size":he.value,"onUpdate:pageSize":e[9]||(e[9]=l=>he.value=l),"page-sizes":[10,20,50,100],total:Ce.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:za,onCurrentChange:Oa},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[W,te.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(de,{modelValue:ve.value,"onUpdate:modelValue":e[12]||(e[12]=l=>ve.value=l),title:`运行工具 - ${(Mt=$.value)==null?void 0:Mt.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:aa,onAfterClose:la},{footer:s(()=>[o("span",ns,[a(r,{onClick:e[11]||(e[11]=l=>ve.value=!1)},{default:s(()=>e[85]||(e[85]=[d("取消")])),_:1}),a(r,{type:"primary",onClick:ua},{default:s(()=>e[86]||(e[86]=[d("提交任务")])),_:1})])]),default:s(()=>[a(at,{ref_key:"formRef",ref:E,model:g.value,rules:je.value,"label-width":"200px",size:"small",class:"run-form"},{default:s(()=>[ae.value.length>0?(y(!0),B(dt,{key:0},pt(ae.value,l=>I((y(),k(O,{key:l.prop,label:l.type==="message"?"":l.label,prop:l.prop,required:l.required,class:Te({"message-form-item":l.type==="message"})},{default:s(()=>{var A,De,Me;return[l.type==="message"?(y(),B("div",as,v(l.component.content),1)):l.type==="upload"?(y(),k(ke,Q({key:1,ref_for:!0},l.component.props,{class:["upload-area",{"is-error":((Me=(De=(A=E.value)==null?void 0:A.fields)==null?void 0:De.find(x=>x.prop===l.prop))==null?void 0:Me.validateState)==="error"}],drag:""}),{default:s(()=>[a(p,{class:"el-icon--upload"},{default:s(()=>[a(_(Ue))]),_:1}),e[84]||(e[84]=o("div",{class:"el-upload__text"},[d(" 拖拽文件到此处"),o("br"),d("或"),o("em",null,"点击上传")],-1))]),_:2},1040,["class"])):l.type==="select"?(y(),k(zt,Q({key:2,modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props,{style:{width:"100%"}}),{default:s(()=>[(y(!0),B(dt,null,pt(l.component.options,x=>(y(),k(Rt,{key:x.value,label:x.label,value:x.value,title:x.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):l.type==="number"?(y(),k(Ot,Q({key:3,modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="datetime"?(y(),k(It,Q({key:4,modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"])):l.type==="checkbox"?(y(),k(z,Q({key:5,modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="color"?(y(),B("div",ls,[a(qa,Q({modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props,{onChange:x=>ma(l.prop,x),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),o("span",ss,v(g.value[`${l.prop}_value`]||"255,255,255"),1)])):(y(),k(J,Q({key:7,modelValue:g.value[l.prop],"onUpdate:modelValue":x=>g.value[l.prop]=x,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[W,Be(l)]])),128)):(y(),B("div",os," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),a(de,{modelValue:_e.value,"onUpdate:modelValue":e[17]||(e[17]=l=>_e.value=l),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:sa,onAfterClose:oa},{footer:s(()=>[o("span",rs,[a(r,{onClick:e[16]||(e[16]=l=>_e.value=!1)},{default:s(()=>e[89]||(e[89]=[d("取消")])),_:1}),a(r,{type:"primary",onClick:da},{default:s(()=>e[90]||(e[90]=[d("确定")])),_:1})])]),default:s(()=>[a(at,{ref_key:"uploadFormRef",ref:j,model:L.value,rules:ta,"label-width":"100px",size:"small"},{default:s(()=>[a(O,{label:"工具名称",prop:"fmw_name"},{default:s(()=>[a(J,{modelValue:L.value.fmw_name,"onUpdate:modelValue":e[13]||(e[13]=l=>L.value.fmw_name=l),placeholder:""},null,8,["modelValue"])]),_:1}),a(O,{label:"所属项目",prop:"project"},{default:s(()=>[a(J,{modelValue:L.value.project,"onUpdate:modelValue":e[14]||(e[14]=l=>L.value.project=l),placeholder:""},null,8,["modelValue"])]),_:1}),a(O,{label:"工具描述",prop:"description"},{default:s(()=>[a(J,{modelValue:L.value.description,"onUpdate:modelValue":e[15]||(e[15]=l=>L.value.description=l),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),a(O,{label:"工具文件",prop:"file"},{default:s(()=>{var l,A,De;return[a(ke,{ref_key:"uploadRef",ref:H,class:Te(["upload-demo",{"is-error":((De=(A=(l=j.value)==null?void 0:l.fields)==null?void 0:A.find(Me=>Me.prop==="file"))==null?void 0:De.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":t.handleFileChange,"before-upload":pa,limit:1,accept:".fmw","file-list":[]},{tip:s(()=>e[87]||(e[87]=[o("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[a(p,{class:"el-icon--upload"},{default:s(()=>[a(_(Ue))]),_:1}),e[88]||(e[88]=o("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(de,{modelValue:ge.value,"onUpdate:modelValue":e[19]||(e[19]=l=>ge.value=l),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:na,onAfterClose:ra},{footer:s(()=>[o("span",is,[a(r,{onClick:e[18]||(e[18]=l=>ge.value=!1)},{default:s(()=>e[93]||(e[93]=[d("取消")])),_:1}),a(r,{type:"primary",onClick:fa,disabled:!q.value.file},{default:s(()=>e[94]||(e[94]=[d("确认更新")])),_:1},8,["disabled"])])]),default:s(()=>[a(ke,{ref_key:"updateUploadRef",ref:X,class:"upload-demo","auto-upload":!1,"on-change":ca,limit:1,accept:".fmw",drag:""},{tip:s(()=>e[91]||(e[91]=[o("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:s(()=>[a(p,{class:"el-icon--upload"},{default:s(()=>[a(_(Ue))]),_:1}),e[92]||(e[92]=o("div",{class:"el-upload__text"},[d(" 将文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),a(de,{modelValue:Ne.value,"onUpdate:modelValue":e[22]||(e[22]=l=>Ne.value=l),title:`运行成果 - ${(Pt=$.value)==null?void 0:Pt.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:s(()=>[I((y(),k(xe,{data:le.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:s(()=>[a(f,{type:"index",label:"序号",width:"80",align:"center"}),a(f,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:s(({row:l})=>[d(v(Ae(l.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),a(f,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:s(({row:l})=>[a(F,{type:Jt(l.status)},{default:s(()=>[d(v(Qt(l.status)),1)]),_:2},1032,["type"])]),_:1}),a(f,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:s(({row:l})=>[d(v(_t(l.time_consuming)),1)]),_:1}),a(f,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),a(f,{label:"操作",width:"300",align:"center",fixed:"right"},{default:s(({row:l})=>[o("div",us,[a(Et,null,{default:s(()=>[l.status==="success"&&l.up_nums>0&&l.file_size!=="0.0MB"?(y(),k(r,{key:0,type:"success",size:"small",onClick:A=>Vt(l),disabled:l.status!=="success"},{default:s(()=>[a(p,null,{default:s(()=>[a(_(Ft))]),_:1}),e[95]||(e[95]=d(" 下载 "))]),_:2},1032,["onClick","disabled"])):ce("",!0),l.error_message?(y(),k(r,{key:1,type:"info",size:"small",onClick:A=>Lt(l)},{default:s(()=>[a(p,null,{default:s(()=>[a(_(it))]),_:1}),e[96]||(e[96]=d(" 日志 "))]),_:2},1032,["onClick"])):ce("",!0),a(r,{type:"danger",size:"small",onClick:A=>Kt(l)},{default:s(()=>[a(p,null,{default:s(()=>[a(_(ut))]),_:1}),e[97]||(e[97]=d(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[lt,Gt.value]]),o("div",ds,[a($t,{"current-page":fe.value,"onUpdate:currentPage":e[20]||(e[20]=l=>fe.value=l),"page-size":Ve.value,"onUpdate:pageSize":e[21]||(e[21]=l=>Ve.value=l),"page-sizes":[10,20,50,100],total:me.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:va,onCurrentChange:ea},null,8,["current-page","page-size","total"])])]),_:1},8,["modelValue","title"]),a(de,{modelValue:Ee.value,"onUpdate:modelValue":e[24]||(e[24]=l=>Ee.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:s(()=>[o("span",cs,[a(r,{onClick:e[23]||(e[23]=l=>Ee.value=!1)},{default:s(()=>e[98]||(e[98]=[d("关闭")])),_:1})])]),default:s(()=>[o("div",ps,[o("pre",null,v(yt.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),a(de,{modelValue:oe.value,"onUpdate:modelValue":e[32]||(e[32]=l=>oe.value=l),title:"申请许可-CAD转GIS",width:"500px","close-on-click-modal":!1,onClose:e[33]||(e[33]=l=>oe.value=!1),onAfterClose:bt},{footer:s(()=>[o("span",ys,[a(r,{onClick:e[31]||(e[31]=l=>oe.value=!1)},{default:s(()=>e[99]||(e[99]=[d("取消")])),_:1}),a(r,{type:"primary",loading:We.value,onClick:ba},{default:s(()=>e[100]||(e[100]=[d("提交")])),_:1},8,["loading"])])]),default:s(()=>[a(at,{ref_key:"licenseFormRef",ref:ye,model:D.value,rules:_a,"label-width":"100px",class:"apply-form",size:"small"},{default:s(()=>[a(O,{label:"工具名称",prop:"tool_name"},{default:s(()=>[a(J,{modelValue:D.value.tool_name,"onUpdate:modelValue":e[25]||(e[25]=l=>D.value.tool_name=l),value:"CAD转GIS",disabled:""},null,8,["modelValue"])]),_:1}),a(O,{label:"使用项目",prop:"user_project"},{error:s(({error:l})=>[o("span",fs,v(l),1)]),default:s(()=>[a(J,{modelValue:D.value.user_project,"onUpdate:modelValue":e[26]||(e[26]=l=>D.value.user_project=l),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(O,{label:"申请原因",prop:"reason"},{error:s(({error:l})=>[o("span",ms,v(l),1)]),default:s(()=>[a(J,{modelValue:D.value.reason,"onUpdate:modelValue":e[27]||(e[27]=l=>D.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(O,{label:"有效期",prop:"end_date"},{error:s(({error:l})=>[o("span",vs,v(l),1)]),default:s(()=>[a(It,{modelValue:D.value.end_date,"onUpdate:modelValue":e[28]||(e[28]=l=>D.value.end_date=l),type:"date",placeholder:"请选择有效期","disabled-date":ga,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),a(O,{label:"申请次数",prop:"usage_count"},{error:s(({error:l})=>[o("span",_s,v(l),1)]),default:s(()=>[a(Ot,{modelValue:D.value.usage_count,"onUpdate:modelValue":e[29]||(e[29]=l=>D.value.usage_count=l),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(O,{label:"审批人",prop:"approver"},{error:s(({error:l})=>[o("span",gs,v(l),1)]),default:s(()=>[a(zt,{modelValue:D.value.approver,"onUpdate:modelValue":e[30]||(e[30]=l=>D.value.approver=l),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:s(()=>[(y(!0),B(dt,null,pt(wt.value,l=>(y(),k(Rt,{key:l.username,label:l.real_name,value:l.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Ds=sl(bs,[["__scopeId","data-v-ccde41fe"]]);export{Ds as default};

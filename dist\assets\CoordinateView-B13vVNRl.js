import{d as Ga,u as Ba,r as i,M as O,O as de,o as Fe,a as It,aq as Na,c as P,e as t,w as l,f as g,i as x,E as u,Y as at,P as z,Z as N,b as o,J as Wa,m as v,W as Ot,y as lt,U as st,n as K,C as k,ar as Pt,s as ee,F as _,A as p,D as Ie,ad as qa,B as Ha,ab as ot,a5 as xe,a0 as Xa,a1 as nt,R as zt,Q as Ya,H as rt,I as it,T as Oe,l as y,a7 as te,j as Za,_ as Ja}from"./index-E0SsINqw.js";import{f as Qa}from"./format-CBpsKyOP.js";const Ka={class:"tools-container"},el={key:"convert"},tl={class:"steps-progress"},al={class:"start-container"},ll={class:"start-content"},sl={class:"tool-description"},ol={class:"description-header"},nl={class:"description-content"},rl={class:"feature-list"},il={class:"feature-item"},ul={class:"feature-item"},dl={class:"feature-item"},cl={class:"process-steps"},pl={class:"ip-info-section"},fl={class:"ip-info-header"},ml={class:"ip-info-content"},vl={class:"ip-item"},_l={class:"ip-value"},gl={class:"ip-item"},yl={key:0,class:"external-warning"},wl={class:"warning-text"},hl={class:"step-content"},bl={style:{"margin-bottom":"20px","margin-top":"20px","padding-left":"50px","text-align":"left",display:"flex",gap:"12px","align-items":"center"}},xl=["title"],Cl=["title"],kl={style:{display:"none"}},Vl={class:"step-footer"},Dl={class:"step-content"},Ul={class:"upload-sections"},Ll={class:"upload-section"},Tl={class:"section-title"},Sl={class:"upload-section"},Rl={class:"section-title"},$l={class:"file-list-table",style:{"margin-top":"20px"}},Al={class:"table-header",style:{display:"flex","justify-content":"flex-start","align-items":"center"}},El={class:"license-tags",style:{display:"flex","align-items":"center","margin-left":"30px"}},Fl=["title"],Il={class:"step-footer"},Ol={class:"step-content"},Pl={class:"output-settings-container"},zl={class:"info-confirmation"},Ml={class:"section-card"},jl={class:"section-card"},Gl={class:"output-types"},Bl={class:"section-card"},Nl={style:{display:"flex","flex-direction":"column",gap:"24px","max-width":"350px"}},Wl={style:{display:"flex","align-items":"center"}},ql={style:{display:"flex","align-items":"center"}},Hl={key:0,style:{display:"flex","align-items":"center"}},Xl={class:"step-footer"},Yl={class:"step-content"},Zl={key:"history"},Jl={class:"table-container"},Ql=["title"],Kl={style:{display:"flex","justify-content":"center"}},es={key:0,class:"message-content"},ts={key:6,class:"color-picker-wrapper"},as={class:"color-value"},ls={key:1,class:"no-params"},ss={class:"dialog-footer"},os={class:"dialog-footer"},ns={class:"dialog-footer"},rs={style:{display:"flex","justify-content":"center"}},is={class:"error-log-content"},us={class:"dialog-footer"},ds={class:"error-message"},cs={class:"error-message"},ps={class:"error-message"},fs={class:"error-message"},ms={class:"error-message"},vs={class:"dialog-footer"},_s=5e3,gs=Ga({__name:"CoordinateView",setup(ys){const b=Ba(),Mt=i(!1),ae=i([]),le=i([]),ut=i(""),dt=i(""),se=i("convert"),jt=i(1),Gt=i(1),Bt=i(1),ct=i(10),pt=i(10);i(10);const Pe=i(0);i(0);const $=i(localStorage.getItem("token")),S=i({client_ip:"",server_ip:"",is_local_network:!1,allow_external_access:!1,can_access_coordinate:!1,access_type:""}),ze=i(!1),E={GET_TOOLS_LIST:"/api/Coordinate/tools/list",GET_TOOLS_COUNT:"/api/Coordinate/tools/count",UPLOAD_TOOL:"/api/Coordinate/tools/upload",DELETE_TOOL:"/api/Coordinate/tools/delete",APPLY_TOOL:"/api/Coordinate/tools/apply",APPROVE_TOOL:"/api/Coordinate/tools/approve",REJECT_TOOL:"/api/Coordinate/tools/reject",GET_MY_APPLICATIONS:"/api/tools/my-applications",GET_MY_APPROVALS:"/api/Coordinate/tools/my-approvals",RUN_TOOL:"/api/Coordinate/tools/run",RUN_FME:"/api/Coordinate/run_fme",GET_RUN_RECORDS:"/api/Coordinate/tools/run_records",UPLOAD_FILE:"/api/upload",DOWNLOAD_RESULT:"/api/tools/download_result",UPDATE_USAGE_COUNT:"/api/Coordinate/tools/update_usacount",DELETE_FILE:"/api/Coordinate/delete"},Nt=a=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${a}`:`${window.location.protocol}//${window.location.host}${a}`,Wt=a=>{a.name==="history"&&ye()},qt=O(()=>ae.value.filter(a=>a.fmw_name.toLowerCase().includes(ut.value.toLowerCase())||(a.user_project||"").toLowerCase().includes(ut.value.toLowerCase()))),Ht=O(()=>le.value.filter(a=>a.fmw_name.toLowerCase().includes(dt.value.toLowerCase())||a.project.toLowerCase().includes(dt.value.toLowerCase())));O(()=>{const a=(Bt.value-1)*ct.value,e=a+ct.value;return qt.value.slice(a,e)}),O(()=>{const a=(Gt.value-1)*pt.value,e=a+pt.value;return Ht.value.slice(a,e)});const Xt=async()=>{ze.value=!0;try{const a=await x.get("/api/client/ip-info");a.data.success?S.value=a.data.data:(u.error("获取IP信息失败"),S.value={client_ip:"未知",server_ip:"未知",is_local_network:!1,allow_external_access:!1,can_access_coordinate:!1,access_type:"外网访问"})}catch(a){console.error("获取IP信息失败:",a),u.error("获取IP信息失败"),S.value={client_ip:"未知",server_ip:"未知",is_local_network:!1,allow_external_access:!1,can_access_coordinate:!1,access_type:"外网访问"}}finally{ze.value=!1}},Ce=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){ae.value=[];return}const e=await x.post(E.GET_TOOLS_LIST,{username:b.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});e.data.success?ae.value=e.data.data.map(n=>({...n,user_project:n.user_project||n.project||"未指定项目",created_at:n.created_at&&!isNaN(new Date(n.created_at).getTime())?n.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取工具列表失败"),ae.value=[])}catch(e){console.error("获取工具列表失败:",e),ae.value=[]}},Yt=async()=>{var a;try{if(!((a=b.user)!=null&&a.username)){le.value=[];return}const e=await x.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{"X-Username":b.user.username}});e.data.success?le.value=e.data.data.filter(n=>n.status==="已通过").map(n=>({...n,count:parseInt(n.count)||0,usage_count:parseInt(n.usage_count)||0,remaining_count:(parseInt(n.usage_count)||0)-(parseInt(n.count)||0),user_project:n.user_project||"未指定项目",end_date:n.end_date||null,created_at:n.created_at&&!isNaN(new Date(n.created_at).getTime())?n.created_at:new Date().toISOString()})):(u.error(e.data.message||"获取申请列表失败"),le.value=[])}catch{le.value=[]}},ke=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return Qa(new Date(a),e)}catch{return"--"}};de(()=>b.user,a=>{a!=null&&a.username?Ce():(ae.value=[],le.value=[])},{immediate:!0});const ce=i(!1),M=i(null),oe=i([]),w=i({}),R=i(null),Me=i({}),Zt=i(!1);i(!1);const je=i(!1),pe=i([]);de(je,a=>{a||(jt.value=1)});const Jt=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",Qt=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,ft=a=>{if(!a)return"-";const e=Math.floor(a/60),n=a%60;return`${e}分${n}秒`},Kt=async a=>{try{await Oe.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=pe.value.findIndex(n=>n.task_id===a.task_id);e!==-1&&(pe.value.splice(e,1),Pe.value--),x.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}}).then(n=>{n.data.success?u.success("删除成功"):(pe.value.splice(e,0,a),Pe.value++,u.error(n.data.message||"删除失败"))}).catch(n=>{pe.value.splice(e,0,a),Pe.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&u.error("删除失败，请稍后重试")}};Fe(()=>{Ce(),Xt(),X.value=Xe(),M.value={fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw"}}),It(()=>{});const fe=i(!1),me=i(!1),T=i({fmw_name:"",project:"",description:"",file:null}),W=i({fmw_id:"",file:null}),ea={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},q=i(null),H=i(null),ta=()=>{ce.value=!1},aa=()=>{var a;w.value={},oe.value=[],R.value&&R.value.resetFields(),(a=R.value)!=null&&a.$el&&R.value.$el.querySelectorAll(".el-upload").forEach(n=>{var d;const f=(d=n.__vueParentComponent)==null?void 0:d.ctx;f&&typeof f.clearFiles=="function"&&f.clearFiles()})},la=()=>{fe.value=!1},sa=()=>{G.value&&G.value.resetFields(),T.value={fmw_name:"",project:"",description:"",file:null},q.value&&typeof q.value.clearFiles=="function"&&q.value.clearFiles()},oa=()=>{me.value=!1},na=()=>{Ve.value&&Ve.value.resetFields(),W.value={fmw_id:"",file:null},H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles()},G=i(),Ve=i();de(()=>w.value,a=>{const e={};oe.value.forEach(n=>{Be(n)&&Me.value[n.prop]&&(e[n.prop]=Me.value[n.prop])}),R.value&&(R.value.clearValidate(),R.value.rules=e)},{deep:!0});const Ge=i(!1),ra=async()=>{var a;Ge.value=!0;try{if(!M.value){u.error("工具信息不完整"),Ge.value=!1;return}const e=oe.value.filter(r=>Be(r));console.log("可见的表单项:",e.map(r=>r.prop));for(const r of e)if(r.required&&!w.value[r.prop]){let h="";r.type==="file"||r.type==="upload"?h=`请上传${r.label}`:r.type==="select"||r.type==="dropdown"||r.type==="listbox"?h=`请选择${r.label}`:h=`请填写${r.label}`,u.error(h);return}const f={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:M.value.fmw_id,fmw_name:M.value.fmw_name,fmw_path:M.value.fmw_path,params:{}};for(const r of e){const h=w.value[r.prop];h!=null&&!r.prop.endsWith("_value")&&(r.type==="color"?f.params[r.prop]=w.value[`${r.prop}_value`]:f.params[r.prop]=h)}if(console.log("提交的请求数据:",f),Zt.value)try{const r=await x.post(E.UPDATE_COUNT,{id:M.value.id,username:b.user.username});if(!r.data.success){u.error(r.data.message||"更新使用次数失败");return}}catch(r){console.error("更新使用次数失败:",r),u.error("更新使用次数失败，请稍后重试");return}const d=await x.post(E.RUN_FME,f,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(d.data.success){try{const r=await x.post("/api/Coordinate/tools/update-run-times",{},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});r.data.success||console.error("更新运行次数失败:",r.data.message)}catch(r){console.error("更新运行次数失败:",r)}u.success("任务提交成功"),w.value={},oe.value=[],R.value&&R.value.resetFields(),(a=R.value)!=null&&a.$el&&R.value.$el.querySelectorAll(".el-upload").forEach(h=>{var m;const U=(m=h.__vueParentComponent)==null?void 0:m.ctx;U&&typeof U.clearFiles=="function"&&U.clearFiles()}),ce.value=!1,window.location.reload(),await Yt(),se.value="history",xt()}else u.error(d.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),u.error("提交失败，请稍后重试")}finally{Ge.value=!1}},ia=async()=>{if(G.value)try{if(await G.value.validate(),!T.value.file){u.error("请上传工具文件");return}const a=new FormData;a.append("file",T.value.file);const e=await x.post(`${E.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const n={fmw_id,fmw_name:T.value.fmw_name,project:T.value.project,description:T.value.description,fmw_path:e.data.data.path,file_path:e.data.data.path,data:new Date().toISOString()},f=await x.post(`${E.UPLOAD_TOOL}`,n,{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});f.data.success?(u.success("工具上传成功"),fe.value=!1,T.value={fmw_name:"",project:"",description:"",file:null},G.value&&G.value.resetFields(),q.value&&typeof q.value.clearFiles=="function"&&q.value.clearFiles(),await Ce()):u.error(f.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),u.error("参数未填写完整")}},ua=a=>a.name.toLowerCase().endsWith(".fmw")?a.size/1024/1024/1024<10?!0:(u.error("文件大小不能超过10GB"),!1):(u.error("只能上传FMW文件"),!1),da=a=>(W.value.file=a.raw,!1),ca=async()=>{if(!W.value.file){u.error("请选择更新文件");return}try{const a=new FormData;a.append("file",W.value.file);const e=await x.post(`${E.UPLOAD_FILE}`,a,{headers:{"Content-Type":"multipart/form-data",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(!e.data.success){u.error(e.data.message||"文件上传失败");return}const n={fmw_id:W.value.fmw_id,file_path:e.data.data.path},f=await x.post(`${E.UPDATE_TOOL}`,n,{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});f.data.success?(u.success("工具更新成功"),me.value=!1,W.value={fmw_id:"",file:null},Ve.value&&Ve.value.resetFields(),H.value&&typeof H.value.clearFiles=="function"&&H.value.clearFiles(),await Ce()):u.error(f.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),u.error("更新失败，请检查网络连接")}},pa=(a,e)=>{if(!e){w.value[a]="rgb(255, 255, 255)";return}w.value[a]=e;const n=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(n){const[,f,d,r]=n;w.value[`${a}_value`]=`${f},${d},${r}`}else w.value[`${a}_value`]="255,255,255"},Be=a=>{var n;if(!((n=a.component)!=null&&n.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const f of e.if){const{condition:d,then:r}=f;let h=!1;if(d.allOf)h=d.allOf.every(U=>{if(U.equals){const{parameter:m,value:j}=U.equals;return w.value[m]===j}else if(U.isEnabled){const{parameter:m}=U.isEnabled;return!!w.value[m]}return!1});else if(d.equals){const{parameter:U,value:m}=d.equals;h=w.value[U]===m}else if(d.isEnabled){const{parameter:U}=d.isEnabled;h=!!w.value[U]}if(h)return r==="visibleEnabled"||r==="visibleDisabled"}return!1},De=i(!1),mt=i(""),ne=i(!1),ve=i(),Ne=i(!1),vt=i([]),V=i({tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),fa={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},ma=a=>a.getTime()<Date.now()-864e5,va=()=>{ne.value=!0,_a()},_a=async()=>{try{const a=await x.get("/api/admin-users");a.data.success?vt.value=a.data.data:u.error("获取审批人失败")}catch{u.error("获取审批人失败")}},ga=async()=>{ve.value&&await ve.value.validate(async a=>{if(a){Ne.value=!0;try{const e=await x.post("/api/Coordinate/tools/apply",{fmw_id:"coordinatetransformation",fmw_name:"坐标转换",applicant:b.user.username,reason:V.value.reason,end_date:V.value.end_date,usage_count:V.value.usage_count,user_project:V.value.user_project,reviewer:V.value.approver},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});e.data.success?(u.success("申请提交成功"),ne.value=!1,_t(),await Le()):u.error(e.data.message||"申请提交失败")}catch{u.error("申请提交失败")}finally{Ne.value=!1}}})},_t=()=>{ve.value&&ve.value.resetFields(),Object.assign(V.value,{tool_name:"坐标转换",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})},Ue=i([]),We=i(!1),I=i(null),L=i(0),gt=i(0),qe=()=>{L.value--},He=()=>{if(L.value,L.value===2&&!ge.value){u.warning("许可次数不足，无法进入下一步");return}L.value++};de(L,a=>{setTimeout(()=>{gt.value=a},300)});const Le=async()=>{We.value=!0;try{const a=await x.get("/api/tools/my-applications",{params:{source:"CoordinateView",fmw_id:"coordinatetransformation"},headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});if(a.data.success){Ue.value=a.data.data;const e=Ue.value.find(n=>yt(n));e&&(I.value=e.id)}else u.error(a.data.message||"获取许可列表失败")}catch(a){console.error("获取许可列表失败:",a),u.error("获取许可列表失败")}finally{We.value=!1}};Fe(()=>{Le()});function _e(a){return{审批中:{type:"info",text:"审批中"},已通过:{type:"success",text:"已通过"},已驳回:{type:"danger",text:"已驳回"},已过期:{type:"warning",text:"已过期"},已耗尽:{type:"warning",text:"已耗尽"},可用:{type:"success",text:"可用"}}[a]||{type:"default",text:a}}const re=i(!1),ya=Na(async()=>{if(!re.value){re.value=!0;try{await Le()}finally{re.value=!1}}},1e3,{leading:!0,trailing:!1}),wa=a=>{if(_e(a.status).text!=="已通过")return;const e=new Date(a.end_date),n=new Date;if(n.setHours(0,0,0,0),e<n){u.warning("该许可已过期");return}if(a.count>=a.usage_count){u.warning("该许可使用次数已达上限");return}if(L.value===2&&D.value.length>0&&(a.usage_count||0)-(a.count||0)-D.value.length<0){u.warning(`当前选择的许可剩余次数不足，无法处理 ${D.value.length} 个文件`);return}I.value=a.id},ha=({row:a})=>{if(_e(a.status).text!=="已通过")return"disabled-row";const e=new Date(a.end_date),n=new Date;return n.setHours(0,0,0,0),e<n||a.count>=a.usage_count?"disabled-row":"clickable-row"},yt=a=>{if(_e(a.status).text!=="已通过")return!1;const e=new Date(a.end_date),n=new Date;return n.setHours(0,0,0,0),!(e<n||a.count>=a.usage_count)},ba=i([]),X=i(""),Xe=()=>{const a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:40},()=>a.charAt(Math.floor(Math.random()*a.length))).join("")};Fe(()=>{X.value=Xe()});const Ye=async a=>{try{const e=await x.post(E.DELETE_FILE,{folderId:X.value,fileName:a.name},{headers:{"Content-Type":"application/json"}});if(e.data.success){const n=D.value.findIndex(f=>f.name===a.name);n!==-1&&D.value.splice(n,1),u.success("文件删除成功")}else u.error(e.data.message||"文件删除失败")}catch(e){console.error("Delete error:",e),u.error("文件删除失败")}},xa=a=>{Oe.confirm(`确定要移除文件 "${a.name}" 吗？`,"移除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Ye({name:a.name})}).catch(()=>{})},Ze=O(()=>D.value.some(a=>a.type==="DWG")),ie=O(()=>I.value?Ue.value.find(a=>a.id===I.value):null),Je=O(()=>{var a,e;return(((a=ie.value)==null?void 0:a.usage_count)||0)-(((e=ie.value)==null?void 0:e.count)||0)-D.value.length}),ge=O(()=>Je.value>=0),wt=i([]),Y=i([]),Qe=i(!1),ht=i(1),bt=i(10),Te=i(0),Se=i(null),Ke=i(!1),Ca=a=>({running:"warning",success:"success",failed:"danger"})[a]||"info",ka=a=>({running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"})[a]||a,ye=async(a=(e=>(e=M.value)==null?void 0:e.fmw_id)()||"coordinatetransformation")=>{try{Qe.value=!0;const n=await x.post("/api/Coordinate/tools/run_records",{fmw_id:a,username:b.user.username,page:ht.value,page_size:bt.value},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}});n.data.success?(Y.value=n.data.data.records,Te.value=n.data.data.pagination.total):u.error(n.data.message||"获取历史记录失败")}catch(n){console.error("获取历史记录失败:",n),u.error("获取历史记录失败")}finally{Qe.value=!1}},xt=()=>{Ke.value||(Ke.value=!0,Se.value=setInterval(async()=>{Y.value.some(e=>e.status==="running"||e.status==="pending")?(console.log("检测到运行中的任务，正在更新状态..."),await ye()):(console.log("没有运行中的任务，停止轮询"),Ct())},_s))},Ct=()=>{Se.value&&(clearInterval(Se.value),Se.value=null),Ke.value=!1},Va=async a=>{try{await Oe.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=Y.value.findIndex(n=>n.task_id===a.task_id);e!==-1&&(Y.value.splice(e,1),Te.value--),x.post("/api/Coordinate/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${$.value}`,"X-Username":b.user.username}}).then(n=>{n.data.success?u.success("删除成功"):(Y.value.splice(e,0,a),Te.value++,u.error(n.data.message||"删除失败"))}).catch(n=>{Y.value.splice(e,0,a),Te.value++,u.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&(console.error("删除历史记录失败:",e),u.error("删除历史记录失败"))}},kt=async a=>{try{const e=`tools/coordinatetransformation/output/${a.task_id}/${a.file_name}`,n=`${E.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`,f=document.createElement("a");f.style.display="none",document.body.appendChild(f),f.href=Nt(n),f.download=a.file_name,f.click(),setTimeout(()=>{document.body.removeChild(f)},100),u.success("开始下载")}catch(e){console.error("下载历史记录失败:",e),u.error("下载失败，请稍后重试")}},Vt=async a=>{try{a.error_message?(mt.value=a.error_message,De.value=!0):u.warning("暂无错误信息")}catch(e){console.error("显示日志失败:",e),u.error("显示日志失败")}};de([ht,bt],()=>{ye()}),Fe(()=>{ye()}),It(()=>{Ct()});const Re=i(null);de(L,async()=>{if(await Za(),Re.value){const a=Re.value.querySelector('.step-content[v-show="true"]');a&&(Re.value.style.height=`${a.scrollHeight}px`)}}),O(()=>wt.value.length>0);const Da=i([]),Ua=i([]),D=i([]),Dt=i(!1),La=a=>a.name.toLowerCase().endsWith(".dwg")?!0:(u.error("只能上传DWG格式的文件！"),!1),Ta=a=>[".zip",".rar",".7z"].some(n=>a.name.toLowerCase().endsWith(n))?!0:(u.error("只能上传ZIP、RAR、7Z格式的压缩包！"),!1),Sa=(a,e)=>{a.success&&Array.isArray(a.files)?(a.files.forEach(n=>{n.name&&n.name.toLowerCase().endsWith(".dwg")&&D.value.push({name:n.name,type:"DWG",size:n.size})}),u.success("DWG文件上传成功")):u.error(a.message||"DWG文件上传失败")},Ra=(a,e)=>{a.success&&Array.isArray(a.files)?(a.files.forEach(n=>{if(n.name){let f="";n.name.toLowerCase().endsWith(".dwg")?f="DWG":n.name.toLowerCase().endsWith(".gdb")?f="GDB":n.name.toLowerCase().endsWith(".shp")&&(f="SHP"),f&&D.value.push({name:n.name,type:f,size:n.size})}}),u.success("压缩包解析成功")):u.error(a.message||"压缩包解析失败")},Ut=a=>{u.error("文件上传失败")},Z=i(""),J=i(""),$e=i("Release2007"),$a=O(()=>!(!Z.value||!J.value||Z.value===J.value||Ze.value&&!$e.value||!ge.value)),Aa=async()=>{try{const a=`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,n={trans_form:Array.from(new Set(D.value.map(r=>r.type.toLowerCase()))).join(" "),input_date:`temp/${X.value}`,source_coor:Z.value,target_coor:J.value,save_path:`tools/coordinatetransformation/output/${a}`,...Ze.value?{VERSION_2:$e.value}:{}},f={task_id:a,fmw_id:"coordinatetransformation",fmw_name:"坐标转换",fmw_path:"tools/coordinatetransformation/coordinatetransformation.fmw",params:n,up_nums:D.value.length},d=sessionStorage.getItem("user");if(!d){u.error("未登录,请先登录");return}try{const r=JSON.parse(d);if(!r.username){u.error("用户信息不完整,请重新登录");return}const h=await x.post(E.RUN_FME,f,{headers:{"Content-Type":"application/json","X-Username":r.username}});if(h.data.success){const U=await x.post(E.UPDATE_USAGE_COUNT,{id:I.value,username:r.username,file_count:D.value.length},{headers:{"Content-Type":"application/json","X-Username":r.username}});U.data.success||console.error("更新使用次数失败:",U.data.message),u.success("任务提交成功"),await new Promise(m=>setTimeout(m,1e3)),await ye("coordinatetransformation"),xt(),L.value=0,I.value=null,D.value=[],wt.value=[],X.value=Xe(),ba.value=[],Dt.value=!1,re.value=!1,await Le(),se.value="history"}else u.error(h.data.message||"任务提交失败")}catch(r){console.error("解析用户信息失败:",r),u.error("用户信息解析失败,请重新登录")}}catch(a){console.error("提交任务失败:",a),u.error("任务提交失败，请重试")}},Ea=()=>{Oe.confirm("确定要提交该任务吗？","提交确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Aa()})};return(a,e)=>{var Et,Ft;const n=g("el-step"),f=g("el-steps"),d=g("el-icon"),r=g("el-button"),h=g("el-skeleton-item"),U=g("el-skeleton"),m=g("el-table-column"),j=g("el-tag"),Fa=g("el-radio"),we=g("el-table"),et=g("ArrowLeft"),he=g("el-upload"),Ae=g("el-descriptions-item"),Ia=g("el-descriptions"),A=g("el-option"),be=g("el-select"),Oa=g("el-alert"),Lt=g("el-card"),Tt=g("el-tab-pane"),Pa=g("el-tooltip"),St=g("el-button-group"),za=g("el-tabs"),Rt=g("el-input-number"),$t=g("el-date-picker"),Ma=g("el-checkbox"),ja=g("el-color-picker"),Q=g("el-input"),F=g("el-form-item"),tt=g("el-form"),ue=g("el-dialog"),At=Ya("loading");return y(),P("div",Ka,[t(za,{modelValue:se.value,"onUpdate:modelValue":e[4]||(e[4]=s=>se.value=s),class:"cad-tabs",onTabClick:Wt},{default:l(()=>[t(Tt,{label:"转换工具",name:"convert"},{default:l(()=>[t(at,{name:"slide-fade",mode:"out-in"},{default:l(()=>[z(o("div",el,[t(Lt,{class:"license-card",style:{"margin-top":"0"}},{default:l(()=>[t(f,{active:L.value,"finish-status":"success",simple:""},{default:l(()=>[t(n,{title:"开始"}),t(n,{title:"选择许可"}),t(n,{title:"上传文件"}),t(n,{title:"参数选择"})]),_:1},8,["active"]),o("div",tl,[o("div",{class:"progress-bar",style:Wa({width:`${gt.value/4*100}%`})},null,4)]),o("div",{class:"step-content-container",ref_key:"stepContentRef",ref:Re},[t(at,{name:"step-fade",mode:"out-in"},{default:l(()=>{var s;return[(y(),P("div",{key:L.value},[z(o("div",al,[o("div",ll,[o("div",sl,[o("div",ol,[t(d,{class:"description-icon"},{default:l(()=>[t(v(Ot))]),_:1}),e[26]||(e[26]=o("h3",null,"坐标转换工具",-1))]),o("div",nl,[e[35]||(e[35]=o("p",{class:"main-description"}," 坐标转换工具支持多种格式文件的坐标系转换，帮助您快速完成不同坐标系之间的数据转换。 ",-1)),o("div",rl,[o("div",il,[t(d,{class:"feature-icon"},{default:l(()=>[t(v(lt))]),_:1}),e[27]||(e[27]=o("span",null,"支持SHP、DWG、GDB等多种格式",-1))]),o("div",ul,[t(d,{class:"feature-icon"},{default:l(()=>[t(v(lt))]),_:1}),e[28]||(e[28]=o("span",null,"支持常用坐标系、独立坐标系相互转换",-1))]),o("div",dl,[t(d,{class:"feature-icon"},{default:l(()=>[t(v(lt))]),_:1}),e[29]||(e[29]=o("span",null,"批量文件处理能力",-1))])]),o("div",cl,[e[30]||(e[30]=o("p",{class:"steps-title"},"转换流程：",-1)),e[31]||(e[31]=o("span",{class:"step-tag"},"选择许可",-1)),t(d,{class:"arrow-icon"},{default:l(()=>[t(v(st))]),_:1}),e[32]||(e[32]=o("span",{class:"step-tag"},"上传文件",-1)),t(d,{class:"arrow-icon"},{default:l(()=>[t(v(st))]),_:1}),e[33]||(e[33]=o("span",{class:"step-tag"},"选择坐标系",-1)),t(d,{class:"arrow-icon"},{default:l(()=>[t(v(st))]),_:1}),e[34]||(e[34]=o("span",{class:"step-tag"},"执行转换",-1))])])]),o("div",pl,[o("div",{class:K(["ip-info-card",{"external-access":!S.value.is_local_network}])},[o("div",fl,[t(d,{class:K(["ip-icon",{warning:!S.value.is_local_network}])},{default:l(()=>[S.value.is_local_network?(y(),k(v(Ot),{key:0})):(y(),k(v(Pt),{key:1}))]),_:1},8,["class"]),e[36]||(e[36]=o("span",{class:"ip-title"},"访问信息",-1))]),o("div",ml,[o("div",vl,[e[37]||(e[37]=o("span",{class:"ip-label"},"当前IP：",-1)),o("span",_l,_(S.value.client_ip||"获取中..."),1)]),o("div",gl,[e[38]||(e[38]=o("span",{class:"ip-label"},"访问类型：",-1)),o("span",{class:K(["ip-value",{"external-text":!S.value.is_local_network}])},_(S.value.access_type||"检测中..."),3)]),S.value.can_access_coordinate?ee("",!0):(y(),P("div",yl,[t(d,{class:"warning-icon"},{default:l(()=>[t(v(Pt))]),_:1}),o("span",wl,_(S.value.is_local_network?"系统错误":S.value.allow_external_access?"外网访问已启用":"外网禁止使用坐标转换功能"),1)]))])],2)]),t(r,{type:"primary",size:"large",class:"start-button",onClick:He,disabled:!S.value.can_access_coordinate,loading:ze.value},{default:l(()=>[t(d,null,{default:l(()=>[t(v(Ie))]),_:1}),e[39]||(e[39]=p(" 开始转换 "))]),_:1},8,["disabled","loading"])])],512),[[N,L.value===0]]),z(o("div",hl,[o("div",bl,[t(r,{type:"primary",onClick:va},{default:l(()=>[t(d,null,{default:l(()=>[t(v(qa))]),_:1}),e[40]||(e[40]=p(" 申请许可 "))]),_:1}),t(r,{type:"primary",onClick:v(ya),disabled:re.value},{default:l(()=>[t(d,{class:K({"refresh-rotate":re.value})},{default:l(()=>[t(v(Ha))]),_:1},8,["class"]),e[41]||(e[41]=p(" 刷新许可 "))]),_:1},8,["onClick","disabled"])]),We.value?(y(),k(U,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:l(()=>[t(h,{variant:"text",style:{width:"80px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"150px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"180px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"100px","margin-right":"16px"}}),t(h,{variant:"text",style:{width:"80px"}})]),_:1})):(y(),k(we,{key:1,data:Ue.value,style:{width:"100%"},onRowClick:wa,"row-class-name":ha,"show-overflow-tooltip":!1},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"user_project",label:"项目","min-width":"150"},{default:l(({row:c})=>[o("span",{title:c.user_project},_(c.user_project),9,xl)]),_:1}),t(m,{prop:"reason",label:"原因","min-width":"150"},{default:l(({row:c})=>[o("span",{title:c.reason},_(c.reason),9,Cl)]),_:1}),t(m,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),t(m,{prop:"count",label:"已用次数",width:"100",align:"center"}),t(m,{prop:"end_date",label:"截止时间",width:"180",align:"center"},{default:l(({row:c})=>[p(_(ke(c.end_date,"yyyy-MM-dd")),1)]),_:1}),t(m,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:c})=>[t(j,{type:_e(c.status).type},{default:l(()=>[p(_(_e(c.status).text),1)]),_:2},1032,["type"])]),_:1}),t(m,{label:"选择",width:"80",align:"center"},{default:l(({row:c})=>[t(Fa,{modelValue:I.value,"onUpdate:modelValue":e[0]||(e[0]=B=>I.value=B),label:c.id,disabled:!yt(c),class:"custom-radio"},{default:l(()=>[o("span",kl,_(c.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"])),o("div",Vl,[t(r,{onClick:qe},{default:l(()=>[t(d,null,{default:l(()=>[t(et)]),_:1}),e[42]||(e[42]=p(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:He,disabled:!I.value},{default:l(()=>[e[43]||(e[43]=p(" 下一步 ")),t(d,null,{default:l(()=>[t(v(Ie))]),_:1})]),_:1},8,["disabled"])])],512),[[N,L.value===1]]),z(o("div",Dl,[o("div",Ul,[o("div",Ll,[o("div",Tl,[t(d,null,{default:l(()=>[t(v(ot))]),_:1}),e[44]||(e[44]=o("span",null,"上传DWG文件",-1))]),e[47]||(e[47]=o("div",{class:"section-desc"},"支持直接上传单个或多个DWG文件",-1)),t(he,{class:"upload-component",drag:"",action:v(x).defaults.baseURL+"/api/Coordinate/upload","auto-upload":!0,"on-success":Sa,"on-error":Ut,"on-remove":Ye,"file-list":Da.value,"before-upload":La,accept:".dwg",multiple:"","show-file-list":!0,data:{folderId:X.value}},{tip:l(()=>e[45]||(e[45]=[o("div",{class:"el-upload__tip"}," 支持 .dwg 格式文件 ",-1)])),default:l(()=>[t(d,{class:"el-icon--upload"},{default:l(()=>[t(v(xe))]),_:1}),e[46]||(e[46]=o("div",{class:"el-upload__text"},[p(" 将DWG文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])]),o("div",Sl,[o("div",Rl,[t(d,null,{default:l(()=>[t(v(Xa))]),_:1}),e[48]||(e[48]=o("span",null,"上传压缩包",-1))]),e[51]||(e[51]=o("div",{class:"section-desc"},"支持上传包含DWG/GDB/SHP的ZIP/RAR/7Z压缩包，将自动解压并提取文件",-1)),t(he,{class:"upload-component",drag:"",action:v(x).defaults.baseURL+"/api/Coordinate/upload","auto-upload":!0,"on-success":Ra,"on-error":Ut,"on-remove":Ye,"file-list":Ua.value,"before-upload":Ta,accept:".zip,.rar,.7z",multiple:"","show-file-list":!0,data:{folderId:X.value,username:(s=v(b).user)==null?void 0:s.username,licenseId:I.value}},{tip:l(()=>e[49]||(e[49]=[o("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包 ",-1)])),default:l(()=>[t(d,{class:"el-icon--upload"},{default:l(()=>[t(v(xe))]),_:1}),e[50]||(e[50]=o("div",{class:"el-upload__text"},[p(" 将压缩包拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data"])])]),o("div",$l,[o("div",Al,[e[52]||(e[52]=o("span",{class:"table-title",style:{"font-size":"18px"}},"待转换文件",-1)),o("div",El,[t(j,{type:"info",style:{"margin-right":"10px"}},{default:l(()=>{var c;return[p("当前许可次数："+_(((c=ie.value)==null?void 0:c.usage_count)||0),1)]}),_:1}),t(j,{type:"info",style:{"margin-right":"10px"}},{default:l(()=>{var c;return[p("已用次数："+_(((c=ie.value)==null?void 0:c.count)||0),1)]}),_:1}),t(j,{type:"warning",style:{"margin-right":"10px"}},{default:l(()=>[p("消耗次数："+_(D.value.length),1)]),_:1}),t(j,{type:ge.value?"success":"danger",style:{"margin-right":"10px"}},{default:l(()=>[p(" 消耗后剩余次数："+_(Je.value),1)]),_:1},8,["type"])])]),t(we,{data:D.value,style:{width:"100%"},border:"",size:"small","show-overflow-tooltip":!1},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"name",label:"文件名","min-width":"200"},{default:l(({row:c})=>[o("span",{title:c.name},_(c.name),9,Fl)]),_:1}),t(m,{prop:"type",label:"类型",width:"100",align:"center"}),t(m,{label:"操作",width:"120",align:"center"},{default:l(({row:c})=>[t(r,{type:"danger",size:"small",onClick:B=>xa(c),disabled:Dt.value},{default:l(()=>[t(d,null,{default:l(()=>[t(v(nt))]),_:1}),e[53]||(e[53]=p(" 移除 "))]),_:2},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])]),o("div",Il,[t(r,{onClick:qe},{default:l(()=>[t(d,null,{default:l(()=>[t(et)]),_:1}),e[54]||(e[54]=p(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:He,disabled:D.value.length===0||!ge.value},{default:l(()=>[e[55]||(e[55]=p(" 下一步 ")),t(d,null,{default:l(()=>[t(v(Ie))]),_:1})]),_:1},8,["disabled"])])],512),[[N,L.value===2]]),z(o("div",Ol,[o("div",Pl,[o("div",zl,[o("div",Ml,[e[56]||(e[56]=o("h3",null,"待转换文件",-1)),t(we,{data:D.value,style:{width:"100%"},size:"small"},{default:l(()=>[t(m,{prop:"name",label:"文件名"}),t(m,{prop:"type",label:"类型",width:"100"})]),_:1},8,["data"])]),o("div",jl,[e[57]||(e[57]=o("h3",null,"许可信息",-1)),t(Ia,{column:1,border:""},{default:l(()=>[t(Ae,{label:"当前许可次数"},{default:l(()=>{var c;return[p(_(((c=ie.value)==null?void 0:c.usage_count)||0),1)]}),_:1}),t(Ae,{label:"已用次数"},{default:l(()=>{var c;return[p(_(((c=ie.value)==null?void 0:c.count)||0),1)]}),_:1}),t(Ae,{label:"消耗次数"},{default:l(()=>[p(_(D.value.length),1)]),_:1}),t(Ae,{label:"消耗后剩余次数"},{default:l(()=>[o("span",{class:K({"text-danger":!ge.value})},_(Je.value),3)]),_:1})]),_:1})])]),o("div",Gl,[o("div",Bl,[e[61]||(e[61]=o("h3",null,"参数选择",-1)),o("div",Nl,[o("div",Wl,[e[58]||(e[58]=o("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"源坐标系：",-1)),t(be,{modelValue:Z.value,"onUpdate:modelValue":e[1]||(e[1]=c=>Z.value=c),placeholder:"请选择源坐标系",style:{width:"200px"}},{default:l(()=>[t(A,{label:"苏州独立",value:"苏州独立"}),t(A,{label:"EPSG:4528",value:"EPSG:4528"})]),_:1},8,["modelValue"])]),o("div",ql,[e[59]||(e[59]=o("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"目标坐标系：",-1)),t(be,{modelValue:J.value,"onUpdate:modelValue":e[2]||(e[2]=c=>J.value=c),placeholder:"请选择目标坐标系",style:{width:"200px"}},{default:l(()=>[t(A,{label:"苏州独立",value:"苏州独立"}),t(A,{label:"EPSG:4528",value:"EPSG:4528"})]),_:1},8,["modelValue"])]),Ze.value?(y(),P("div",Hl,[e[60]||(e[60]=o("label",{style:{"font-weight":"bold",width:"100px","text-align":"right","margin-right":"16px","white-space":"nowrap"}},"DWG输出版本：",-1)),t(be,{modelValue:$e.value,"onUpdate:modelValue":e[3]||(e[3]=c=>$e.value=c),placeholder:"请选择DWG输出版本",style:{width:"200px"}},{default:l(()=>[t(A,{label:"2000",value:"Release2000"}),t(A,{label:"2004",value:"Release2004"}),t(A,{label:"2007",value:"Release2007"}),t(A,{label:"2010",value:"Release2010"}),t(A,{label:"2013",value:"Release2013"}),t(A,{label:"2018",value:"Release2018"})]),_:1},8,["modelValue"])])):ee("",!0)]),Z.value&&J.value&&Z.value===J.value?(y(),k(Oa,{key:0,type:"error","show-icon":"",title:"源坐标系和目标坐标系不能相同",style:{margin:"16px 0 0 0"}})):ee("",!0)])])]),o("div",Xl,[t(r,{onClick:qe},{default:l(()=>[t(d,null,{default:l(()=>[t(et)]),_:1}),e[62]||(e[62]=p(" 上一步 "))]),_:1}),t(r,{type:"primary",onClick:Ea,disabled:!$a.value},{default:l(()=>[e[63]||(e[63]=p(" 提交任务 ")),t(d,null,{default:l(()=>[t(v(Ie))]),_:1})]),_:1},8,["disabled"])])],512),[[N,L.value===3]]),z(o("div",Yl,null,512),[[N,L.value===4]])]))]}),_:1})],512)]),_:1})],512),[[N,se.value==="convert"]])]),_:1})]),_:1}),t(Tt,{label:"历史记录",name:"history"},{default:l(()=>[t(at,{name:"slide-fade",mode:"out-in"},{default:l(()=>[z(o("div",Zl,[t(Lt,{class:"history-card",style:{"margin-top":"0px"}},{header:l(()=>e[64]||(e[64]=[])),default:l(()=>[o("div",Jl,[z((y(),k(we,{data:Y.value,style:{width:"100%"}},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"submit_time",label:"提交时间",width:"350",align:"center"},{default:l(({row:s})=>[t(Pa,{content:ke(s.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:l(()=>[o("span",null,_(ke(s.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(m,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:s})=>[t(j,{type:Ca(s.status)},{default:l(()=>[p(_(ka(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"time_consuming",label:"运行耗时",width:"250",align:"center"},{default:l(({row:s})=>[p(_(ft(s.time_consuming)),1)]),_:1}),t(m,{prop:"file_size",label:"文件大小",width:"150",align:"center"},{default:l(({row:s})=>[p(_(s.file_size),1)]),_:1}),t(m,{prop:"up_nums",label:"转换文件数量","min-width":"130",align:"center"},{default:l(({row:s})=>[o("span",{title:s.up_nums},_(s.up_nums),9,Ql)]),_:1}),t(m,{label:"操作",width:"300",align:"center",fixed:"right"},{default:l(({row:s})=>[o("div",Kl,[t(St,null,{default:l(()=>[s.status==="success"&&s.up_nums>0&&s.file_size!=="0.0MB"?(y(),k(r,{key:0,type:"success",size:"small",onClick:c=>kt(s),disabled:s.status!=="success"},{default:l(()=>[t(d,null,{default:l(()=>[t(v(zt))]),_:1}),e[65]||(e[65]=p(" 下载 "))]),_:2},1032,["onClick","disabled"])):ee("",!0),s.error_message?(y(),k(r,{key:1,type:"info",size:"small",onClick:c=>Vt(s)},{default:l(()=>[t(d,null,{default:l(()=>[t(v(ot))]),_:1}),e[66]||(e[66]=p(" 日志 "))]),_:2},1032,["onClick"])):ee("",!0),t(r,{type:"danger",size:"small",onClick:c=>Va(s)},{default:l(()=>[t(d,null,{default:l(()=>[t(v(nt))]),_:1}),e[67]||(e[67]=p(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[At,Qe.value]])])]),_:1})],512),[[N,se.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(ue,{modelValue:ce.value,"onUpdate:modelValue":e[6]||(e[6]=s=>ce.value=s),title:`运行工具 - ${(Et=M.value)==null?void 0:Et.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:ta,onAfterClose:aa},{footer:l(()=>[o("span",ss,[t(r,{onClick:e[5]||(e[5]=s=>ce.value=!1)},{default:l(()=>e[69]||(e[69]=[p("取消")])),_:1}),t(r,{type:"primary",onClick:ra},{default:l(()=>e[70]||(e[70]=[p("提交任务")])),_:1})])]),default:l(()=>[t(tt,{ref_key:"formRef",ref:R,model:w.value,rules:Me.value,"label-width":"200px",size:"small",class:"run-form"},{default:l(()=>[oe.value.length>0?(y(!0),P(rt,{key:0},it(oe.value,s=>z((y(),k(F,{key:s.prop,label:s.type==="message"?"":s.label,prop:s.prop,required:s.required,class:K({"message-form-item":s.type==="message"})},{default:l(()=>{var c,B,Ee;return[s.type==="message"?(y(),P("div",es,_(s.component.content),1)):s.type==="upload"?(y(),k(he,te({key:1,ref_for:!0},s.component.props,{class:["upload-area",{"is-error":((Ee=(B=(c=R.value)==null?void 0:c.fields)==null?void 0:B.find(C=>C.prop===s.prop))==null?void 0:Ee.validateState)==="error"}],drag:""}),{default:l(()=>[t(d,{class:"el-icon--upload"},{default:l(()=>[t(v(xe))]),_:1}),e[68]||(e[68]=o("div",{class:"el-upload__text"},[p(" 拖拽文件到此处"),o("br"),p("或"),o("em",null,"点击上传")],-1))]),_:2},1040,["class"])):s.type==="select"?(y(),k(be,te({key:2,modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props,{style:{width:"100%"}}),{default:l(()=>[(y(!0),P(rt,null,it(s.component.options,C=>(y(),k(A,{key:C.value,label:C.label,value:C.value,title:C.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):s.type==="number"?(y(),k(Rt,te({key:3,modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):s.type==="datetime"?(y(),k($t,te({key:4,modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props),null,16,["modelValue","onUpdate:modelValue"])):s.type==="checkbox"?(y(),k(Ma,te({key:5,modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):s.type==="color"?(y(),P("div",ts,[t(ja,te({modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props,{onChange:C=>pa(s.prop,C),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),o("span",as,_(w.value[`${s.prop}_value`]||"255,255,255"),1)])):(y(),k(Q,te({key:7,modelValue:w.value[s.prop],"onUpdate:modelValue":C=>w.value[s.prop]=C,ref_for:!0},s.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[N,Be(s)]])),128)):(y(),P("div",ls," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(ue,{modelValue:fe.value,"onUpdate:modelValue":e[11]||(e[11]=s=>fe.value=s),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:la,onAfterClose:sa},{footer:l(()=>[o("span",os,[t(r,{onClick:e[10]||(e[10]=s=>fe.value=!1)},{default:l(()=>e[73]||(e[73]=[p("取消")])),_:1}),t(r,{type:"primary",onClick:ia},{default:l(()=>e[74]||(e[74]=[p("确定")])),_:1})])]),default:l(()=>[t(tt,{ref_key:"uploadFormRef",ref:G,model:T.value,rules:ea,"label-width":"100px",size:"small"},{default:l(()=>[t(F,{label:"工具名称",prop:"fmw_name"},{default:l(()=>[t(Q,{modelValue:T.value.fmw_name,"onUpdate:modelValue":e[7]||(e[7]=s=>T.value.fmw_name=s),placeholder:""},null,8,["modelValue"])]),_:1}),t(F,{label:"所属项目",prop:"project"},{default:l(()=>[t(Q,{modelValue:T.value.project,"onUpdate:modelValue":e[8]||(e[8]=s=>T.value.project=s),placeholder:""},null,8,["modelValue"])]),_:1}),t(F,{label:"工具描述",prop:"description"},{default:l(()=>[t(Q,{modelValue:T.value.description,"onUpdate:modelValue":e[9]||(e[9]=s=>T.value.description=s),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(F,{label:"工具文件",prop:"file"},{default:l(()=>{var s,c,B;return[t(he,{ref_key:"uploadRef",ref:q,class:K(["upload-demo",{"is-error":((B=(c=(s=G.value)==null?void 0:s.fields)==null?void 0:c.find(Ee=>Ee.prop==="file"))==null?void 0:B.validateState)==="error"}]),drag:"","auto-upload":!1,"on-change":a.handleFileChange,"before-upload":ua,limit:1,accept:".fmw","file-list":[]},{tip:l(()=>e[71]||(e[71]=[o("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:l(()=>[t(d,{class:"el-icon--upload"},{default:l(()=>[t(v(xe))]),_:1}),e[72]||(e[72]=o("div",{class:"el-upload__text"},[p(" 将文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},8,["class","on-change"])]}),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(ue,{modelValue:me.value,"onUpdate:modelValue":e[13]||(e[13]=s=>me.value=s),title:"更新工具",width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1,onClose:oa,onAfterClose:na},{footer:l(()=>[o("span",ns,[t(r,{onClick:e[12]||(e[12]=s=>me.value=!1)},{default:l(()=>e[77]||(e[77]=[p("取消")])),_:1}),t(r,{type:"primary",onClick:ca,disabled:!W.value.file},{default:l(()=>e[78]||(e[78]=[p("确认更新")])),_:1},8,["disabled"])])]),default:l(()=>[t(he,{ref_key:"updateUploadRef",ref:H,class:"upload-demo","auto-upload":!1,"on-change":da,limit:1,accept:".fmw",drag:""},{tip:l(()=>e[75]||(e[75]=[o("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:l(()=>[t(d,{class:"el-icon--upload"},{default:l(()=>[t(v(xe))]),_:1}),e[76]||(e[76]=o("div",{class:"el-upload__text"},[p(" 将文件拖到此处，或"),o("em",null,"点击上传")],-1))]),_:1},512)]),_:1},8,["modelValue"]),t(ue,{modelValue:je.value,"onUpdate:modelValue":e[14]||(e[14]=s=>je.value=s),title:`运行成果 - ${(Ft=M.value)==null?void 0:Ft.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:l(()=>[z((y(),k(we,{data:pe.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:l(()=>[t(m,{type:"index",label:"序号",width:"80",align:"center"}),t(m,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:l(({row:s})=>[p(_(ke(s.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(m,{prop:"status",label:"运行状态",width:"100",align:"center"},{default:l(({row:s})=>[t(j,{type:Jt(s.status)},{default:l(()=>[p(_(Qt(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"time_consuming",label:"运行耗时",width:"100",align:"center"},{default:l(({row:s})=>[p(_(ft(s.time_consuming)),1)]),_:1}),t(m,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),t(m,{label:"操作",width:"300",align:"center",fixed:"right"},{default:l(({row:s})=>[o("div",rs,[t(St,null,{default:l(()=>[s.status==="success"&&s.up_nums>0&&s.file_size!=="0.0MB"?(y(),k(r,{key:0,type:"success",size:"small",onClick:c=>kt(s),disabled:s.status!=="success"},{default:l(()=>[t(d,null,{default:l(()=>[t(v(zt))]),_:1}),e[79]||(e[79]=p(" 下载 "))]),_:2},1032,["onClick","disabled"])):ee("",!0),s.error_message?(y(),k(r,{key:1,type:"info",size:"small",onClick:c=>Vt(s)},{default:l(()=>[t(d,null,{default:l(()=>[t(v(ot))]),_:1}),e[80]||(e[80]=p(" 日志 "))]),_:2},1032,["onClick"])):ee("",!0),t(r,{type:"danger",size:"small",onClick:c=>Kt(s)},{default:l(()=>[t(d,null,{default:l(()=>[t(v(nt))]),_:1}),e[81]||(e[81]=p(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[At,Mt.value]])]),_:1},8,["modelValue","title"]),t(ue,{modelValue:De.value,"onUpdate:modelValue":e[16]||(e[16]=s=>De.value=s),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:l(()=>[o("span",us,[t(r,{onClick:e[15]||(e[15]=s=>De.value=!1)},{default:l(()=>e[82]||(e[82]=[p("关闭")])),_:1})])]),default:l(()=>[o("div",is,[o("pre",null,_(mt.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),t(ue,{modelValue:ne.value,"onUpdate:modelValue":e[24]||(e[24]=s=>ne.value=s),title:"申请许可-坐标转换",width:"500px","close-on-click-modal":!1,onClose:e[25]||(e[25]=s=>ne.value=!1),onAfterClose:_t},{footer:l(()=>[o("span",vs,[t(r,{onClick:e[23]||(e[23]=s=>ne.value=!1)},{default:l(()=>e[83]||(e[83]=[p("取消")])),_:1}),t(r,{type:"primary",loading:Ne.value,onClick:ga},{default:l(()=>e[84]||(e[84]=[p("提交")])),_:1},8,["loading"])])]),default:l(()=>[t(tt,{ref_key:"licenseFormRef",ref:ve,model:V.value,rules:fa,"label-width":"100px",class:"apply-form",size:"small"},{default:l(()=>[t(F,{label:"工具名称",prop:"tool_name"},{default:l(()=>[t(Q,{modelValue:V.value.tool_name,"onUpdate:modelValue":e[17]||(e[17]=s=>V.value.tool_name=s),value:"坐标转换",disabled:""},null,8,["modelValue"])]),_:1}),t(F,{label:"使用项目",prop:"user_project"},{error:l(({error:s})=>[o("span",ds,_(s),1)]),default:l(()=>[t(Q,{modelValue:V.value.user_project,"onUpdate:modelValue":e[18]||(e[18]=s=>V.value.user_project=s),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),t(F,{label:"申请原因",prop:"reason"},{error:l(({error:s})=>[o("span",cs,_(s),1)]),default:l(()=>[t(Q,{modelValue:V.value.reason,"onUpdate:modelValue":e[19]||(e[19]=s=>V.value.reason=s),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),t(F,{label:"有效期",prop:"end_date"},{error:l(({error:s})=>[o("span",ps,_(s),1)]),default:l(()=>[t($t,{modelValue:V.value.end_date,"onUpdate:modelValue":e[20]||(e[20]=s=>V.value.end_date=s),type:"date",placeholder:"请选择有效期","disabled-date":ma,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),t(F,{label:"申请次数",prop:"usage_count"},{error:l(({error:s})=>[o("span",fs,_(s),1)]),default:l(()=>[t(Rt,{modelValue:V.value.usage_count,"onUpdate:modelValue":e[21]||(e[21]=s=>V.value.usage_count=s),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(F,{label:"审批人",prop:"approver"},{error:l(({error:s})=>[o("span",ms,_(s),1)]),default:l(()=>[t(be,{modelValue:V.value.approver,"onUpdate:modelValue":e[22]||(e[22]=s=>V.value.approver=s),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:l(()=>[(y(!0),P(rt,null,it(vt.value,s=>(y(),k(A,{key:s.username,label:s.real_name,value:s.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),xs=Ja(gs,[["__scopeId","data-v-dbc98e80"]]);export{xs as default};

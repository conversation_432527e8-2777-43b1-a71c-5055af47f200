import{d as Ye,r as d,M as E,N as Ee,o as Ge,O as Fe,a as He,c as Oe,e as a,w as s,f as c,i as h,E as r,b as n,F as y,J as K,A as p,P as Z,Q as Je,C as _,s as C,m as fe,R as Le,S as Qe,T,k as We,l as f,_ as Ke}from"./index-E0SsINqw.js";import{u as Ze}from"./user-DxYiFUU2.js";const et={class:"dashboard"},tt={class:"card-header"},at={class:"header-left"},lt={class:"stats-grid-vertical"},st={class:"stat-item"},nt={class:"stat-value"},ot={class:"number"},rt={class:"stat-item"},ut={class:"stat-value"},it={class:"number"},dt={class:"stat-item"},pt={class:"stat-value"},ct={class:"number"},mt={class:"server-stats"},vt={class:"server-stat-item"},gt={class:"stat-header"},ft={class:"stat-value"},yt={class:"server-stat-item"},_t={class:"stat-header"},bt={class:"stat-value"},wt={class:"server-stat-item"},ht={class:"stat-header"},Ct={class:"stat-value"},Vt={class:"pagination-container"},kt={class:"pagination-container"},xt={class:"pagination-container"},zt={class:"upload-actions"},Ut={class:"dialog-footer"},At=Ye({__name:"HomeView",setup(St){const ee=d(0),te=d(0),ae=d(0),m=d({cpu:{percent:0},memory:{used:0,total:0,percent:0},disk:{used:0,total:0,percent:0,type:"",drive:""}}),le=E(()=>m.value.cpu.percent?m.value.cpu.percent>90||m.value.memory.percent>90?{type:"danger",text:"负载较高"}:m.value.cpu.percent>70||m.value.memory.percent>70?{type:"warning",text:"负载正常"}:{type:"success",text:"运行正常"}:{type:"info",text:"获取中..."});d("myApplications");const k=d(!1),q=d([]),B=d([]),D=d([]),U=d(1),R=d(10),A=d(1),$=d(10),S=d(1),P=d(10),ye=E(()=>{const t=(U.value-1)*R.value,e=t+R.value;return q.value.slice(t,e)}),_e=E(()=>{const t=(A.value-1)*$.value,e=t+$.value;return B.value.slice(t,e)}),be=E(()=>{const t=(S.value-1)*P.value,e=t+P.value;return D.value.slice(t,e)}),we=t=>{U.value=t},he=t=>{R.value=t,U.value=1},Ce=t=>{A.value=t},Ve=t=>{$.value=t,A.value=1},ke=t=>{S.value=t},xe=t=>{P.value=t,S.value=1},b=Ze(),X=We(),se=async()=>{try{const t=await h.get("/api/tools/count");t.data.success&&(ee.value=t.data.data.tools_count,te.value=t.data.data.current_queue_count,ae.value=t.data.data.running_count)}catch{r.error("获取工具总数和任务状态失败")}},ne=async()=>{try{const t=await h.get("/api/server/stats");t.data.success?m.value=t.data.data:r.error(`获取服务器信息失败: ${t.data.message}`)}catch(t){t.response?r.error(`获取服务器信息失败: ${t.response.data.message||"未知错误"}`):t.request?r.error("服务器无响应，请检查后端服务是否运行"):r.error(`请求错误: ${t.message}`)}},M=async()=>{var t,e;k.value=!0;try{if(!((t=b.user)!=null&&t.username)){q.value=[],U.value=1;return}const o=await h.get("/api/tools/my-applications",{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});o.data.success?(q.value=o.data.data,U.value=1):r.error(o.data.message||"获取申请列表失败")}catch{q.value=[],U.value=1}finally{k.value=!1}},j=async()=>{var t,e;k.value=!0;try{if(!((t=b.user)!=null&&t.username)){B.value=[],A.value=1;return}const o=await h.get("/api/tools/my-approvals",{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});o.data.success?(B.value=o.data.data,A.value=1):r.error(o.data.message||"获取审批列表失败")}catch{B.value=[],A.value=1}finally{k.value=!1}},oe=async()=>{var t;k.value=!0;try{if(!((t=b.user)!=null&&t.username)){D.value=[],S.value=1;return}const e=await h.get("/api/requirements/my-approvals",{headers:{"X-Username":b.user.username}});e.data.success?(D.value=e.data.data,S.value=1):r.error(e.data.message||"获取需求审批列表失败")}catch{D.value=[],S.value=1}finally{k.value=!1}},ze=async t=>{var e;try{const o=await h.post("/api/tools/approve",{applicationId:t.id},{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});o.data.success?(r.success("审批通过成功"),await j(),await M()):r.error(o.data.message||"审批失败")}catch{r.error("审批失败")}},Ue=async t=>{var e;try{const{value:o}=await T.prompt("请输入驳回原因","驳回申请",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.{2,100}/,inputErrorMessage:"驳回原因长度在2到100个字符之间"});if(o){const x=await h.post("/api/tools/reject",{applicationId:t.id,reason:o},{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});x.data.success?(r.success("驳回成功"),await j(),await M()):r.error(x.data.message||"驳回失败")}}catch(o){o!=="cancel"&&r.error("驳回失败")}},Ae=async t=>{var e;try{await T.confirm("确定要撤回该申请吗？","撤回申请",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await h.post("/api/tools/withdraw-application",{applicationId:t.id},{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});o.data.success?(r.success("申请已撤回"),await M(),await j()):r.error(o.data.message||"撤回失败")}catch(o){o!=="cancel"&&r.error("撤回申请失败")}},re=t=>{T.alert(t.review_comment||"无驳回原因","驳回原因",{confirmButtonText:"确定",type:"info",customClass:"reject-reason-dialog"})},ue=t=>{switch(t){case"completed":return"success";case"pending":return"warning";default:return"info"}},G=d(!1),i=d(null),Se=t=>{i.value=t,G.value=!0};let N=null;const F=t=>{const e=Math.round(120-t*1.2);return`linear-gradient(90deg, #67C23A, ${`hsl(${Math.max(e,0)}deg, 70%, 45%)`})`},ie=(t,e=!0)=>{if(!t)return"--";try{const o=new Date(t);return isNaN(o.getTime())?"--":o.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",...e?{hour:"2-digit",minute:"2-digit",hour12:!1}:{}}).replace(/\//g,"-")}catch{return"--"}},de=t=>{switch(t){case"已通过":return"success";case"已驳回":return"danger";case"审批中":return"warning";default:return"info"}},Te=async t=>{try{if(!t.attachments||t.attachments.length===0){r.warning("没有可下载的测试数据");return}const e=t.attachments[0];await T.confirm("是否确认下载该测试数据文件？","下载确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"});const o=`/api/requirements/download?filePath=${encodeURIComponent(e)}`;window.open(o),r.success("已发起下载")}catch(e){if(e==="cancel")return;r.error("下载失败，请稍后重试")}},qe=async t=>{var e;try{await T.confirm("确认该需求已完成？","确认完成",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"});const o=new Date().toISOString(),x=await h.post("/api/requirements/complete",{requirementId:t.id,completeTime:o},{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});x.data.success?(r.success("已确认完成"),await oe()):r.error(x.data.message||"确认失败")}catch(o){o!=="cancel"&&r.error("确认失败")}},Be=async t=>{var e;try{await T.confirm("确定要删除该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await h.post("/api/tools/delete-application",{applicationId:t.id},{headers:{"X-Username":(e=b.user)==null?void 0:e.username}});o.data.success?(r.success("删除成功"),await M(),await j()):r.error(o.data.message||"删除失败")}catch(o){o!=="cancel"&&r.error("删除申请失败，请检查网络连接")}},I=d(!1),H=d(),O=d(!1),v=d({application_id:"",fmw_name:"",user_project:"",reason:"",end_date:"",usage_count:1}),De=Ee({user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}]}),Me=t=>t.getTime()<Date.now()-864e5,je=t=>{v.value={application_id:t.id,fmw_name:t.fmw_name,user_project:t.user_project,reason:t.reason,end_date:t.end_date,usage_count:t.usage_count},I.value=!0},Re=async()=>{var t;if(H.value)try{await H.value.validate(),O.value=!0;const e=await h.post("/api/tools/resubmit-application",{applicationId:v.value.application_id,reason:v.value.reason,end_date:v.value.end_date,usage_count:v.value.usage_count,user_project:v.value.user_project},{headers:{"X-Username":(t=b.user)==null?void 0:t.username}});e.data.success?(r.success("申请重新提交成功"),I.value=!1,await M(),await j()):r.error(e.data.message||"申请重新提交失败")}catch(e){e!=="cancel"&&r.error("申请重新提交失败")}finally{O.value=!1}},$e=t=>{T.confirm("确定要同意该审批吗？","确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{ze(t)}).catch(()=>{})},Pe=t=>{t.fmw_name==="CAD转GIS"?X.push({name:"cad2gis"}):t.fmw_name==="数据质检"?X.push({name:"quality"}):t.fmw_name==="坐标转换"?X.push({name:"coordinate"}):X.push({name:"tools",query:{highlightId:t.id}})};return Ge(async()=>{await se(),await ne(),N=setInterval(async()=>{await ne(),await se()},5e3)}),Fe(()=>b.user,t=>{t!=null&&t.username?(M(),j(),oe()):(q.value=[],B.value=[],D.value=[])},{immediate:!0}),He(()=>{N&&(clearInterval(N),N=null)}),(t,e)=>{const o=c("el-tag"),x=c("el-col"),J=c("el-progress"),pe=c("el-row"),Y=c("el-card"),u=c("el-table-column"),w=c("el-button"),L=c("el-table"),Q=c("el-pagination"),V=c("el-input"),g=c("el-form-item"),ce=c("el-radio"),Ie=c("el-radio-group"),me=c("el-date-picker"),Xe=c("el-icon"),ve=c("el-form"),ge=c("el-dialog"),Ne=c("el-input-number"),W=Je("loading");return f(),Oe("div",et,[a(pe,{gutter:20},{default:s(()=>[a(x,{span:24},{default:s(()=>[a(Y,{class:"dashboard-card system-status"},{header:s(()=>[n("div",tt,[n("div",at,[e[23]||(e[23]=n("span",{class:"header-title"},"系统状态",-1)),a(o,{size:"small",type:le.value.type,effect:"light",class:"status-tag"},{default:s(()=>[p(y(le.value.text),1)]),_:1},8,["type"])])])]),default:s(()=>[a(pe,{gutter:30},{default:s(()=>[a(x,{span:12},{default:s(()=>[e[30]||(e[30]=n("div",{class:"section-title"},"工具运行情况",-1)),n("div",lt,[n("div",st,[e[25]||(e[25]=n("div",{class:"stat-title"},"工具总数",-1)),n("div",nt,[n("span",ot,y(ee.value),1),e[24]||(e[24]=n("span",{class:"unit"},"个",-1))])]),n("div",rt,[e[27]||(e[27]=n("div",{class:"stat-title"},"当前队列数量",-1)),n("div",ut,[n("span",it,y(te.value),1),e[26]||(e[26]=n("span",{class:"unit"},"个",-1))])]),n("div",dt,[e[29]||(e[29]=n("div",{class:"stat-title"},"当前运行数量",-1)),n("div",pt,[n("span",ct,y(ae.value),1),e[28]||(e[28]=n("span",{class:"unit"},"个",-1))])])])]),_:1}),a(x,{span:12},{default:s(()=>[e[34]||(e[34]=n("div",{class:"section-title"},"服务器信息",-1)),n("div",mt,[n("div",vt,[n("div",gt,[e[31]||(e[31]=n("span",{class:"stat-title"},"CPU使用率",-1)),n("span",ft,y(m.value.cpu.percent)+"%",1)]),a(J,{percentage:m.value.cpu.percent,format:()=>"",style:K({"--progress-bg":F(m.value.cpu.percent)})},null,8,["percentage","style"])]),n("div",yt,[n("div",_t,[e[32]||(e[32]=n("span",{class:"stat-title"},"内存使用情况",-1)),n("span",bt,y(m.value.memory.used)+"GB / "+y(m.value.memory.total)+"GB",1)]),a(J,{percentage:m.value.memory.percent,format:()=>"",style:K({"--progress-bg":F(m.value.memory.percent)})},null,8,["percentage","style"])]),n("div",wt,[n("div",ht,[e[33]||(e[33]=n("span",{class:"stat-title"},"磁盘使用情况",-1)),n("span",Ct,y(m.value.disk.used)+"GB / "+y(m.value.disk.total)+"GB",1)]),a(J,{percentage:m.value.disk.percent,format:()=>"",style:K({"--progress-bg":F(m.value.disk.percent)})},null,8,["percentage","style"])])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),a(Y,{class:"dashboard-card"},{header:s(()=>e[35]||(e[35]=[n("div",{class:"card-header"},[n("span",null,"我的申请")],-1)])),default:s(()=>[Z((f(),_(L,{data:ye.value,style:{width:"100%"}},{default:s(()=>[a(u,{prop:"fmw_name",label:"工具名称","min-width":"120"}),a(u,{prop:"applicant_real_name",label:"申请人","min-width":"100"}),a(u,{prop:"usage_count",label:"申请次数","min-width":"100",align:"center"}),a(u,{prop:"count",label:"已使用次数","min-width":"100",align:"center"}),a(u,{prop:"end_date",label:"截止日期","min-width":"100",align:"center"}),a(u,{prop:"reason",label:"申请原因","min-width":"120","show-overflow-tooltip":""}),a(u,{prop:"user_project",label:"使用项目","min-width":"120"}),a(u,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:s(({row:l})=>[a(o,{type:de(l.status)},{default:s(()=>[p(y(l.status),1)]),_:2},1032,["type"])]),_:1}),a(u,{label:"操作",width:"180",fixed:"right"},{default:s(({row:l})=>[l.status==="已通过"?(f(),_(w,{key:0,type:"primary",size:"small",style:{"margin-right":"6px"},onClick:z=>Pe(l)},{default:s(()=>e[36]||(e[36]=[p(" 去使用 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="审批中"?(f(),_(w,{key:1,type:"warning",size:"small",onClick:z=>Ae(l)},{default:s(()=>e[37]||(e[37]=[p(" 撤回 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="已驳回"?(f(),_(w,{key:2,type:"info",size:"small",onClick:z=>re(l)},{default:s(()=>e[38]||(e[38]=[p(" 查看原因 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="已驳回"?(f(),_(w,{key:3,type:"primary",size:"small",onClick:z=>je(l)},{default:s(()=>e[39]||(e[39]=[p(" 再次提交 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="已通过"?(f(),_(w,{key:4,type:"danger",size:"small",onClick:z=>Be(l)},{default:s(()=>e[40]||(e[40]=[p(" 删除 ")])),_:2},1032,["onClick"])):C("",!0)]),_:1})]),_:1},8,["data"])),[[W,k.value]]),n("div",Vt,[a(Q,{"current-page":U.value,"onUpdate:currentPage":e[0]||(e[0]=l=>U.value=l),"page-size":R.value,"onUpdate:pageSize":e[1]||(e[1]=l=>R.value=l),"page-sizes":[10,20,50,100],total:q.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:he,onCurrentChange:we},null,8,["current-page","page-size","total"])])]),_:1}),a(Y,{class:"dashboard-card"},{header:s(()=>e[41]||(e[41]=[n("div",{class:"card-header"},[n("span",null,"我的审批")],-1)])),default:s(()=>[Z((f(),_(L,{data:_e.value,style:{width:"100%"}},{default:s(()=>[a(u,{prop:"fmw_name",label:"工具名称","min-width":"120"}),a(u,{prop:"applicant_real_name",label:"申请人","min-width":"100"}),a(u,{prop:"usage_count",label:"申请次数","min-width":"100",align:"center"}),a(u,{prop:"count",label:"已使用次数","min-width":"100",align:"center"}),a(u,{prop:"end_date",label:"截止日期","min-width":"100",align:"center"}),a(u,{prop:"reason",label:"申请原因","min-width":"120","show-overflow-tooltip":""}),a(u,{prop:"user_project",label:"使用项目","min-width":"120"}),a(u,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:s(({row:l})=>[a(o,{type:de(l.status)},{default:s(()=>[p(y(l.status),1)]),_:2},1032,["type"])]),_:1}),a(u,{label:"操作",width:"200",fixed:"right"},{default:s(({row:l})=>[l.status==="审批中"?(f(),_(w,{key:0,type:"success",size:"small",onClick:z=>$e(l)},{default:s(()=>e[42]||(e[42]=[p(" 同意 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="审批中"?(f(),_(w,{key:1,type:"danger",size:"small",onClick:z=>Ue(l)},{default:s(()=>e[43]||(e[43]=[p(" 驳回 ")])),_:2},1032,["onClick"])):C("",!0),l.status==="已驳回"?(f(),_(w,{key:2,type:"info",size:"small",onClick:z=>re(l)},{default:s(()=>e[44]||(e[44]=[p(" 查看原因 ")])),_:2},1032,["onClick"])):C("",!0)]),_:1})]),_:1},8,["data"])),[[W,k.value]]),n("div",kt,[a(Q,{"current-page":A.value,"onUpdate:currentPage":e[2]||(e[2]=l=>A.value=l),"page-size":$.value,"onUpdate:pageSize":e[3]||(e[3]=l=>$.value=l),"page-sizes":[10,20,50,100],total:B.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ve,onCurrentChange:Ce},null,8,["current-page","page-size","total"])])]),_:1}),a(Y,{class:"dashboard-card"},{header:s(()=>e[45]||(e[45]=[n("div",{class:"card-header"},[n("span",null,"需求审批")],-1)])),default:s(()=>[Z((f(),_(L,{data:be.value,style:{width:"100%"}},{empty:s(()=>e[46]||(e[46]=[n("div",{class:"empty-data",style:{"text-align":"center",padding:"20px"}}," 暂无数据 ",-1)])),default:s(()=>[a(u,{prop:"title",label:"项目名称","min-width":"120"}),a(u,{prop:"submitter_real_name",label:"提交人",width:"100"}),a(u,{prop:"priority",label:"期望运行方式","min-width":"100",align:"center"}),a(u,{prop:"delivery_date",label:"交付日期","min-width":"100",align:"center"}),a(u,{prop:"contact",label:"联系人",width:"100"}),a(u,{prop:"phone",label:"联系电话",width:"120"}),a(u,{prop:"submit_time",label:"提交时间","min-width":"160",align:"center"}),a(u,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:s(({row:l})=>[a(o,{type:ue(l.status)},{default:s(()=>[p(y(l.status==="pending"?"开发中":"已完成"),1)]),_:2},1032,["type"])]),_:1}),a(u,{prop:"review_time",label:"实际完成时间",width:"180",align:"center"},{default:s(({row:l})=>[p(y(l.status==="completed"?ie(l.review_time):"--"),1)]),_:1}),a(u,{label:"操作",width:"200",fixed:"right"},{default:s(({row:l})=>[a(w,{type:"primary",size:"small",onClick:z=>Se(l)},{default:s(()=>e[47]||(e[47]=[p(" 查看详情 ")])),_:2},1032,["onClick"]),l.status==="pending"?(f(),_(w,{key:0,type:"success",size:"small",onClick:z=>qe(l)},{default:s(()=>e[48]||(e[48]=[p(" 确认完成 ")])),_:2},1032,["onClick"])):C("",!0)]),_:1})]),_:1},8,["data"])),[[W,k.value]]),n("div",xt,[a(Q,{"current-page":S.value,"onUpdate:currentPage":e[4]||(e[4]=l=>S.value=l),"page-size":P.value,"onUpdate:pageSize":e[5]||(e[5]=l=>P.value=l),"page-sizes":[10,20,50,100],total:D.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:ke},null,8,["current-page","page-size","total"])])]),_:1}),a(ge,{modelValue:G.value,"onUpdate:modelValue":e[15]||(e[15]=l=>G.value=l),title:"需求详情",width:"800px","close-on-click-modal":!1,class:"requirement-detail-dialog",style:{"margin-top":"10vh"}},{default:s(()=>[i.value?(f(),_(ve,{key:0,model:i.value,"label-width":"120px","label-position":"right",class:"form-content"},{default:s(()=>[a(g,{label:"工具名称"},{default:s(()=>[a(V,{modelValue:i.value.title,"onUpdate:modelValue":e[6]||(e[6]=l=>i.value.title=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"所属项目"},{default:s(()=>[a(V,{modelValue:i.value.category,"onUpdate:modelValue":e[7]||(e[7]=l=>i.value.category=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"提交人"},{default:s(()=>[a(V,{modelValue:i.value.submitter_real_name,"onUpdate:modelValue":e[8]||(e[8]=l=>i.value.submitter_real_name=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"期望运行方式"},{default:s(()=>[a(Ie,{modelValue:i.value.priority,"onUpdate:modelValue":e[9]||(e[9]=l=>i.value.priority=l),disabled:""},{default:s(()=>[a(ce,{value:"online"},{default:s(()=>e[49]||(e[49]=[p("在线")])),_:1}),a(ce,{value:"offline"},{default:s(()=>e[50]||(e[50]=[p("离线")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(g,{label:"期望交付日期"},{default:s(()=>[a(me,{modelValue:i.value.delivery_date,"onUpdate:modelValue":e[10]||(e[10]=l=>i.value.delivery_date=l),type:"date",disabled:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(g,{label:"联系人"},{default:s(()=>[a(V,{modelValue:i.value.contact,"onUpdate:modelValue":e[11]||(e[11]=l=>i.value.contact=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"联系电话"},{default:s(()=>[a(V,{modelValue:i.value.phone,"onUpdate:modelValue":e[12]||(e[12]=l=>i.value.phone=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"需求描述"},{default:s(()=>[a(V,{modelValue:i.value.description,"onUpdate:modelValue":e[13]||(e[13]=l=>i.value.description=l),type:"textarea",rows:4,disabled:""},null,8,["modelValue"])]),_:1}),i.value.attachments&&i.value.attachments.length>0?(f(),_(g,{key:0,label:"测试数据"},{default:s(()=>[n("div",zt,[a(w,{type:"primary",size:"small",onClick:e[14]||(e[14]=l=>Te(i.value))},{default:s(()=>[a(Xe,null,{default:s(()=>[a(fe(Le))]),_:1}),e[51]||(e[51]=p(" 下载数据 "))]),_:1})])]),_:1})):C("",!0),a(g,{label:"提交时间"},{default:s(()=>[a(V,{value:i.value?ie(i.value.submit_time):"",disabled:""},null,8,["value"])]),_:1}),a(g,{label:"状态"},{default:s(()=>[a(o,{type:ue(i.value.status)},{default:s(()=>[p(y(i.value.status==="pending"?"开发中":"已完成"),1)]),_:1},8,["type"])]),_:1})]),_:1},8,["model"])):C("",!0)]),_:1},8,["modelValue"]),a(ge,{modelValue:I.value,"onUpdate:modelValue":e[22]||(e[22]=l=>I.value=l),title:"重新提交申请",width:"500px","close-on-click-modal":!1},{footer:s(()=>[n("span",Ut,[a(w,{onClick:e[21]||(e[21]=l=>I.value=!1)},{default:s(()=>e[52]||(e[52]=[p("取消")])),_:1}),a(w,{type:"primary",onClick:Re,loading:O.value},{default:s(()=>e[53]||(e[53]=[p(" 提交申请 ")])),_:1},8,["loading"])])]),default:s(()=>[a(ve,{ref_key:"applyFormRef",ref:H,model:v.value,rules:De,"label-width":"100px",class:"apply-form"},{default:s(()=>[a(g,{label:"工具名称",prop:"fmw_name"},{default:s(()=>[a(V,{modelValue:v.value.fmw_name,"onUpdate:modelValue":e[16]||(e[16]=l=>v.value.fmw_name=l),disabled:""},null,8,["modelValue"])]),_:1}),a(g,{label:"使用项目",prop:"user_project"},{default:s(()=>[a(V,{modelValue:v.value.user_project,"onUpdate:modelValue":e[17]||(e[17]=l=>v.value.user_project=l),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(g,{label:"申请原因",prop:"reason"},{default:s(()=>[a(V,{modelValue:v.value.reason,"onUpdate:modelValue":e[18]||(e[18]=l=>v.value.reason=l),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(g,{label:"有效时间",prop:"end_date"},{default:s(()=>[a(me,{modelValue:v.value.end_date,"onUpdate:modelValue":e[19]||(e[19]=l=>v.value.end_date=l),type:"date",placeholder:"请选择有效时间","disabled-date":Me,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD",locale:fe(Qe)},null,8,["modelValue","default-time","locale"])]),_:1}),a(g,{label:"申请次数",prop:"usage_count"},{default:s(()=>[a(Ne,{modelValue:v.value.usage_count,"onUpdate:modelValue":e[20]||(e[20]=l=>v.value.usage_count=l),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Bt=Ke(At,[["__scopeId","data-v-aa212887"]]);export{Bt as default};

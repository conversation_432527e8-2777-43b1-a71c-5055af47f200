import{d as Ms,u as Ns,r as _,M as H,O as Ol,o as Ps,a as Bs,c as v,e as a,w as t,f as q,i as T,j as Ie,E as g,Y as Hl,P as J,Z as ce,b as s,J as As,m as n,a8 as w,W as Yl,y as wl,U as Xe,a9 as Fs,s as $,aa as Ra,H as G,I as E,a0 as Os,ab as Qe,F as p,ac as k,A as m,D as We,C,ad as Ke,n as qe,B as Ze,ae as Xl,af as me,ag as X,ah as Ql,a5 as Wl,z as Hs,g as Je,Q as Ys,ai as Xs,aj as Qs,R as Ws,a1 as Kl,ak as Zl,al as I,am as Ks,an as Zs,ao as kl,ap as Js,a2 as en,T as el,l as d,_ as ln}from"./index-E0SsINqw.js";const an={class:"tools-container"},tn={key:"quality"},sn={class:"steps-progress"},nn={class:"step-content-container"},on={class:"start-container"},un={class:"start-content-grid"},rn={class:"tool-description"},dn={class:"description-header"},pn={class:"description-content"},cn={class:"feature-list"},mn={class:"feature-item"},vn={class:"feature-item"},fn={class:"feature-item"},_n={class:"process-steps"},yn={class:"quality-dictionary"},gn={class:"description-header"},bn={class:"dictionary-content"},hn={class:"cascade-selector-container"},wn={class:"cascade-columns"},kn={class:"cascade-column level1-column"},Vn={class:"column-header"},Cn={class:"column-content"},Ln=["onClick"],xn={class:"item-text"},$n={key:0,class:"cascade-column level2-column"},Un={class:"column-header"},Sn={class:"column-content"},Dn=["onClick"],In={class:"item-text"},qn={key:1,class:"cascade-column level3-column"},Gn={class:"column-header"},En={class:"column-content"},zn=["onClick"],Tn={class:"item-content"},Rn={class:"item-name"},jn={class:"item-code"},Mn={key:0,class:"dictionary-stats"},Nn={class:"stat-item"},Pn={class:"stat-value"},Bn={class:"start-button-container"},An={class:"step-content"},Fn={class:"license-toolbar"},On={style:{display:"none"}},Hn={class:"step-footer"},Yn={class:"error-message"},Xn={class:"error-message"},Qn={class:"error-message"},Wn={class:"error-message"},Kn={class:"error-message"},Zn={class:"dialog-footer"},Jn={class:"step-content"},eo={class:"three-column-layout"},lo={class:"left-column panel"},ao={class:"mode-selector"},to={class:"upload-section"},so={key:0,class:"upload-progress"},no={class:"progress-text"},oo={key:0,class:"parse-progress-details"},uo={class:"progress-info"},io={key:0,class:"parsed-databases"},ro={class:"database-list"},po={class:"db-name"},co={class:"db-layers"},mo={class:"history-section"},vo={class:"history-controls"},fo={key:0,class:"history-files-list"},_o={class:"number-text"},yo={class:"number-text"},go={key:0,class:"database-names"},bo={key:0,class:"more-dbs"},ho={key:1,style:{color:"#999"}},wo={key:1,class:"empty-history"},ko={key:2,class:"loading-history"},Vo={class:"right-column panel"},Co={key:0,class:"gdb-selection"},Lo={class:"gdb-name-compact"},xo={class:"number-text"},$o={class:"number-text"},Uo={key:1,class:"empty-state"},So={key:0,class:"bottom-section panel"},Do={class:"layer-preview"},Io={class:"layer-preview-header"},qo={class:"layer-stats"},Go={style:{display:"flex","align-items":"center",gap:"8px"}},Eo={key:0},zo={key:1,style:{color:"#999"}},To=["title"],Ro={key:1,style:{color:"#999"}},jo={key:0,class:"more-fields"},Mo={key:1,style:{color:"#999"}},No={class:"pagination-container"},Po={class:"step-footer"},Bo={class:"step-content"},Ao={class:"config-selection-section"},Fo={class:"card-header"},Oo={class:"header-left"},Ho={class:"header-actions"},Yo={key:0,class:"configs-list"},Xo={class:"config-name-cell"},Qo={class:"config-name"},Wo={key:0},Ko={key:1,class:"text-gray-400"},Zo={key:1,class:"empty-configs"},Jo={key:0,class:"config-editor-section"},eu={class:"card-header"},lu={class:"header-left"},au={class:"config-title"},tu={class:"header-actions"},su={key:0,class:"configured-items"},nu={class:"category-header"},ou={key:0},uu={key:1,class:"text-gray-400"},iu={key:0,class:"param-text"},ru={key:1,class:"text-gray-400"},du={key:1,class:"empty-items"},pu={key:1,class:"config-preview-section"},cu={class:"config-summary"},mu={class:"summary-item"},vu={class:"value"},fu={class:"summary-item"},_u={class:"value"},yu={class:"summary-item"},gu={class:"value"},bu={class:"step-footer"},hu={key:"history"},wu={class:"table-container"},ku={style:{display:"flex","justify-content":"center"}},Vu={class:"error-log-content"},Cu={class:"dialog-footer"},Lu={class:"quality-management-container"},xu={class:"management-toolbar"},$u={class:"dialog-footer"},Uu={class:"quality-library-container"},Su={class:"library-toolbar"},Du={class:"library-stats"},Iu={class:"quality-categories-container"},qu=["onClick"],Gu={class:"category-title"},Eu={class:"category-name"},zu={class:"category-content"},Tu={class:"sub-category-header"},Ru={class:"sub-category-name"},ju={class:"quality-items-grid"},Mu={class:"item-header"},Nu={class:"item-title"},Pu={class:"item-name"},Bu={class:"item-content"},Au={class:"item-code"},Fu=["title"],Ou={key:0,class:"item-param-info"},Hu={class:"param-type"},Yu={key:0,class:"param-name"},Xu={class:"item-example"},Qu={key:0,class:"example-img"},Wu=["src","alt"],Ku={key:1,class:"example-placeholder"},Zu={key:0,class:"quality-item-detail"},Ju={class:"detail-section"},ei={class:"section-title"},li={class:"detail-grid"},ai={class:"detail-item"},ti={class:"detail-value"},si={class:"detail-item"},ni={class:"detail-value"},oi={class:"detail-item"},ui={class:"detail-value"},ii={class:"detail-item"},ri={class:"detail-value"},di={class:"detail-item"},pi={class:"detail-item"},ci={class:"detail-value"},mi={class:"detail-section"},vi={class:"section-title"},fi={class:"rule-content"},_i={key:0,class:"detail-section"},yi={class:"section-title"},gi={class:"param-info"},bi={class:"param-name"},hi={class:"param-type"},wi={key:1,class:"detail-section"},ki={class:"section-title"},Vi={class:"example-image"},Ci={class:"image-error"},Li={key:2,class:"detail-section"},xi={class:"section-title"},$i={class:"default-example"},Ui={class:"default-image-placeholder"},Si={class:"dialog-footer"},Di={class:"new-config-info"},Ii={class:"copy-config-info"},qi={class:"config-match-info"},Gi={class:"quality-items-selector"},Ei={class:"filter-section"},zi={class:"items-list-section"},Ti={key:1,class:"text-gray-400"},Ri={key:0,class:"selected-item-section"},ji={class:"selected-item-card"},Mi={class:"item-header"},Ni={class:"item-tags"},Pi={class:"item-content"},Bi={key:0},Ai={key:0,class:"config-item-dialog"},Fi={class:"item-info-section"},Oi={class:"item-description"},Hi={class:"item-meta"},Yi={key:0,class:"parameter-number-input"},Xi={class:"parameter-unit"},Qi={key:1,class:"parameter-range-input"},Wi={class:"parameter-unit"},Ki={key:5,class:"dual-layer-param-config"},Zi={class:"dual-layer-item"},Ji={class:"dual-layer-label"},er={class:"dual-layer-item"},lr={class:"dual-layer-label"},ar={class:"quality-check-description",style:{"margin-top":"8px"}},tr={style:{color:"#666","font-style":"italic"}},sr={class:"form-tip"},nr={key:0},or={key:1},ur={key:0,class:"layer-selection-toolbar"},ir={class:"layer-option summary-option"},rr={class:"layer-name"},dr={class:"layer-option"},pr={class:"layer-name"},cr={class:"form-tip"},mr={key:0,class:"edit-config-item-dialog"},vr={class:"item-info-section"},fr={class:"item-description"},_r={class:"item-meta"},yr={class:"layer-selection-controls"},gr={class:"form-tip"},br={key:0,class:"parameter-number-input"},hr={class:"parameter-unit"},wr={key:1,class:"parameter-range-input"},kr={class:"parameter-unit"},Vr={key:5,class:"dual-layer-param-config"},Cr={class:"dual-layer-item"},Lr={class:"dual-layer-label"},xr={class:"dual-layer-item"},$r={class:"dual-layer-label"},Ur={class:"quality-check-description",style:{"margin-top":"8px"}},Sr={style:{color:"#666","font-style":"italic"}},Dr={class:"form-tip"},Ir={key:0},qr={key:1},Gr=4,Er=Ms({__name:"QualityView",setup(zr){const R=Ns(),ll=l=>window.location.pathname.startsWith("/gsi/")?`/gsi${l}`:`${T.defaults.baseURL}${l}`,Y=_(0),al=_([]),Vl=_(!1),te=_(null),we=_(!1),tl=_(!1),Ge=_(),Cl=_(!1),Jl=_([]),A=_({tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""}),ja={user_project:[{required:!0,message:"请输入使用项目",trigger:"blur"}],reason:[{required:!0,message:"请输入申请原因",trigger:"blur"},{min:10,message:"申请原因不能少于10个字符",trigger:"blur"}],end_date:[{required:!0,message:"请选择有效时间",trigger:"change"}],usage_count:[{required:!0,message:"请输入申请次数",trigger:"change"}],approver:[{required:!0,message:"请选择审批人",trigger:"change"}]},Ma=l=>l.getTime()<Date.now()-864e5;function Na(){we.value=!0,Ba()}function Pa(){tl.value=!0,je().finally(()=>{setTimeout(()=>{tl.value=!1},600)})}async function Ba(){var e;const l=(e=R.user)==null?void 0:e.username;if(!l){g.error("未登录，无法获取审批人");return}try{const o=await T.get("/api/admin-users",{headers:{"X-Username":l}});o.data.success?Jl.value=o.data.data:g.error("获取审批人失败")}catch{g.error("获取审批人失败")}}async function Aa(){Ge.value&&await Ge.value.validate(async l=>{var e;if(l){Cl.value=!0;try{const o=(e=R.user)==null?void 0:e.username,u=await T.post("/api/quality/tools/apply",{fmw_id:"quality",fmw_name:"数据质检",tool_name:"数据质检",applicant:o,reason:A.value.reason,end_date:A.value.end_date,usage_count:A.value.usage_count,user_project:A.value.user_project,reviewer:A.value.approver},{headers:{"Content-Type":"application/json","X-Username":o}});u.data.success?(g.success("申请提交成功"),we.value=!1,ea(),await je()):g.error(u.data.message||"申请提交失败")}catch{g.error("申请提交失败")}finally{Cl.value=!1}}})}function ea(){Ge.value&&Ge.value.resetFields(),Object.assign(A.value,{tool_name:"数据质检",user_project:"",reason:"",end_date:"",usage_count:1,approver:""})}const Ll=H(()=>{var l;return{"X-Username":((l=R.user)==null?void 0:l.username)||""}}),j=_([]),D=_(""),ke=_(!1),ue=_(0),de=_(""),ie=_(!1),se=_(null),Ee=_("upload"),xl=_([]),ze=_(!1),sl=_(""),ee=_(""),nl=_([]),$l=_(""),Te=_(!1),Ve=_(1),Re=_(20),Ul=_(!1),N=H(()=>{if(!D.value)return[];const l=j.value.find(e=>e.path===D.value);return l?l.layers:[]}),Fa=H(()=>{const l=(Ve.value-1)*Re.value,e=l+Re.value;return N.value.slice(l,e)});function Sl(){if(Y.value===1&&!D.value){g.warning("请先选择一个GDB数据库");return}if(Y.value===2&&!_l.value){g.warning("请先配置质检项");return}if(Y.value===3&&!te.value){g.warning("请先选择一个质检许可");return}Y.value<3&&Y.value++}function Dl(){Y.value>0&&Y.value--}async function je(){var l;Vl.value=!0;try{const e=(l=R.user)==null?void 0:l.username;if(!e){g.error("未登录，无法获取许可列表"),al.value=[];return}const o=await T.post("/api/quality/tools/my-approvals",{},{headers:{"X-Username":e}});if(o.data.success){al.value=o.data.data;const u=al.value.find(c=>{if(Me(c.status).text!=="已通过")return!1;const V=new Date(c.end_date),y=new Date;return y.setHours(0,0,0,0),!(V<y||c.count>=c.usage_count)});te.value=u?u.id:null}else g.error(o.data.message||"获取许可列表失败")}catch{g.error("获取许可列表失败")}finally{Vl.value=!1}}Ol(Y,l=>{l===1&&je()});function Me(l){return{pending:{type:"info",text:"审批中"},approved:{type:"success",text:"已通过"},rejected:{type:"danger",text:"已驳回"},expired:{type:"warning",text:"已过期"},exhausted:{type:"warning",text:"已耗尽"},available:{type:"success",text:"可用"}}[l]||{type:"default",text:l}}function la({row:l}){if(Me(l.status).text!=="已通过")return"disabled-row";const e=new Date(l.end_date),o=new Date;return o.setHours(0,0,0,0),e<o||l.count>=l.usage_count?"disabled-row":l.id===te.value?"clickable-row selected-row":"clickable-row"}function Oa(l){if(Me(l.status).text!=="已通过")return;const e=new Date(l.end_date),o=new Date;o.setHours(0,0,0,0),!(e<o)&&(l.count>=l.usage_count||(te.value=l.id))}function Ha(l){var u;const e=(u=l.name.split(".").pop())==null?void 0:u.toLowerCase();if(!["zip","rar","7z"].includes(e))return g.error("只支持zip、rar、7z格式的压缩包"),!1;const o=10*1024*1024*1024;return l.size>o?(g.error("文件大小不能超过10GB"),!1):(ke.value=!0,ue.value=0,de.value="开始上传...",j.value=[],D.value="",!0)}function Ya(l){ue.value=Math.round(l.loaded/l.total*100),ue.value<100?de.value=`上传中... ${ue.value}%`:de.value="解析GDB文件中..."}function Xa(){if(ue.value===100&&!ie.value)return"success";ie.value}function Qa(l){if(ke.value=!1,ue.value=100,de.value="上传完成",l.success&&l.data&&l.data.task_id){const e=l.data.task_id;ie.value=!0,de.value="文件上传成功，开始解析GDB文件...",Ka(e)}else l.success&&l.data&&l.data.gdb_databases?Wa(l):g.error(l.message||"GDB上传失败")}function Wa(l){j.value=l.data.gdb_databases.map(o=>({...o,name:o.path.split(/[/\\]/).pop()||"",relativePath:o.path.replace(l.data.base_path||"","").replace(/^[/\\]/,""),totalFeatures:o.layers.reduce((u,c)=>u+(c.feature_count||0),0)})),j.value.length>0&&(D.value=j.value[0].path);const e=j.value.reduce((o,u)=>o+u.layers.length,0);g.success(`GDB解析成功，发现 ${j.value.length} 个数据库，共 ${e} 个图层`)}function Ka(l){const e=new EventSource(`${ll("/api/quality/gdb-progress")}/${l}`);e.onmessage=function(o){try{const u=JSON.parse(o.data);if(u.error){g.error(u.error),e.close(),ie.value=!1,se.value=null;return}if(ue.value=u.progress||0,de.value=u.message||"解析中...",se.value=u,u.gdb_databases&&u.gdb_databases.length>0&&(j.value=u.gdb_databases.map(c=>{var V;return{...c,name:c.name||((V=c.path.split(/[/\\]/).pop())==null?void 0:V.replace(".gdb",""))||"Unknown",totalFeatures:c.totalFeatures||c.layers.reduce((y,S)=>y+(S.feature_count||0),0)}}),!D.value&&j.value.length>0&&(D.value=j.value[0].path)),u.status==="completed"){e.close(),ie.value=!1,ue.value=100,de.value="解析完成！";const c=j.value.reduce((V,y)=>V+y.layers.length,0);g.success(`GDB解析完成！发现 ${j.value.length} 个数据库，共 ${c} 个图层`),setTimeout(()=>{se.value=null},3e3)}u.status==="error"&&(e.close(),ie.value=!1,se.value=null,g.error(u.error||"解析失败"))}catch(u){console.error("解析SSE数据失败:",u)}},e.onerror=function(o){console.error("SSE连接错误:",o),e.close(),ie.value=!1,se.value=null,g.error("实时解析连接中断")}}function Za(l){ke.value=!1,ue.value=0,de.value="",ie.value=!1,se.value=null,console.error("GDB上传错误详情:",l),g.error("GDB上传失败："+(l.message||"未知错误"))}function Ja(l){return l.feature_set&&l.feature_set!==l.name?`${l.feature_set}/${l.name}`:l.name}function et(l){return typeof l=="object"&&l.name?l.name:l}function lt(l){if(typeof l=="object"){const e=[];return l.name&&e.push(`字段名: ${l.name}`),l.type&&e.push(`类型: ${l.type}`),l.length&&e.push(`长度: ${l.length}`),e.join(`
`)}return l}function at(l){D.value=l.path,Ve.value=1,pe.value={}}function tt(l){Re.value=l,Ve.value=1,pe.value={}}function st(l){Ve.value=l,pe.value={}}const pe=_({});function nt(l,e,o){const u=ta(o);return l>=u}function aa(l,e){const o=ta(e);return l>o?l-o:0}function ta(l){const e=`row-${l}`;if(pe.value[e])return pe.value[e];const o=window.innerWidth;let u=3;return o>=2560?u=12:o>=1920?u=10:o>=1680?u=8:o>=1440?u=6:o>=1200?u=5:o>=1024&&(u=4),pe.value[e]=u,Ie(()=>{sa(l)}),u}function sa(l){try{const e=`fieldsContainer-${l}`,o=document.querySelector(`[ref="${e}"]`);if(!o)return;const u=o.clientWidth-120;let c=0,V=0;const y=o.querySelectorAll(".field-tag:not(.field-hidden)");if(y.length<=3){const K=`row-${l}`;pe.value[K]=y.length;return}for(let K=0;K<y.length;K++){const B=y[K].offsetWidth+6;if(K<2||c+B<=u)c+=B,V++;else break}const S=`row-${l}`;pe.value[S]=Math.max(2,V)}catch(e){console.warn("计算字段可见性失败:",e)}}function ot(l){return l?{CGCS2000_3_Degree_GK_CM_114E:"CGCS2000 114°E",CGCS2000_3_Degree_GK_CM_117E:"CGCS2000 117°E",CGCS2000_3_Degree_GK_CM_120E:"CGCS2000 120°E",WGS_1984_UTM_Zone_49N:"WGS84 UTM 49N",WGS_1984_UTM_Zone_50N:"WGS84 UTM 50N",Beijing_1954_3_Degree_GK_CM_114E:"Beijing54 114°E",Beijing_1954_3_Degree_GK_CM_117E:"Beijing54 117°E",Xian_1980_3_Degree_GK_CM_114E:"Xian80 114°E",Xian_1980_3_Degree_GK_CM_117E:"Xian80 117°E"}[l]||l:"未知"}function ut(l){switch(l){case"point":return"Location";case"line":return"Connection";case"polygon":return"Grid";default:return"Document"}}function it(l){return!l.geometry_type||l.geometry_type==="None"||l.geometry_type==="Unknown"?"数据表":l.geometry_type}function rt(l){const e=l.geometry_type,o=l.type;if(!e||e==="None"||e==="Unknown")return"info";switch(o){case"point":return"success";case"line":return"warning";case"polygon":return"danger";default:return"info"}}const dt=H(()=>N.value.reduce((l,e)=>l+(e.feature_count||0),0)),Ne=_("quality");function pt(l){l.name==="history"&&oa()}const ol=_([]),Il=_(!1),ul=_(!1),na=_("");function il(l,e="yyyy-MM-dd HH:mm"){if(!l)return"--";try{const o=new Date(l),u=c=>c<10?"0"+c:c;return e==="yyyy-MM-dd HH:mm:ss"?`${o.getFullYear()}-${u(o.getMonth()+1)}-${u(o.getDate())} ${u(o.getHours())}:${u(o.getMinutes())}:${u(o.getSeconds())}`:`${o.getFullYear()}-${u(o.getMonth()+1)}-${u(o.getDate())} ${u(o.getHours())}:${u(o.getMinutes())}`}catch{return"--"}}function ct(l){if(!l)return"--";const e=Math.floor(Number(l)/1e3);if(e<60)return`${e}s`;const o=Math.floor(e/60),u=e%60;return`${o}m${u}s`}function mt(l){return{running:"warning",success:"success",completed:"success",pending:"info",failed:"danger"}[l]||"info"}function vt(l){return{running:"运行中",success:"完成",completed:"完成",pending:"队列中",failed:"失败"}[l]||l}async function oa(){var l;Il.value=!0;try{const e=(l=R.user)==null?void 0:l.username,o=await T.post("/api/quality/tools/run_records",{fmw_id:"quality",username:e},{headers:{"X-Username":e}});o.data.success?ol.value=o.data.data.records:g.error(o.data.message||"获取历史记录失败")}catch{g.error("获取历史记录失败")}finally{Il.value=!1}}function ft(l){try{const e=`tools/quality/output/${l.task_id}/${l.file_name}`,o=`/api/tools/download_result?file_path=${encodeURIComponent(e)}`,u=document.createElement("a");u.style.display="none",document.body.appendChild(u),u.href=o,u.download=l.file_name,u.click(),setTimeout(()=>{document.body.removeChild(u)},100),g.success("开始下载")}catch{g.error("下载失败，请稍后重试")}}function _t(l){l.error_message?(na.value=l.error_message,ul.value=!0):g.warning("暂无错误信息")}async function yt(l){var e;try{await el.confirm("确定要删除这条历史记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=ol.value.findIndex(u=>u.task_id===l.task_id);o!==-1&&ol.value.splice(o,1),await T.post("/api/quality/tools/delete_result",{task_id:l.task_id},{headers:{"X-Username":(e=R.user)==null?void 0:e.username}}),g.success("删除成功")}catch(o){o!=="cancel"&&g.error("删除失败")}}const ge=_([]),U=_(null),ql=_(!1),Pe=_(!1),le=_({name:"",description:"",globalTolerance:.001}),Be=_(!1),ne=_({name:"",description:"",globalTolerance:.001}),Ce=_(null),Ae=_(!1),f=_(null),Fe=_(!1),rl=_(""),dl=_(""),L=_({selectedLayers:[],paramValue:null,paramRange:{min:null,max:null},sourceLayers:[],targetLayers:[],sourceLayer:"",targetLayer:""}),Oe=_(!1),h=_(null),ua=_(-1),x=_({selectedLayers:[],paramValue:null,paramRange:{min:null,max:null},sourceLayer:null,targetLayer:null,sourceLayers:[],targetLayers:[]});_(),_(!1),_(0),_(""),_(),_(!1),_(0),_("");const pl=_(!1),F=_([]),Gl=_([]);_();const gt=_([]),bt=_([]),ve=_([]);_({});const Q=_(""),W=_(""),cl=_(!1),O=_(null),fe=_({level1_name:"",level2_name:"",selectedItem:null,selectedLayers:[],paramValue:""}),ia=_(!1),El=_(!1),Le=_(!1),He=_(),be=_(!1),zl=_(!1),M=_({id:null,level1_name:"",level2_name:"",item_name:"",object_type:"通用",item_code:"",rule_content:"",param_type:"无",param_name:""}),ht=H(()=>{const l=new Set;return F.value.forEach(e=>{e.level1_name&&l.add(e.level1_name)}),Array.from(l).sort()}),wt=H(()=>{if(!M.value.level1_name)return[];const l=new Set;return F.value.forEach(e=>{e.level1_name===M.value.level1_name&&e.level2_name&&l.add(e.level2_name)}),Array.from(l).sort()});H(()=>{if(!fe.value.level1_name)return[];const l=new Set;return F.value.forEach(e=>{e.level1_name===fe.value.level1_name&&e.level2_name&&l.add(e.level2_name)}),Array.from(l).sort()});const ml=H(()=>{const l=new Set;return F.value.forEach(e=>{e.level1_name&&l.add(e.level1_name)}),Array.from(l).sort()}),xe=H(()=>{if(!Q.value)return[];const l=new Set;return F.value.forEach(e=>{e.level1_name===Q.value&&e.level2_name&&l.add(e.level2_name)}),Array.from(l).sort()}),kt=H(()=>!Q.value||!W.value?[]:F.value.filter(l=>l.level1_name===Q.value&&l.level2_name===W.value));H(()=>!fe.value.level1_name||!fe.value.level2_name?[]:F.value.filter(l=>l.level1_name===fe.value.level1_name&&l.level2_name===fe.value.level2_name)),H(()=>fe.value.selectedItem&&fe.value.selectedLayers.length>0);const Vt={level1_name:[{required:!0,message:"请选择一级分类",trigger:"change"}],level2_name:[{required:!0,message:"请输入二级分类名称",trigger:"blur"}],item_name:[{required:!0,message:"请输入质检项名称",trigger:"blur"}],object_type:[{required:!0,message:"请选择对象类型",trigger:"change"}],item_code:[{required:!0,message:"请输入质检项代码",trigger:"blur"},{pattern:/^[A-Z]\d{3}$/,message:"质检项代码格式应为：字母+3位数字，如A001",trigger:"blur"}],rule_content:[{required:!0,message:"请输入规则内容",trigger:"blur"}],param_type:[{required:!0,message:"请选择参数类型",trigger:"change"}]};_(!1),_([]);const Ct=_(null),Tl=_(!1),Rl=_(!1);async function $e(){var l;ql.value=!0;try{let e="";if(D.value){const c=j.value.find(V=>V.path===D.value);c?e=c.name||Se(D.value):e=Se(D.value)}let o="/api/quality/configs";e&&(o=`/api/quality/configs/by-gdb/${encodeURIComponent(e)}`);const u=await T.get(o,{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});u.data.success?ge.value=u.data.data:g.error(u.data.message||"加载配置失败")}catch(e){console.error("加载质检配置失败:",e),g.error("加载质检配置失败")}finally{ql.value=!1}}function vl(){if(D.value){const l=j.value.find(e=>e.path===D.value);return l&&l.name||Se(D.value)}return"未选择GDB文件"}function ra(){le.value={name:"",description:"",globalTolerance:.001},Pe.value=!0}function Lt(l){Ce.value=l,ne.value={name:`${l.name}_副本`,description:l.description||"",globalTolerance:l.global_tolerance||.001},Be.value=!0}async function xt(){var l;if(!le.value.name.trim()){g.warning("请输入配置名称");return}try{let e="";if(D.value){const u=j.value.find(c=>c.path===D.value);u?e=u.name||Se(D.value):e=Se(D.value)}const o=await T.post("/api/quality/configs",{name:le.value.name,description:le.value.description,gdb_path:D.value,gdb_filename:e,global_tolerance:le.value.globalTolerance},{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});if(o.data.success){g.success("配置创建成功"),Pe.value=!1,await $e();const u=ge.value.find(c=>c.id===o.data.data.id);u&&fl(u)}else g.error(o.data.message||"创建配置失败")}catch(e){console.error("创建配置失败:",e),g.error("创建配置失败")}}async function $t(){var l;if(!ne.value.name.trim()){g.warning("请输入配置名称");return}if(!Ce.value){g.error("未找到要复制的配置");return}try{const e=vl(),o=await T.post("/api/quality/configs/copy",{source_config_id:Ce.value.id,name:ne.value.name,description:ne.value.description,gdb_path:D.value,gdb_filename:e},{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});if(o.data.success){g.success("配置副本创建成功"),Be.value=!1,Ce.value=null,await $e();const u=ge.value.find(c=>c.id===o.data.data.id);u&&await Ut(u.id)}else g.error(o.data.message||"创建配置副本失败")}catch(e){console.error("创建配置副本失败:",e),g.error("创建配置副本失败")}}async function fl(l){var u;const e=vl();if(!(l.gdb_filename===e||!l.gdb_filename)&&e!=="未选择GDB文件"){Lt(l);return}try{const c=await T.get(`/api/quality/configs/${l.id}`,{headers:{"X-Username":(u=R.user)==null?void 0:u.username}});c.data.success?U.value=c.data.data:g.error(c.data.message||"加载配置详情失败")}catch(c){console.error("加载配置详情失败:",c),g.error("加载配置详情失败")}}async function Ut(l){var e;try{const o=await T.get(`/api/quality/configs/${l}`,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}});o.data.success?U.value=o.data.data:g.error(o.data.message||"加载配置详情失败")}catch(o){console.error("加载配置详情失败:",o),g.error("加载配置详情失败")}}async function St(l){var e,o;try{await el.confirm(`确定要删除配置"${l.name}"吗？`,"确认删除",{type:"warning"});const u=await T.delete(`/api/quality/configs/${l.id}`,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}});u.data.success?(g.success("配置删除成功"),await $e(),((o=U.value)==null?void 0:o.id)===l.id&&(U.value=null)):g.error(u.data.message||"删除配置失败")}catch(u){u!=="cancel"&&(console.error("删除配置失败:",u),g.error("删除配置失败"))}}function da(){Ae.value=!0}async function Dt(l){var o;if(!U.value||!U.value.items)return;const e=U.value.items[l];try{await el.confirm(`确定要删除质检项"${e.item_name}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1});const u=await T.delete(`/api/quality/configs/${U.value.id}/items/${e.id}`,{headers:{"X-Username":(o=R.user)==null?void 0:o.username}});u.data.success?(U.value.items.splice(l,1),g.success("质检项移除成功")):g.error(u.data.message||"移除质检项失败")}catch(u){u!=="cancel"&&(console.error("移除质检项失败:",u),g.error("移除质检项失败"))}}function It(l){return!l||typeof l!="object"?"":Object.entries(l).map(([o,u])=>`${o}: ${u}`).join(", ")}Ol(Y,l=>{l===2&&($e(),F.value.length===0&&_e()),l===3&&je()});async function _e(){var l;pl.value=!0;try{const e=await T.get("/api/quality/items",{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});e.data.success&&(F.value=e.data.data,qt(),Ie(()=>{xa()}))}catch(e){console.error("加载质检项失败:",e),g.error("加载质检项失败")}finally{pl.value=!1}}function qt(){const l=[],e=new Map,o=new Map;F.value.forEach(u=>{if(!e.has(u.level1_name)){const y={id:`level1_${u.level1_name}`,label:u.level1_name,type:"level1",children:[]};e.set(u.level1_name,y),l.push(y)}const c=`${u.level1_name}_${u.level2_name}`;if(!o.has(c)){const y={id:`level2_${c}`,label:u.level2_name,type:"level2",children:[]};o.set(c,y),e.get(u.level1_name).children.push(y)}const V={id:`item_${u.id}`,label:u.item_name,type:"item",data:u,object_type:u.object_type,rule_content:u.rule_content,param_type:u.param_type,param_name:u.param_name};o.get(c).children.push(V)}),Gl.value=l,gt.value=l.map(u=>u.id),ve.value=l.map(u=>u.id)}function Gt(l){const e=ve.value.indexOf(l);e>-1?ve.value.splice(e,1):ve.value.push(l)}function Et(l){let e=0;return l.children&&l.children.forEach(o=>{o.children&&(e+=o.children.length)}),e}function zt(){ve.value=Gl.value.map(l=>l.id)}function Tt(){ve.value=[]}function ye(l){return{数据集:"primary",图层:"info",字段:"warning",点要素:"success",线要素:"warning",面要素:"danger",通用:"default",点:"success",点图层:"success",线:"warning",线图层:"warning",面:"danger",面图层:"danger",图层字段:"warning",所有点线面图层:"default","线、面图层":"warning","点、线、面图层":"default"}[l]||"default"}function Rt(l,e){l.success?(L.value.paramValue=l.data.file_path,g.success("模板文件上传成功")):g.error(l.message||"模板文件上传失败")}function pa(l){console.error("模板文件上传失败:",l),g.error("模板文件上传失败")}function jt(l,e){l.success?(x.value.paramValue=l.data.file_path,g.success("模板文件上传成功")):g.error(l.message||"模板文件上传失败")}function he(){var l;return N.value?((l=f.value)==null?void 0:l.item_name)==="工作范围检查"&&L.value.paramValue?N.value.filter(e=>e.name!==L.value.paramValue):N.value:[]}function ca(){var y;if(!N.value)return[];const l=he(),e=(y=f.value)==null?void 0:y.object_type,o=l.filter(S=>S.type==="Point"),u=l.filter(S=>S.type==="Polyline"),c=l.filter(S=>S.type==="Polygon"),V=[];return(e==="通用"||e==="交互检查")&&V.push({label:"所有图层",value:"__ALL_LAYERS__",count:l.length,disabled:l.length===0}),(e==="点要素"||e==="通用")&&o.length>0&&V.push({label:"所有点图层",value:"__ALL_POINT_LAYERS__",count:o.length,disabled:!1}),(e==="线要素"||e==="通用")&&u.length>0&&V.push({label:"所有线图层",value:"__ALL_LINE_LAYERS__",count:u.length,disabled:!1}),(e==="面要素"||e==="通用")&&c.length>0&&V.push({label:"所有面图层",value:"__ALL_POLYGON_LAYERS__",count:c.length,disabled:!1}),e==="通用"&&(o.length>0&&u.length>0&&V.push({label:"所有点、线图层",value:"__ALL_POINT_LINE_LAYERS__",count:o.length+u.length,disabled:!1}),u.length>0&&c.length>0&&V.push({label:"所有线、面图层",value:"__ALL_LINE_POLYGON_LAYERS__",count:u.length+c.length,disabled:!1}),o.length>0&&u.length>0&&c.length>0&&V.push({label:"所有点、线、面图层",value:"__ALL_POINT_LINE_POLYGON_LAYERS__",count:o.length+u.length+c.length,disabled:!1})),V}function Ye(){var l;return N.value?((l=h.value)==null?void 0:l.item_name)==="工作范围检查"&&x.value.paramValue?N.value.filter(e=>e.name!==x.value.paramValue):N.value:[]}function Mt(){var l,e;return((l=f.value)==null?void 0:l.item_name)==="工作范围检查"?L.value.paramValue?`待检图层：选择要检查是否在范围内的图层（已排除范围图层：${L.value.paramValue}）`:"待检图层：请先选择范围图层参数，然后选择要检查的图层":((e=f.value)==null?void 0:e.param_type)==="图层选择"?"交互检查：先选择参考图层作为参数，再选择需要检查的图层（会自动排除参考图层）":"根据质检项的对象类型，只显示兼容的图层"}function Nt(){var l,e;return((l=h.value)==null?void 0:l.item_name)==="工作范围检查"?"工作范围检查：先选择范围图层作为参数，再选择需要检查的图层（会自动排除范围图层）":((e=h.value)==null?void 0:e.param_type)==="图层选择"?"交互检查：先选择参考图层作为参数，再选择需要检查的图层（会自动排除参考图层）":"根据质检项的对象类型，只显示兼容的图层"}function Pt(){var l;return((l=f.value)==null?void 0:l.object_type)==="交互检查"?!1:he().length>1}function jl(l){var u,c;return he().some(V=>yl(V,l))&&(((u=f.value)==null?void 0:u.object_type)==="通用"||((c=f.value)==null?void 0:c.object_type)===l)}function Ue(){var l;return((l=f.value)==null?void 0:l.item_name)==="工作范围检查"?!L.value.paramValue:!1}function Bt(){const e=he().filter(o=>{var u;return yl(o,(u=f.value)==null?void 0:u.object_type)});L.value.selectedLayers=e.map(o=>o.name)}function Ml(l){const o=he().filter(u=>yl(u,l));L.value.selectedLayers=o.map(u=>u.name)}function At(){L.value.selectedLayers=[]}function Ft(l){const e=he(),o=[];for(const u of l)if(u.startsWith("__")&&u.endsWith("__")){const c=Ot(u,e);o.push(...c)}else o.push(u);L.value.selectedLayers=[...new Set(o)]}function Ot(l,e){const o=e.filter(V=>V.type==="Point").map(V=>V.name),u=e.filter(V=>V.type==="Polyline").map(V=>V.name),c=e.filter(V=>V.type==="Polygon").map(V=>V.name);switch(l){case"__ALL_LAYERS__":return e.map(V=>V.name);case"__ALL_POINT_LAYERS__":return o;case"__ALL_LINE_LAYERS__":return u;case"__ALL_POLYGON_LAYERS__":return c;case"__ALL_POINT_LINE_LAYERS__":return[...o,...u];case"__ALL_LINE_POLYGON_LAYERS__":return[...u,...c];case"__ALL_POINT_LINE_POLYGON_LAYERS__":return[...o,...u,...c];default:return[]}}function Ht(){L.value.selectedLayers=[]}function ae(l){const e=(l==null?void 0:l.param_name)||"";return e.includes("线长")||e.includes("边长")?{min:.001,max:100,step:.001,precision:3,unit:"m",placeholder:"请输入最小允许线长"}:e.includes("夹角")||e.includes("角度")?{min:.1,max:180,step:.1,precision:1,unit:"°",placeholder:"请输入最小允许夹角"}:e.includes("面积")?{min:.001,max:1e4,step:.001,precision:3,unit:"m²",placeholder:"请输入最小允许面积"}:{min:0,max:999999,step:.001,precision:3,unit:"",placeholder:"请输入数值"}}function re(l){const e=(l==null?void 0:l.param_name)||"";return e.includes("节点")||e.includes("数量")?{min:3,max:1e4,unit:"个"}:{min:0,max:999999,unit:""}}function ma(){return N.value?N.value.filter(l=>{const e=l.type||l.geometry_type||l.geometryType||"";return e==="Polygon"||e==="POLYGON"||e.toLowerCase().includes("polygon")}):[]}function va(l){const e=(l==null?void 0:l.param_name)||"";return e.includes("-")?`源图层（${e.split("-")[0]}）`:"源图层"}function fa(l){const e=(l==null?void 0:l.param_name)||"";return e.includes("-")?`目标图层（${e.split("-")[1]}）`:"目标图层"}function Nl(l){return((l==null?void 0:l.param_name)||"").split("-")[0]||"源"}function Pl(l){return((l==null?void 0:l.param_name)||"").split("-")[1]||"目标"}function _a(l){if(!N.value)return[];const e=Nl(l);return N.value.filter(o=>{const u=o.type||o.geometry_type||o.geometryType||"";return e==="点"?u==="Point"||u==="POINT"||u.toLowerCase().includes("point"):e==="线"?u==="Polyline"||u==="POLYLINE"||u==="LineString"||u==="LINESTRING"||u.toLowerCase().includes("line"):e==="面"?u==="Polygon"||u==="POLYGON"||u.toLowerCase().includes("polygon"):!0})}function ya(l){if(!N.value)return[];const e=Pl(l);return N.value.filter(o=>{const u=o.type||o.geometry_type||o.geometryType||"";return e==="点"?u==="Point"||u==="POINT"||u.toLowerCase().includes("point"):e==="线"?u==="Polyline"||u==="POLYLINE"||u==="LineString"||u==="LINESTRING"||u.toLowerCase().includes("line"):e==="面"?u==="Polygon"||u==="POLYGON"||u.toLowerCase().includes("polygon"):!0})}function ga(l){return l?!["文件上传","双图层选择"].includes(l.param_type):!1}function ba(l,e,o){if(!l||!e||!o)return"请选择源图层和目标图层";const u=l.item_name||"",c=l.param_name||"";return c.includes("点-点")?`质检 ${e} 图层中与 ${o} 图层中点要素重合的部分`:c.includes("点-线")?`质检 ${e} 图层中与 ${o} 图层中线要素重合的部分`:c.includes("点-面")?`质检 ${e} 图层中未被 ${o} 图层中面要素覆盖的部分`:c.includes("线-点")?`质检 ${e} 图层中与 ${o} 图层中点要素重合的部分`:c.includes("线-线")?u.includes("重合")?`质检 ${e} 图层中与 ${o} 图层中线要素重合的部分`:u.includes("相交")?`质检 ${e} 图层中与 ${o} 图层中线要素相交的部分`:u.includes("悬挂")?`质检 ${e} 图层中悬挂在 ${o} 图层中线要素上的部分`:`质检 ${e} 图层与 ${o} 图层中线要素的关系`:c.includes("线-面")?u.includes("边界覆盖")?`质检 ${e} 图层中要素未被 ${o} 图层中要素边界覆盖的部分`:u.includes("内部")?`质检 ${e} 图层中不在 ${o} 图层内部的部分`:`质检 ${e} 图层中未被 ${o} 图层中面要素覆盖的部分`:c.includes("面-点")?`质检 ${e} 图层中包含 ${o} 图层中点要素的部分`:c.includes("面-线")?`质检 ${e} 图层中包含 ${o} 图层中线要素的部分`:c.includes("面-面")?u.includes("重叠")?`质检 ${e} 图层中与 ${o} 图层中面要素重叠的部分`:u.includes("缝隙")?`质检 ${e} 图层与 ${o} 图层之间的缝隙`:u.includes("包含")?`质检 ${e} 图层中包含 ${o} 图层中面要素的部分`:`质检 ${e} 图层与 ${o} 图层中面要素的关系`:`质检 ${e} 图层与 ${o} 图层的空间关系`}function Yt(){M.value.level2_name=""}function Xt(l){return`${l.name} (${l.type})`}function Qt(){El.value=!0,F.value.length===0&&_e()}function Wt(){be.value=!1,Zt(),Le.value=!0}function Kt(l){be.value=!0,M.value={id:l.id,level1_name:l.level1_name,level2_name:l.level2_name,item_name:l.item_name,object_type:l.object_type||"通用",item_code:l.item_code,rule_content:l.rule_content,param_type:l.param_type||"无",param_name:l.param_name||""},Le.value=!0}function Zt(){M.value={id:null,level1_name:"",level2_name:"",item_name:"",object_type:"通用",item_code:"",rule_content:"",param_type:"无",param_name:""},He.value&&He.value.resetFields()}async function Jt(){He.value&&await He.value.validate(async l=>{var e;if(l){zl.value=!0;try{const o=be.value?`/api/quality/items/update/${M.value.id}`:"/api/quality/items/add",u=be.value?"put":"post",c=await T[u](o,M.value,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}});c.data.success?(g.success(be.value?"质检项更新成功":"质检项添加成功"),Le.value=!1,await _e()):g.error(c.data.message||"操作失败")}catch(o){console.error("保存质检项失败:",o),g.error("保存质检项失败")}finally{zl.value=!1}}})}async function es(l){var e;try{await el.confirm(`确定要删除质检项"${l.item_name}"吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=await T.delete(`/api/quality/items/delete/${l.id}`,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}});o.data.success?(g.success("质检项删除成功"),await _e()):g.error(o.data.message||"删除失败")}catch(o){o!=="cancel"&&(console.error("删除质检项失败:",o),g.error("删除质检项失败"))}}async function ha(){var l;if(!U.value){g.warning("没有可保存的配置");return}Tl.value=!0;try{const e={name:U.value.name,description:U.value.description||"",gdb_path:D.value,selected_layers:N.value.map(u=>u.name),quality_items:U.value.items||[],license_id:te.value},o=await T.post("/api/quality/config/save",e,{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});o.data.success?(g.success("配置保存成功"),U.value.id=o.data.data.config_id):g.error(o.data.message||"配置保存失败")}catch(e){console.error("保存配置失败:",e),g.error("保存配置失败")}finally{Tl.value=!1}}const _l=H(()=>{var l;return U.value&&te.value&&D.value&&(((l=U.value.items)==null?void 0:l.length)>0||U.value.source==="history")}),ls=H(()=>{const l=new Set;return F.value.forEach(e=>{e.object_type&&l.add(e.object_type)}),Array.from(l).sort()}),as=H(()=>{let l=F.value;if(Q.value&&(l=l.filter(e=>e.level1_name===Q.value)),W.value&&(l=l.filter(e=>e.level2_name===W.value)),rl.value&&(l=l.filter(e=>e.object_type===rl.value)),dl.value){const e=dl.value.toLowerCase();l=l.filter(o=>o.item_name.toLowerCase().includes(e)||o.rule_content.toLowerCase().includes(e)||o.item_code.toLowerCase().includes(e))}return l}),wa=H(()=>{if(!f.value)return!1;if(f.value.param_type==="双图层选择"){if(!L.value.sourceLayer||!L.value.targetLayer)return!1}else if(f.value.param_type==="图层选择"&&f.value.item_name==="工作范围检查"){if(!L.value.paramValue||!L.value.selectedLayers.length)return!1}else if(f.value.param_type!=="文件上传"&&!L.value.selectedLayers.length)return!1;if(f.value.param_type&&f.value.param_type!=="无"){if(f.value.param_type==="范围")return L.value.paramRange.min!==null&&L.value.paramRange.max!==null;if(f.value.param_type==="双图层选择")return!0;if(f.value.param_type!=="文件上传")return L.value.paramValue!==null&&L.value.paramValue!==""}return!0}),ka=H(()=>{if(!sl.value)return xl.value;const l=sl.value.toLowerCase();return xl.value.filter(e=>{var o;return e.name.toLowerCase().includes(l)||((o=e.original_name)==null?void 0:o.toLowerCase().includes(l))})}),ts=H(()=>{var e;if(!((e=U.value)!=null&&e.items))return{};const l={};return U.value.items.forEach(o=>{const u=o.level1_name||"其他";l[u]||(l[u]=[]),l[u].push(o)}),l}),Va=H(()=>{if(!h.value)return!1;if(h.value.param_type==="双图层选择"){if(!x.value.sourceLayer||!x.value.targetLayer)return!1}else if(h.value.param_type==="图层选择"&&h.value.item_name==="工作范围检查"){if(!x.value.paramValue||!x.value.selectedLayers.length)return!1}else if(h.value.param_type!=="文件上传"&&!x.value.selectedLayers.length)return!1;if(h.value.param_type&&h.value.param_type!=="无"){if(h.value.param_type==="范围")return x.value.paramRange.min!==null&&x.value.paramRange.max!==null;if(h.value.param_type==="双图层选择")return!0;if(h.value.param_type!=="文件上传")return x.value.paramValue!==null&&x.value.paramValue!==""}return!0});async function ss(){try{console.log("开始加载坐标系数据...");const l=await T.get("/api/quality/coordinate-systems");console.log("坐标系API响应:",l.data),l.data.success?(nl.value=l.data.data,console.log("坐标系数据加载成功:",nl.value)):console.error("加载坐标系列表失败:",l.data.message)}catch(l){console.error("加载坐标系列表失败:",l)}}function ns(l){l==="history"&&Bl(),ee.value="",l==="upload"&&!D.value&&(j.value=[])}async function Bl(){var l;ze.value=!0;try{const e=await T.get("/api/quality/history-gdb-files",{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});e.data.success?xl.value=e.data.data:g.error(e.data.message||"加载历史文件失败")}catch(e){console.error("加载历史GDB文件失败:",e),g.error("加载历史文件失败")}finally{ze.value=!1}}async function os(l){var e,o,u;if(l.path&&ee.value!==l.path)try{Te.value=!0,Ul.value=!0,ee.value=l.path,D.value="",j.value=[];let c;if(l.has_cached_info)try{if(c=await T.get(`/api/quality/history-gdb-files/${encodeURIComponent(l.path)}/details`,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}}),c.data.success)console.log("使用缓存的GDB信息");else throw new Error("缓存信息不可用")}catch{console.log("缓存信息获取失败，使用文件系统解析"),c=await T.get(`/api/quality/gdb-info/${encodeURIComponent(l.path)}`,{headers:{"X-Username":(o=R.user)==null?void 0:o.username}})}else console.log("没有缓存信息，使用文件系统解析"),c=await T.get(`/api/quality/gdb-info/${encodeURIComponent(l.path)}`,{headers:{"X-Username":(u=R.user)==null?void 0:u.username}});if(c.data.success){j.value=c.data.data.databases||[],j.value.length>0&&(D.value=j.value[0].path);const V=c.data.data.from_cache?"（使用缓存）":"（重新解析）";g.success(`已选择历史文件: ${l.name} ${V}`)}else g.error(c.data.message||"加载文件信息失败"),ee.value="",D.value=""}catch(c){console.error("使用历史GDB文件失败:",c),g.error("使用历史文件失败"),ee.value="",D.value=""}finally{Te.value=!1,Ul.value=!1}}function us(l){ee.value!==l.path&&(Te.value||os(l))}function is({row:l}){return ee.value===l.path?"selected-row":Te.value?"disabled-row":""}async function rs(l){var e;try{await el.confirm(`确定要删除文件 "${l.name}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),$l.value=l.path;const o=await T.delete(`/api/quality/history-gdb-files/${encodeURIComponent(l.path)}`,{headers:{"X-Username":(e=R.user)==null?void 0:e.username}});o.data.success?(g.success("文件删除成功"),ee.value===l.path&&(ee.value="",D.value="",j.value=[]),await Bl()):g.error(o.data.message||"删除文件失败")}catch(o){o!=="cancel"&&(console.error("删除历史GDB文件失败:",o),g.error("删除文件失败"))}finally{$l.value=""}}function Se(l){if(!l)return"";const e=l.replace(/\\/g,"/").split("/"),o=e[e.length-1];return!o||o===""?e[e.length-2]||"":o}async function ds(){var l;if(!_l.value){g.warning("请完成所有配置步骤");return}Rl.value=!0;try{let e=U.value.id;if(!e&&U.value.source!=="history"&&(await ha(),e=U.value.id),!e){g.error("配置保存失败，无法提交任务");return}const o={config_id:e,license_id:te.value,gdb_path:D.value,layers:N.value.map(c=>c.name)},u=await T.post("/api/quality/task/submit",o,{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});u.data.success?(g.success("质检任务提交成功"),Ne.value="history",oa(),Y.value=0,te.value=null,D.value="",j.value=[],U.value=null,bt.value=[],Ct.value=null):g.error(u.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),g.error("任务提交失败")}finally{Rl.value=!1}}Ol(Y,l=>{l===1&&je(),l===3&&($e(),F.value.length===0?_e():Ie(()=>{xa()}))});function ps(){W.value=""}function cs(){}function ms(){}function vs(){}function fs(l){f.value=l}function _s(l){f.value=l,Ca(l)}function Ca(l){O.value=l,cl.value=!0}function ys(){f.value&&(L.value={selectedLayers:[],paramValue:null,paramRange:{min:null,max:null},sourceLayers:[],targetLayers:[],sourceLayer:"",targetLayer:""},Ae.value=!1,Fe.value=!0)}function yl(l,e){var c;const o=((c=l.type)==null?void 0:c.toLowerCase())||"",u=(e==null?void 0:e.toLowerCase())||"";return u.includes("点")?o.includes("point")||o.includes("点"):u.includes("线")?o.includes("line")||o.includes("polyline")||o.includes("线"):u.includes("面")?o.includes("polygon")||o.includes("面"):(u.includes("数据集"),!0)}function gs(l){return l!=null&&l.toLowerCase().includes("point")?"success":l!=null&&l.toLowerCase().includes("line")||l!=null&&l.toLowerCase().includes("polyline")?"warning":l!=null&&l.toLowerCase().includes("polygon")?"danger":"info"}function La(l){var e;return(e=U.value)!=null&&e.items?U.value.items.findIndex(o=>o.item_id===l.item_id&&o.item_name===l.item_name):-1}function bs(l,e){var y,S,K;console.log("编辑配置项 - 原始数据:",l),h.value=l,ua.value=e;let o=null,u=null,c=l.selected_layers?[...l.selected_layers]:[];if(l.param_type==="双图层选择"&&l.selected_layers&&l.selected_layers.length>0){console.log("双图层选择 - selected_layers:",l.selected_layers);const Z=l.selected_layers[0];if(Z.includes(" → ")){const[B,z]=Z.split(" → ");o=B.trim(),u=z.trim(),c=[],console.log("解析结果 - 源图层:",o,"目标图层:",u)}}l.param_type==="图层选择"&&l.item_name==="工作范围检查"&&console.log("工作范围检查 - parameters:",l.parameters);const V={selectedLayers:c,paramValue:((y=l.parameters)==null?void 0:y.value)||null,paramRange:{min:((S=l.parameters)==null?void 0:S.min)||null,max:((K=l.parameters)==null?void 0:K.max)||null},sourceLayer:o,targetLayer:u,sourceLayers:l.source_layers?[...l.source_layers]:[],targetLayers:l.target_layers?[...l.target_layers]:[]};console.log("编辑表单数据:",V),x.value=V,Oe.value=!0}function hs(){const l=Ye();x.value.selectedLayers=l.map(e=>e.name)}function ws(){const e=Ye().filter(o=>o.type==="Point");x.value.selectedLayers=e.map(o=>o.name)}function ks(){const e=Ye().filter(o=>o.type==="Polyline");x.value.selectedLayers=e.map(o=>o.name)}function Vs(){const e=Ye().filter(o=>o.type==="Polygon");x.value.selectedLayers=e.map(o=>o.name)}function Cs(){x.value.selectedLayers=[]}function Ls(){var l;((l=h.value)==null?void 0:l.item_name)==="工作范围检查"&&(x.value.selectedLayers=[])}async function xs(){var l;if(!(!h.value||!U.value||!Va.value))try{let e={},o=[],u=[],c=[];h.value.param_type&&h.value.param_type!=="无"&&(h.value.param_type==="范围"?e={min:x.value.paramRange.min,max:x.value.paramRange.max}:h.value.param_type==="双图层选择"?e={interaction_type:h.value.param_name||"图层关系检查"}:e={value:x.value.paramValue}),h.value.param_type==="双图层选择"?o=[`${x.value.sourceLayer} → ${x.value.targetLayer}`]:o=x.value.selectedLayers||[];const V=await T.put(`/api/quality/configs/${U.value.id}/items/${h.value.id}`,{selected_layers:o,parameters:e,source_layers:u,target_layers:c},{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});V.data.success?(g.success("配置项更新成功"),await fl(U.value),Oe.value=!1,h.value=null,ua.value=-1):g.error(V.data.message||"更新配置项失败")}catch(e){console.error("更新配置项失败:",e),g.error("更新配置项失败")}}async function $s(){var l;if(!(!f.value||!U.value||!wa.value))try{let e={},o=[],u=[],c=[];f.value.param_type&&f.value.param_type!=="无"&&(f.value.param_type==="范围"?e={min:L.value.paramRange.min,max:L.value.paramRange.max}:f.value.param_type==="双图层选择"?e={interaction_type:f.value.param_name||"图层关系检查"}:e={value:L.value.paramValue}),f.value.param_type==="双图层选择"?o=[`${L.value.sourceLayer} → ${L.value.targetLayer}`]:o=L.value.selectedLayers||[];const V=await T.post(`/api/quality/configs/${U.value.id}/items`,{item_id:f.value.id,selected_layers:o,parameters:e,source_layers:u,target_layers:c},{headers:{"X-Username":(l=R.user)==null?void 0:l.username}});V.data.success?(g.success("质检项添加成功"),await fl(U.value),Fe.value=!1,f.value=null):g.error(V.data.message||"添加质检项失败")}catch(e){console.error("添加质检项失败:",e),g.error("添加质检项失败")}}function Us(l){Q.value=l,f.value=null,Ie(()=>{xe.value.length>0?W.value=xe.value[0]:W.value=""})}function Ss(l){W.value=l,f.value=null}function xa(){ml.value.length>0&&!Q.value&&(Q.value=ml.value[0],Ie(()=>{xe.value.length>0&&(W.value=xe.value[0])}))}function $a(){cl.value=!1,O.value=null}function Ua(){pe.value={},Ie(()=>{for(let l=0;l<N.value.length;l++)sa(l)})}return Ps(()=>{F.value.length===0&&_e(),ss(),window.addEventListener("resize",Ua)}),Bs(()=>{window.removeEventListener("resize",Ua)}),(l,e)=>{var Ta;const o=q("el-step"),u=q("el-steps"),c=q("el-skeleton-item"),V=q("el-skeleton"),y=q("el-table-column"),S=q("el-tag"),K=q("el-radio"),Z=q("el-table"),B=q("el-input"),z=q("el-form-item"),Ds=q("el-date-picker"),De=q("el-form"),oe=q("el-dialog"),Sa=q("el-radio-button"),Is=q("el-radio-group"),qs=q("el-progress"),Da=q("Search"),Al=q("el-empty"),Gs=q("el-pagination"),gl=q("el-card"),Ia=q("el-tab-pane"),Es=q("el-tooltip"),qa=q("el-button-group"),zs=q("el-tabs"),Ts=q("el-image"),Ga=q("el-alert"),bl=q("el-col"),Rs=q("el-row"),Fl=q("el-divider"),Ea=q("el-option-group"),za=q("el-optgroup"),hl=Ys("loading");return d(),v("div",an,[a(zs,{modelValue:Ne.value,"onUpdate:modelValue":e[19]||(e[19]=i=>Ne.value=i),class:"cad-tabs",onTabClick:pt},{default:t(()=>[a(Ia,{label:"质检工具",name:"quality"},{default:t(()=>[a(Hl,{name:"slide-fade",mode:"out-in"},{default:t(()=>[J(s("div",tn,[a(gl,{class:"license-card",style:{"margin-top":"0"}},{default:t(()=>[a(u,{active:Y.value,"finish-status":"success",simple:"",class:"quality-steps"},{default:t(()=>[a(o,{title:"开始"}),a(o,{title:"上传GDB"}),a(o,{title:"质检配置"}),a(o,{title:"选择许可"})]),_:1},8,["active"]),s("div",sn,[s("div",{class:"progress-bar",style:As({width:`${Y.value/(Gr-1)*100}%`})},null,4)]),s("div",nn,[a(Hl,{name:"step-fade",mode:"out-in"},{default:t(()=>{var i;return[(d(),v("div",{key:Y.value},[J(s("div",on,[s("div",un,[s("div",rn,[s("div",dn,[a(n(w),{class:"description-icon"},{default:t(()=>[a(n(Yl))]),_:1}),e[76]||(e[76]=s("h3",null,"数据质检工具",-1))]),s("div",pn,[e[85]||(e[85]=s("p",{class:"main-description"}," 数据质检工具可以帮助您检查GDB数据库中的数据质量问题，确保数据的完整性和准确性。 ",-1)),s("div",cn,[s("div",mn,[a(n(w),{class:"feature-icon"},{default:t(()=>[a(n(wl))]),_:1}),e[77]||(e[77]=s("span",null,"支持多种质检规则配置",-1))]),s("div",vn,[a(n(w),{class:"feature-icon"},{default:t(()=>[a(n(wl))]),_:1}),e[78]||(e[78]=s("span",null,"提供详细的质检报告",-1))]),s("div",fn,[a(n(w),{class:"feature-icon"},{default:t(()=>[a(n(wl))]),_:1}),e[79]||(e[79]=s("span",null,"支持批量数据处理",-1))])]),s("div",_n,[e[80]||(e[80]=s("p",{class:"steps-title"},"质检流程：",-1)),e[81]||(e[81]=s("span",{class:"step-tag"},"上传GDB",-1)),a(n(w),{class:"arrow-icon"},{default:t(()=>[a(n(Xe))]),_:1}),e[82]||(e[82]=s("span",{class:"step-tag"},"配置规则",-1)),a(n(w),{class:"arrow-icon"},{default:t(()=>[a(n(Xe))]),_:1}),e[83]||(e[83]=s("span",{class:"step-tag"},"选择许可",-1)),a(n(w),{class:"arrow-icon"},{default:t(()=>[a(n(Xe))]),_:1}),e[84]||(e[84]=s("span",{class:"step-tag"},"执行质检",-1))])])]),s("div",yn,[s("div",gn,[a(n(w),{class:"description-icon"},{default:t(()=>[a(n(Fs))]),_:1}),e[86]||(e[86]=s("h3",null,"质检项字典库",-1))]),s("div",bn,[e[91]||(e[91]=s("p",{class:"main-description"}," 浏览所有可用的质检项，了解质检规则和参数配置。 ",-1)),s("div",hn,[s("div",wn,[s("div",kn,[s("div",Vn,[a(n(w),null,{default:t(()=>[a(n(Ra))]),_:1}),e[87]||(e[87]=s("span",null,"一级分类",-1))]),s("div",Cn,[(d(!0),v(G,null,E(ml.value,r=>(d(),v("div",{key:r,class:qe(["cascade-item",{active:Q.value===r}]),onClick:b=>Us(r)},[s("span",xn,p(r),1),Q.value===r?(d(),C(n(w),{key:0,class:"arrow-icon"},{default:t(()=>[a(n(Xe))]),_:1})):$("",!0)],10,Ln))),128))])]),Q.value?(d(),v("div",$n,[s("div",Un,[a(n(w),null,{default:t(()=>[a(n(Os))]),_:1}),e[88]||(e[88]=s("span",null,"二级分类",-1))]),s("div",Sn,[(d(!0),v(G,null,E(xe.value,r=>(d(),v("div",{key:r,class:qe(["cascade-item",{active:W.value===r}]),onClick:b=>Ss(r)},[s("span",In,p(r),1),W.value===r?(d(),C(n(w),{key:0,class:"arrow-icon"},{default:t(()=>[a(n(Xe))]),_:1})):$("",!0)],10,Dn))),128))])])):$("",!0),W.value?(d(),v("div",qn,[s("div",Gn,[a(n(w),null,{default:t(()=>[a(n(Qe))]),_:1}),e[89]||(e[89]=s("span",null,"质检项",-1))]),s("div",En,[(d(!0),v(G,null,E(kt.value,r=>{var b;return d(),v("div",{key:r.id,class:qe(["cascade-item quality-item",{active:((b=f.value)==null?void 0:b.id)===r.id}]),onClick:P=>_s(r)},[s("div",Tn,[s("span",Rn,p(r.item_name),1),s("span",jn,p(r.item_code),1)]),a(n(w),{class:"info-icon"},{default:t(()=>[a(n(Yl))]),_:1})],10,zn)}),128))])])):$("",!0)])]),F.value.length>0?(d(),v("div",Mn,[s("div",Nn,[e[90]||(e[90]=s("span",{class:"stat-label"},"总质检项：",-1)),s("span",Pn,p(F.value.length)+"个",1)])])):$("",!0)])])]),s("div",Bn,[a(n(k),{type:"primary",size:"large",class:"start-button",onClick:Sl},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(We))]),_:1}),e[92]||(e[92]=m(" 开始质检 "))]),_:1})])],512),[[ce,Y.value===0]]),J(s("div",An,[e[99]||(e[99]=s("h2",{class:"step-title"},"选择许可并提交任务",-1)),s("div",Fn,[a(n(k),{type:"primary",onClick:Na},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ke))]),_:1}),e[93]||(e[93]=m(" 申请许可 "))]),_:1}),a(n(k),{type:"primary",onClick:Pa,disabled:tl.value},{default:t(()=>[a(n(w),{class:qe({"refresh-rotate":tl.value})},{default:t(()=>[a(n(Ze))]),_:1},8,["class"]),e[94]||(e[94]=m(" 刷新许可 "))]),_:1},8,["disabled"])]),Vl.value?(d(),C(V,{key:0,rows:5,animated:"",style:{margin:"20px 0"}},{template:t(()=>[a(c,{variant:"text",style:{width:"80px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"150px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"180px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"100px","margin-right":"16px"}}),a(c,{variant:"text",style:{width:"80px"}})]),_:1})):$("",!0),a(Z,{data:al.value,style:{width:"100%"},border:"",onRowClick:Oa,"row-class-name":la,class:"quality-table"},{default:t(()=>[a(y,{type:"index",label:"序号",width:"80",align:"center"}),a(y,{prop:"user_project",label:"项目","min-width":"150","show-overflow-tooltip":""}),a(y,{prop:"reason",label:"原因","min-width":"150","show-overflow-tooltip":""}),a(y,{prop:"usage_count",label:"申请次数",width:"100",align:"center"}),a(y,{prop:"count",label:"已用次数",width:"100",align:"center"}),a(y,{prop:"end_date",label:"截止时间",width:"180",align:"center"}),a(y,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(({row:r})=>[a(S,{type:Me(r.status).type},{default:t(()=>[m(p(Me(r.status).text),1)]),_:2},1032,["type"])]),_:1}),a(y,{label:"选择",width:"80",align:"center"},{default:t(({row:r})=>[a(K,{modelValue:te.value,"onUpdate:modelValue":e[0]||(e[0]=b=>te.value=b),label:r.id,disabled:la({row:r})==="disabled-row",class:"custom-radio"},{default:t(()=>[s("span",On,p(r.id),1)]),_:2},1032,["modelValue","label","disabled"])]),_:1})]),_:1},8,["data"]),s("div",Hn,[a(n(k),{onClick:Dl},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Xl))]),_:1}),e[95]||(e[95]=m(" 上一步 "))]),_:1}),a(n(k),{type:"primary",onClick:ds,disabled:!te.value||!_l.value,loading:Rl.value},{default:t(()=>[e[96]||(e[96]=m(" 提交质检任务 ")),a(n(w),null,{default:t(()=>[a(n(We))]),_:1})]),_:1},8,["disabled","loading"])]),a(oe,{modelValue:we.value,"onUpdate:modelValue":e[8]||(e[8]=r=>we.value=r),title:"申请许可-数据质检",width:"500px","close-on-click-modal":!1,onClose:e[9]||(e[9]=r=>we.value=!1),onAfterClose:ea},{footer:t(()=>[s("span",Zn,[a(n(k),{onClick:e[7]||(e[7]=r=>we.value=!1)},{default:t(()=>e[97]||(e[97]=[m("取消")])),_:1}),a(n(k),{type:"primary",loading:Cl.value,onClick:Aa},{default:t(()=>e[98]||(e[98]=[m("提交")])),_:1},8,["loading"])])]),default:t(()=>[a(De,{ref_key:"licenseFormRef",ref:Ge,model:A.value,rules:ja,"label-width":"100px",class:"apply-form",size:"small"},{default:t(()=>[a(z,{label:"工具名称",prop:"tool_name"},{default:t(()=>[a(B,{modelValue:A.value.tool_name,"onUpdate:modelValue":e[1]||(e[1]=r=>A.value.tool_name=r),value:"数据质检",disabled:""},null,8,["modelValue"])]),_:1}),a(z,{label:"使用项目",prop:"user_project"},{error:t(({error:r})=>[s("span",Yn,p(r),1)]),default:t(()=>[a(B,{modelValue:A.value.user_project,"onUpdate:modelValue":e[2]||(e[2]=r=>A.value.user_project=r),placeholder:"请输入使用项目"},null,8,["modelValue"])]),_:1}),a(z,{label:"申请原因",prop:"reason"},{error:t(({error:r})=>[s("span",Xn,p(r),1)]),default:t(()=>[a(B,{modelValue:A.value.reason,"onUpdate:modelValue":e[3]||(e[3]=r=>A.value.reason=r),type:"textarea",rows:3,placeholder:"请输入申请原因"},null,8,["modelValue"])]),_:1}),a(z,{label:"有效期",prop:"end_date"},{error:t(({error:r})=>[s("span",Qn,p(r),1)]),default:t(()=>[a(Ds,{modelValue:A.value.end_date,"onUpdate:modelValue":e[4]||(e[4]=r=>A.value.end_date=r),type:"date",placeholder:"请选择有效期","disabled-date":Ma,"default-time":new Date(2e3,1,1,23,59,59),style:{width:"100%",height:"32px","line-height":"32px"},format:"YYYY年MM月DD日","value-format":"YYYY-MM-DD"},null,8,["modelValue","default-time"])]),_:1}),a(z,{label:"申请次数",prop:"usage_count"},{error:t(({error:r})=>[s("span",Wn,p(r),1)]),default:t(()=>[a(n(me),{modelValue:A.value.usage_count,"onUpdate:modelValue":e[5]||(e[5]=r=>A.value.usage_count=r),min:1,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(z,{label:"审批人",prop:"approver"},{error:t(({error:r})=>[s("span",Kn,p(r),1)]),default:t(()=>[a(n(X),{modelValue:A.value.approver,"onUpdate:modelValue":e[6]||(e[6]=r=>A.value.approver=r),placeholder:"请选择审批人",style:{width:"100%",height:"32px","line-height":"32px"}},{default:t(()=>[(d(!0),v(G,null,E(Jl.value,r=>(d(),C(n(I),{key:r.username,label:r.real_name,value:r.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],512),[[ce,Y.value===3]]),J(s("div",Jn,[e[116]||(e[116]=s("h2",{class:"step-title"},"质检配置管理",-1)),s("div",eo,[s("div",lo,[s("div",ao,[a(Is,{modelValue:Ee.value,"onUpdate:modelValue":e[10]||(e[10]=r=>Ee.value=r),onChange:ns},{default:t(()=>[a(Sa,{label:"upload"},{default:t(()=>e[100]||(e[100]=[m("上传新文件")])),_:1}),a(Sa,{label:"history"},{default:t(()=>e[101]||(e[101]=[m("选择历史文件")])),_:1})]),_:1},8,["modelValue"])]),J(s("div",to,[e[105]||(e[105]=s("h3",null,"上传GDB压缩包",-1)),a(n(Ql),{class:"gdb-upload",drag:"",action:ll("/api/quality/upload-gdb"),headers:Ll.value,"show-file-list":!1,"before-upload":Ha,"on-success":Qa,"on-error":Za,"on-progress":Ya,accept:".zip,.rar,.7z",disabled:ke.value},{tip:t(()=>e[102]||(e[102]=[s("div",{class:"el-upload__tip"}," 支持 .zip、.rar、.7z 格式压缩包，单个文件不超过10GB ",-1)])),default:t(()=>[a(n(w),{class:"el-icon--upload"},{default:t(()=>[a(n(Wl))]),_:1}),e[103]||(e[103]=s("div",{class:"el-upload__text"},[m(" 将GDB压缩包拖到此处，或"),s("em",null,"点击上传")],-1))]),_:1},8,["action","headers","disabled"]),ke.value||ie.value?(d(),v("div",so,[a(qs,{percentage:ue.value,status:Xa()},null,8,["percentage","status"]),s("p",no,p(de.value),1),ie.value&&se.value?(d(),v("div",oo,[s("div",uo,[a(n(w),{class:"progress-icon"},{default:t(()=>[a(n(Hs))]),_:1}),s("span",null,p(se.value.message),1)]),se.value.gdb_databases&&se.value.gdb_databases.length>0?(d(),v("div",io,[e[104]||(e[104]=s("p",{class:"databases-title"},"已解析的数据库：",-1)),s("div",ro,[(d(!0),v(G,null,E(se.value.gdb_databases,(r,b)=>(d(),v("div",{key:b,class:"database-item"},[a(n(w),null,{default:t(()=>[a(n(Qe))]),_:1}),s("span",po,p(r.name),1),s("span",co,"("+p(r.layers.length)+" 个图层)",1)]))),128))])])):$("",!0)])):$("",!0)])):$("",!0)],512),[[ce,Ee.value==="upload"]]),J(s("div",mo,[e[110]||(e[110]=s("h3",null,"选择历史提交文件",-1)),s("div",vo,[a(n(k),{onClick:Bl,loading:ze.value,size:"small"},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ze))]),_:1}),e[106]||(e[106]=m(" 刷新列表 "))]),_:1},8,["loading"]),a(B,{modelValue:sl.value,"onUpdate:modelValue":e[11]||(e[11]=r=>sl.value=r),placeholder:"搜索历史文件名",size:"small",style:{width:"200px","margin-left":"8px"},clearable:""},{prefix:t(()=>[a(n(w),null,{default:t(()=>[a(Da)]),_:1})]),_:1},8,["modelValue"])]),ka.value.length>0?(d(),v("div",fo,[a(Z,{data:ka.value,onRowClick:us,"highlight-current-row":"","current-row-key":ee.value,"row-key":"path",size:"small","max-height":"300","row-class-name":is},{default:t(()=>[a(y,{label:"选择",width:"50",align:"center"},{default:t(({row:r})=>[a(K,{modelValue:ee.value,"onUpdate:modelValue":e[12]||(e[12]=b=>ee.value=b),label:r.path,onClick:e[13]||(e[13]=Je(()=>{},["stop"]))},{default:t(()=>e[107]||(e[107]=[s("span",null,null,-1)])),_:2},1032,["modelValue","label"])]),_:1}),a(y,{prop:"name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),a(y,{prop:"upload_time",label:"上传时间",width:"120",align:"center"},{default:t(({row:r})=>[s("span",null,p(il(r.upload_time,"MM-dd HH:mm")),1)]),_:1}),a(y,{prop:"databases_count",label:"数据库数",width:"80",align:"center"},{default:t(({row:r})=>[s("span",_o,p(r.databases_count||0),1)]),_:1}),a(y,{prop:"total_layers",label:"图层数",width:"80",align:"center"},{default:t(({row:r})=>[s("span",yo,p(r.total_layers||0),1)]),_:1}),a(y,{label:"数据库","min-width":"120","show-overflow-tooltip":""},{default:t(({row:r})=>[r.database_names&&r.database_names.length>0?(d(),v("div",go,[(d(!0),v(G,null,E(r.database_names.slice(0,2),(b,P)=>(d(),C(S,{key:P,size:"small",effect:"plain",class:"db-tag"},{default:t(()=>[m(p(b),1)]),_:2},1024))),128)),r.database_names.length>2?(d(),v("span",bo," +"+p(r.database_names.length-2),1)):$("",!0)])):(d(),v("span",ho,"未知"))]),_:1}),a(y,{label:"状态",width:"80",align:"center"},{default:t(({row:r})=>[a(S,{type:r.has_cached_info?"success":"warning",size:"small",effect:"plain"},{default:t(()=>[m(p(r.has_cached_info?"已缓存":"需解析"),1)]),_:2},1032,["type"])]),_:1}),a(y,{label:"操作",width:"80",align:"center"},{default:t(({row:r})=>[a(n(k),{size:"small",type:"danger",onClick:Je(b=>rs(r),["stop"]),loading:$l.value===r.path},{default:t(()=>e[108]||(e[108]=[m(" 删除 ")])),_:2},1032,["onClick","loading"])]),_:1})]),_:1},8,["data","current-row-key"])])):ze.value?$("",!0):(d(),v("div",wo,[a(Al,{description:"暂无历史GDB文件"},{default:t(()=>[a(n(k),{type:"primary",onClick:e[14]||(e[14]=r=>Ee.value="upload")},{default:t(()=>e[109]||(e[109]=[m(" 上传第一个GDB文件 ")])),_:1})]),_:1})])),ze.value?(d(),v("div",ko,[a(V,{rows:3,animated:""})])):$("",!0)],512),[[ce,Ee.value==="history"]])]),s("div",Vo,[j.value.length>0?(d(),v("div",Co,[e[112]||(e[112]=s("h3",null,"选择GDB数据库",-1)),J((d(),C(Z,{data:j.value,style:{width:"100%"},border:"",size:"small",onRowClick:at,"highlight-current-row":"","element-loading-text":"加载GDB信息中..."},{default:t(()=>[a(y,{label:"选择",width:"50",align:"center"},{default:t(r=>[a(K,{modelValue:D.value,"onUpdate:modelValue":e[15]||(e[15]=b=>D.value=b),label:r.row.path,onClick:e[16]||(e[16]=Je(()=>{},["stop"]))},{default:t(()=>e[111]||(e[111]=[s("span",null,null,-1)])),_:2},1032,["modelValue","label"])]),_:1}),a(y,{prop:"name",label:"GDB名称","min-width":"120"},{default:t(r=>[s("div",Lo,p(r.row.name),1)]),_:1}),a(y,{prop:"layers",label:"图层数",width:"80",align:"center"},{default:t(r=>[s("span",xo,p(r.row.layers.length),1)]),_:1}),a(y,{prop:"totalFeatures",label:"要素数",width:"100",align:"center"},{default:t(r=>{var b;return[s("span",$o,p(((b=r.row.totalFeatures)==null?void 0:b.toLocaleString())||0),1)]}),_:1})]),_:1},8,["data"])),[[hl,Te.value]])])):ke.value?$("",!0):(d(),v("div",Uo,[a(n(w),{class:"empty-icon"},{default:t(()=>[a(n(Ra))]),_:1}),e[113]||(e[113]=s("p",null,"请先上传GDB压缩包",-1))]))])]),D.value&&N.value.length>0?(d(),v("div",So,[s("div",Do,[s("div",Io,[e[114]||(e[114]=s("h3",null,"图层预览 (所有图层将进入质检流程)",-1)),s("div",qo," 共 "+p(N.value.length)+" 个图层，"+p(dt.value.toLocaleString())+" 个要素 ",1)]),J((d(),C(Z,{data:Fa.value,style:{width:"100%"},border:""},{default:t(()=>[a(y,{prop:"name",label:"图层名称",width:"300"},{default:t(r=>[s("div",Go,[a(n(w),null,{default:t(()=>[(d(),C(Xs(ut(r.row.type))))]),_:2},1024),s("span",null,p(Ja(r.row)),1)])]),_:1}),a(y,{prop:"geometry_type",label:"几何类型",width:"150"},{default:t(r=>[a(S,{type:rt(r.row),size:"small"},{default:t(()=>[m(p(it(r.row)),1)]),_:2},1032,["type"])]),_:1}),a(y,{prop:"feature_set",label:"要素集",width:"200"},{default:t(r=>[r.row.feature_set?(d(),v("span",Eo,p(r.row.feature_set),1)):(d(),v("span",zo,"-"))]),_:1}),a(y,{prop:"feature_count",label:"要素数量",width:"120"},{default:t(r=>{var b;return[m(p(((b=r.row.feature_count)==null?void 0:b.toLocaleString())||0),1)]}),_:1}),a(y,{prop:"coordinate_system",label:"坐标系",width:"200"},{default:t(r=>[r.row.coordinate_system?(d(),v("span",{key:0,title:r.row.coordinate_system},p(ot(r.row.coordinate_system)),9,To)):(d(),v("span",Ro,"未知"))]),_:1}),a(y,{prop:"fields",label:"字段信息","min-width":"300"},{default:t(r=>[r.row.fields&&r.row.fields.length>0?(d(),v("div",{key:0,class:"fields-display",ref:`fieldsContainer-${r.$index}`},[(d(!0),v(G,null,E(r.row.fields,(b,P)=>(d(),C(S,{key:b.name||b,size:"small",effect:"plain",class:qe(["field-tag",{"field-hidden":nt(P,r.row.fields.length,r.$index)}]),title:lt(b),ref_for:!0,ref:`fieldTag-${r.$index}-${P}`},{default:t(()=>[m(p(et(b)),1)]),_:2},1032,["class","title"]))),128)),aa(r.row.fields.length,r.$index)>0?(d(),v("span",jo," +"+p(aa(r.row.fields.length,r.$index))+" 个字段 ",1)):$("",!0)],512)):(d(),v("span",Mo,"无字段信息"))]),_:1})]),_:1},8,["data"])),[[hl,Ul.value]]),s("div",No,[a(Gs,{"current-page":Ve.value,"onUpdate:currentPage":e[17]||(e[17]=r=>Ve.value=r),"page-size":Re.value,"onUpdate:pageSize":e[18]||(e[18]=r=>Re.value=r),"page-sizes":[10,20,50,100],total:N.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:tt,onCurrentChange:st},null,8,["current-page","page-size","total"])])])])):$("",!0),s("div",Po,[a(n(k),{onClick:Dl},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Xl))]),_:1}),e[115]||(e[115]=m(" 上一步 "))]),_:1}),a(n(k),{type:"primary",disabled:!D.value,onClick:Sl},{default:t(()=>[m(" 下一步（"+p(N.value.length)+"个图层） ",1),a(n(w),null,{default:t(()=>[a(n(We))]),_:1})]),_:1},8,["disabled"])])],512),[[ce,Y.value===1]]),J(s("div",Bo,[e[132]||(e[132]=s("h2",{class:"step-title"},"质检配置管理",-1)),s("div",Ao,[a(gl,null,{header:t(()=>[s("div",Fo,[s("div",Oo,[e[117]||(e[117]=s("span",{class:"section-title"},"选择或创建质检配置",-1)),ge.value.length>0?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(ge.value.length)+" 个配置 ",1)]),_:1})):$("",!0)]),s("div",Ho,[a(n(k),{onClick:$e,loading:ql.value},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ze))]),_:1}),e[118]||(e[118]=m(" 刷新 "))]),_:1},8,["loading"]),a(n(k),{type:"primary",onClick:ra},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ke))]),_:1}),e[119]||(e[119]=m(" 新建配置 "))]),_:1})])])]),default:t(()=>{var r;return[ge.value.length>0?(d(),v("div",Yo,[a(Z,{data:ge.value,onRowClick:fl,"highlight-current-row":"","current-row-key":(r=U.value)==null?void 0:r.id,"row-key":"id"},{default:t(()=>[a(y,{label:"配置名称","min-width":"200","show-overflow-tooltip":""},{default:t(({row:b})=>[s("div",Xo,[s("span",Qo,p(b.name),1),b.is_matched?(d(),C(S,{key:0,type:"success",size:"small",class:"matched-tag"},{default:t(()=>e[120]||(e[120]=[m(" 关联配置 ")])),_:1})):$("",!0)])]),_:1}),a(y,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),a(y,{prop:"gdb_filename",label:"关联GDB文件","min-width":"200",align:"center","show-overflow-tooltip":""},{default:t(({row:b})=>[b.gdb_filename?(d(),v("span",Wo,p(b.gdb_filename.endsWith(".gdb")?b.gdb_filename:b.gdb_filename+".gdb"),1)):(d(),v("span",Ko,"通用配置"))]),_:1}),a(y,{prop:"item_count",label:"质检项数量",width:"100",align:"center"},{default:t(({row:b})=>[a(S,{type:"info"},{default:t(()=>[m(p(b.item_count)+" 项",1)]),_:2},1024)]),_:1}),a(y,{prop:"updated_at",label:"更新时间",width:"150",align:"center"},{default:t(({row:b})=>[m(p(il(b.updated_at,"yyyy-MM-dd HH:mm")),1)]),_:1}),a(y,{label:"操作",width:"200",align:"center"},{default:t(({row:b})=>[a(n(k),{size:"small",type:"danger",onClick:Je(P=>St(b),["stop"])},{default:t(()=>e[121]||(e[121]=[m("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","current-row-key"])])):(d(),v("div",Zo,[a(Al,{description:"暂无质检配置"},{default:t(()=>[a(n(k),{type:"primary",onClick:ra},{default:t(()=>e[122]||(e[122]=[m("创建第一个配置")])),_:1})]),_:1})]))]}),_:1})]),U.value?(d(),v("div",Jo,[a(gl,null,{header:t(()=>[s("div",eu,[s("div",lu,[s("span",au,p(U.value.name),1),U.value.items&&U.value.items.length>0?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(U.value.items.length)+" 个质检项 ",1)]),_:1})):$("",!0)]),s("div",tu,[a(n(k),{onClick:ha,loading:Tl.value},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Qs))]),_:1}),e[123]||(e[123]=m(" 保存配置 "))]),_:1},8,["loading"]),a(n(k),{type:"primary",onClick:da},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ke))]),_:1}),e[124]||(e[124]=m(" 添加质检项 "))]),_:1})])])]),default:t(()=>[U.value.items&&U.value.items.length>0?(d(),v("div",su,[(d(!0),v(G,null,E(ts.value,(r,b)=>(d(),v("div",{key:b,class:"category-group"},[s("div",nu,[s("h4",null,p(b),1),a(S,{type:"info",size:"small"},{default:t(()=>[m(p(r.length)+" 项",1)]),_:2},1024)]),a(Z,{data:r,size:"small",class:"config-items-table","show-header":!0,stripe:""},{default:t(()=>[a(y,{prop:"item_name",label:"质检项名称",width:"200","show-overflow-tooltip":""}),a(y,{prop:"object_type",label:"对象类型",width:"100",align:"center"},{default:t(({row:P})=>[a(S,{type:ye(P.object_type),size:"small"},{default:t(()=>[m(p(P.object_type),1)]),_:2},1032,["type"])]),_:1}),a(y,{label:"适用图层","min-width":"150","show-overflow-tooltip":""},{default:t(({row:P})=>[P.selected_layers&&P.selected_layers.length>0?(d(),v("span",ou,p(P.selected_layers.join(", ")),1)):(d(),v("span",uu,"未配置"))]),_:1}),a(y,{label:"参数配置",width:"300",align:"center"},{default:t(({row:P})=>[P.parameters&&Object.keys(P.parameters).length>0?(d(),v("span",iu,p(It(P.parameters)),1)):(d(),v("span",ru,"无参数"))]),_:1}),a(y,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(({row:P,$index:Tr})=>[a(n(k),{size:"small",onClick:js=>bs(P,La(P))},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Zl))]),_:1})]),_:2},1032,["onClick"]),a(n(k),{size:"small",type:"danger",onClick:js=>Dt(La(P))},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Kl))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:2},1032,["data"])]))),128))])):(d(),v("div",du,[a(Al,{description:"暂无质检项配置"},{default:t(()=>[a(n(k),{type:"primary",onClick:da},{default:t(()=>e[125]||(e[125]=[m("添加第一个质检项")])),_:1})]),_:1})]))]),_:1})])):$("",!0),U.value?(d(),v("div",pu,[e[129]||(e[129]=s("div",{class:"section-header"},[s("h3",null,"配置预览")],-1)),s("div",cu,[s("div",mu,[e[126]||(e[126]=s("span",{class:"label"},"配置名称:",-1)),s("span",vu,p(U.value.name),1)]),s("div",fu,[e[127]||(e[127]=s("span",{class:"label"},"质检项数量:",-1)),s("span",_u,p(((i=U.value.items)==null?void 0:i.length)||0)+" 项",1)]),s("div",yu,[e[128]||(e[128]=s("span",{class:"label"},"目标图层:",-1)),s("span",gu,p(N.value.length)+" 个图层",1)])])])):$("",!0),s("div",bu,[a(n(k),{onClick:Dl},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Xl))]),_:1}),e[130]||(e[130]=m(" 上一步 "))]),_:1}),a(n(k),{type:"primary",disabled:!_l.value,onClick:Sl},{default:t(()=>[e[131]||(e[131]=m(" 下一步 ")),a(n(w),null,{default:t(()=>[a(n(We))]),_:1})]),_:1},8,["disabled"])])],512),[[ce,Y.value===2]])]))]}),_:1})])]),_:1})],512),[[ce,Ne.value==="quality"]])]),_:1})]),_:1}),a(Ia,{label:"历史记录",name:"history"},{default:t(()=>[a(Hl,{name:"slide-fade",mode:"out-in"},{default:t(()=>[J(s("div",hu,[a(gl,{class:"history-card",style:{"margin-top":"0px"}},{default:t(()=>[s("div",wu,[J((d(),C(Z,{data:ol.value,style:{width:"100%"}},{default:t(()=>[a(y,{type:"index",label:"序号",width:"80",align:"center"}),a(y,{prop:"submit_time",label:"提交时间",width:"180",align:"center"},{default:t(({row:i})=>[a(Es,{content:il(i.submit_time,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:t(()=>[s("span",null,p(il(i.submit_time,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),a(y,{prop:"status",label:"状态",width:"100",align:"center"},{default:t(({row:i})=>[a(S,{type:mt(i.status)},{default:t(()=>[m(p(vt(i.status)),1)]),_:2},1032,["type"])]),_:1}),a(y,{prop:"time_consuming",label:"运行耗时",width:"120",align:"center"},{default:t(({row:i})=>[m(p(ct(i.time_consuming)),1)]),_:1}),a(y,{prop:"file_size",label:"文件大小",width:"100",align:"center"}),a(y,{prop:"up_nums",label:"质检文件数量","min-width":"130",align:"center","show-overflow-tooltip":""}),a(y,{label:"操作",width:"300",align:"center",fixed:"right"},{default:t(({row:i})=>[s("div",ku,[a(qa,null,{default:t(()=>[i.status==="success"&&i.up_nums>0&&i.file_size!=="0.0MB"?(d(),C(n(k),{key:0,type:"success",size:"small",onClick:r=>ft(i),disabled:i.status!=="success"},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ws))]),_:1}),e[133]||(e[133]=m(" 下载 "))]),_:2},1032,["onClick","disabled"])):$("",!0),i.error_message?(d(),C(n(k),{key:1,type:"info",size:"small",onClick:r=>_t(i)},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Qe))]),_:1}),e[134]||(e[134]=m(" 日志 "))]),_:2},1032,["onClick"])):$("",!0),a(n(k),{type:"danger",size:"small",onClick:r=>yt(i)},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Kl))]),_:1}),e[135]||(e[135]=m(" 删除 "))]),_:2},1032,["onClick"])]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[hl,Il.value]])])]),_:1})],512),[[ce,Ne.value==="history"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(oe,{modelValue:ul.value,"onUpdate:modelValue":e[21]||(e[21]=i=>ul.value=i),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:t(()=>[s("span",Cu,[a(n(k),{onClick:e[20]||(e[20]=i=>ul.value=!1)},{default:t(()=>e[136]||(e[136]=[m("关闭")])),_:1})])]),default:t(()=>[s("div",Vu,[s("pre",null,p(na.value||"暂无错误信息"),1)])]),_:1},8,["modelValue"]),a(oe,{modelValue:El.value,"onUpdate:modelValue":e[22]||(e[22]=i=>El.value=i),title:"质检项管理",width:"80%","close-on-click-modal":!1},{default:t(()=>[s("div",Lu,[s("div",xu,[a(n(k),{type:"primary",onClick:Wt},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ke))]),_:1}),e[137]||(e[137]=m(" 新增质检项 "))]),_:1}),a(n(k),{onClick:_e},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ze))]),_:1}),e[138]||(e[138]=m(" 刷新列表 "))]),_:1})]),J((d(),C(Z,{data:F.value,style:{width:"100%"},border:"","max-height":"500"},{default:t(()=>[a(y,{type:"index",label:"序号",width:"60",align:"center"}),a(y,{prop:"level1_name",label:"一级分类",width:"80",align:"center"}),a(y,{prop:"level2_name",label:"二级分类",width:"120"}),a(y,{prop:"item_name",label:"质检项名称","min-width":"150","show-overflow-tooltip":""}),a(y,{prop:"object_type",label:"对象类型",width:"80",align:"center"},{default:t(({row:i})=>[a(S,{size:"small",type:ye(i.object_type)},{default:t(()=>[m(p(i.object_type||"通用"),1)]),_:2},1032,["type"])]),_:1}),a(y,{prop:"item_code",label:"质检项代码",width:"100",align:"center"}),a(y,{prop:"rule_content",label:"规则内容","min-width":"200","show-overflow-tooltip":""}),a(y,{prop:"param_type",label:"参数类型",width:"80",align:"center"}),a(y,{label:"操作",width:"120",align:"center",fixed:"right"},{default:t(({row:i})=>[a(n(k),{size:"small",onClick:r=>Kt(i)},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Zl))]),_:1})]),_:2},1032,["onClick"]),a(n(k),{size:"small",type:"danger",onClick:r=>es(i)},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Kl))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[hl,pl.value]])])]),_:1},8,["modelValue"]),a(oe,{modelValue:Le.value,"onUpdate:modelValue":e[32]||(e[32]=i=>Le.value=i),title:be.value?"编辑质检项":"新增质检项",width:"600px","close-on-click-modal":!1},{footer:t(()=>[s("div",$u,[a(n(k),{onClick:e[31]||(e[31]=i=>Le.value=!1)},{default:t(()=>e[139]||(e[139]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:Jt,loading:zl.value},{default:t(()=>[m(p(be.value?"更新":"保存"),1)]),_:1},8,["loading"])])]),default:t(()=>[a(De,{ref_key:"qualityItemFormRef",ref:He,model:M.value,rules:Vt,"label-width":"100px",class:"quality-item-form"},{default:t(()=>[a(z,{label:"一级分类",prop:"level1_name"},{default:t(()=>[a(n(X),{modelValue:M.value.level1_name,"onUpdate:modelValue":e[23]||(e[23]=i=>M.value.level1_name=i),placeholder:"请选择或输入一级分类",style:{width:"100%"},filterable:"","allow-create":"",onChange:Yt},{default:t(()=>[(d(!0),v(G,null,E(ht.value,i=>(d(),C(n(I),{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(z,{label:"二级分类",prop:"level2_name"},{default:t(()=>[a(n(X),{modelValue:M.value.level2_name,"onUpdate:modelValue":e[24]||(e[24]=i=>M.value.level2_name=i),placeholder:"请选择或输入二级分类",style:{width:"100%"},filterable:"","allow-create":""},{default:t(()=>[(d(!0),v(G,null,E(wt.value,i=>(d(),C(n(I),{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(z,{label:"质检项名称",prop:"item_name"},{default:t(()=>[a(B,{modelValue:M.value.item_name,"onUpdate:modelValue":e[25]||(e[25]=i=>M.value.item_name=i),placeholder:"请输入质检项名称"},null,8,["modelValue"])]),_:1}),a(z,{label:"对象类型",prop:"object_type"},{default:t(()=>[a(n(X),{modelValue:M.value.object_type,"onUpdate:modelValue":e[26]||(e[26]=i=>M.value.object_type=i),placeholder:"请选择对象类型",style:{width:"100%"},filterable:"","allow-create":""},{default:t(()=>[a(n(I),{label:"数据集",value:"数据集"}),a(n(I),{label:"图层",value:"图层"}),a(n(I),{label:"字段",value:"字段"}),a(n(I),{label:"点要素",value:"点要素"}),a(n(I),{label:"线要素",value:"线要素"}),a(n(I),{label:"面要素",value:"面要素"}),a(n(I),{label:"通用",value:"通用"})]),_:1},8,["modelValue"])]),_:1}),a(z,{label:"质检项代码",prop:"item_code"},{default:t(()=>[a(B,{modelValue:M.value.item_code,"onUpdate:modelValue":e[27]||(e[27]=i=>M.value.item_code=i),placeholder:"请输入质检项代码，如：A001"},null,8,["modelValue"])]),_:1}),a(z,{label:"规则内容",prop:"rule_content"},{default:t(()=>[a(B,{modelValue:M.value.rule_content,"onUpdate:modelValue":e[28]||(e[28]=i=>M.value.rule_content=i),type:"textarea",rows:3,placeholder:"请输入质检规则的详细描述"},null,8,["modelValue"])]),_:1}),a(z,{label:"参数类型",prop:"param_type"},{default:t(()=>[a(n(X),{modelValue:M.value.param_type,"onUpdate:modelValue":e[29]||(e[29]=i=>M.value.param_type=i),placeholder:"请选择参数类型",style:{width:"100%"}},{default:t(()=>[a(n(I),{label:"无",value:"无"}),a(n(I),{label:"单选",value:"单选"}),a(n(I),{label:"数值",value:"数值"}),a(n(I),{label:"范围",value:"范围"}),a(n(I),{label:"文件上传",value:"文件上传"}),a(n(I),{label:"图层选择",value:"图层选择"}),a(n(I),{label:"双图层选择",value:"双图层选择"})]),_:1},8,["modelValue"])]),_:1}),a(z,{label:"参数名称",prop:"param_name"},{default:t(()=>[a(B,{modelValue:M.value.param_name,"onUpdate:modelValue":e[30]||(e[30]=i=>M.value.param_name=i),placeholder:"请输入参数名称（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),a(oe,{modelValue:ia.value,"onUpdate:modelValue":e[33]||(e[33]=i=>ia.value=i),title:"质检项库",width:"90%","close-on-click-modal":!1},{default:t(()=>[s("div",Uu,[s("div",Su,[a(n(k),{type:"primary",onClick:_e,loading:pl.value},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ze))]),_:1}),e[140]||(e[140]=m(" 刷新质检项 "))]),_:1},8,["loading"]),a(n(k),{onClick:zt},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ke))]),_:1}),e[141]||(e[141]=m(" 展开全部 "))]),_:1}),a(n(k),{onClick:Tt},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Ks))]),_:1}),e[142]||(e[142]=m(" 收起全部 "))]),_:1}),a(n(k),{onClick:Qt},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Zl))]),_:1}),e[143]||(e[143]=m(" 管理质检项 "))]),_:1}),s("div",Du,[s("span",null,"共 "+p(F.value.length)+" 个质检项",1)])]),s("div",Iu,[(d(!0),v(G,null,E(Gl.value,i=>(d(),v("div",{key:i.id,class:"category-card"},[s("div",{class:"category-header",onClick:r=>Gt(i.id)},[s("div",Gu,[a(n(w),{class:qe(["category-icon",{expanded:ve.value.includes(i.id)}])},{default:t(()=>[a(n(We))]),_:2},1032,["class"]),s("span",Eu,p(i.label),1),a(S,{size:"small",type:"info"},{default:t(()=>[m(p(Et(i))+" 项",1)]),_:2},1024)])],8,qu),J(s("div",zu,[(d(!0),v(G,null,E(i.children,r=>(d(),v("div",{key:r.id,class:"sub-category"},[s("div",Tu,[s("span",Ru,p(r.label),1),a(S,{size:"small"},{default:t(()=>[m(p(r.children.length)+" 项",1)]),_:2},1024)]),s("div",ju,[(d(!0),v(G,null,E(r.children,b=>(d(),v("div",{key:b.id,class:"quality-item-card library-item-card"},[s("div",Mu,[s("div",Nu,[s("span",Pu,p(b.data.item_name),1),a(S,{size:"small",type:ye(b.data.object_type)},{default:t(()=>[m(p(b.data.object_type||"通用"),1)]),_:2},1032,["type"])])]),s("div",Bu,[s("div",Au,"编码: "+p(b.data.item_code),1),s("div",{class:"item-rule",title:b.data.rule_content},p(b.data.rule_content),9,Fu),b.data.param_type&&b.data.param_type!=="无"?(d(),v("div",Ou,[s("span",Hu,"参数类型: "+p(b.data.param_type),1),b.data.param_name?(d(),v("span",Yu,"参数名: "+p(b.data.param_name),1)):$("",!0)])):$("",!0),s("div",Xu,[b.data.example_img?(d(),v("div",Qu,[s("img",{src:b.data.example_img,alt:b.data.item_name},null,8,Wu)])):(d(),v("div",Ku,[a(n(w),null,{default:t(()=>[a(n(Qe))]),_:1}),e[144]||(e[144]=s("span",null,"暂无示意图",-1))]))])])]))),128))])]))),128))],512),[[ce,ve.value.includes(i.id)]])]))),128))])])]),_:1},8,["modelValue"]),a(oe,{modelValue:cl.value,"onUpdate:modelValue":e[34]||(e[34]=i=>cl.value=i),title:((Ta=O.value)==null?void 0:Ta.item_name)||"质检项详情",width:"800px","before-close":$a},{footer:t(()=>[s("div",Si,[a(n(k),{onClick:$a},{default:t(()=>e[158]||(e[158]=[m("关闭")])),_:1})])]),default:t(()=>[O.value?(d(),v("div",Zu,[s("div",Ju,[s("h4",ei,[a(n(w),null,{default:t(()=>[a(n(Yl))]),_:1}),e[145]||(e[145]=m(" 基本信息 "))]),s("div",li,[s("div",ai,[e[146]||(e[146]=s("span",{class:"detail-label"},"质检项名称：",-1)),s("span",ti,p(O.value.item_name),1)]),s("div",si,[e[147]||(e[147]=s("span",{class:"detail-label"},"质检项代码：",-1)),s("span",ni,p(O.value.item_code),1)]),s("div",oi,[e[148]||(e[148]=s("span",{class:"detail-label"},"一级分类：",-1)),s("span",ui,p(O.value.level1_name),1)]),s("div",ii,[e[149]||(e[149]=s("span",{class:"detail-label"},"二级分类：",-1)),s("span",ri,p(O.value.level2_name),1)]),s("div",di,[e[150]||(e[150]=s("span",{class:"detail-label"},"对象类型：",-1)),a(S,{type:ye(O.value.object_type),size:"small"},{default:t(()=>[m(p(O.value.object_type||"通用"),1)]),_:1},8,["type"])]),s("div",pi,[e[151]||(e[151]=s("span",{class:"detail-label"},"参数类型：",-1)),s("span",ci,p(O.value.param_type||"无"),1)])])]),s("div",mi,[s("h4",vi,[a(n(w),null,{default:t(()=>[a(n(Qe))]),_:1}),e[152]||(e[152]=m(" 质检规则 "))]),s("div",fi,p(O.value.rule_content),1)]),O.value.param_name?(d(),v("div",_i,[s("h4",yi,[a(n(w),null,{default:t(()=>[a(n(Zs))]),_:1}),e[153]||(e[153]=m(" 参数说明 "))]),s("div",gi,[s("span",bi,p(O.value.param_name),1),s("span",hi,"（"+p(O.value.param_type)+"）",1)])])):$("",!0),O.value.example_img?(d(),v("div",wi,[s("h4",ki,[a(n(w),null,{default:t(()=>[a(n(kl))]),_:1}),e[154]||(e[154]=m(" 示意图 "))]),s("div",Vi,[a(Ts,{src:O.value.example_img,"preview-src-list":[O.value.example_img],fit:"contain",style:{width:"100%","max-height":"300px"},"preview-teleported":!0},{error:t(()=>[s("div",Ci,[a(n(w),null,{default:t(()=>[a(n(kl))]),_:1}),e[155]||(e[155]=s("span",null,"暂无示意图",-1))])]),_:1},8,["src","preview-src-list"])])])):(d(),v("div",Li,[s("h4",xi,[a(n(w),null,{default:t(()=>[a(n(kl))]),_:1}),e[156]||(e[156]=m(" 示意图 "))]),s("div",$i,[s("div",Ui,[a(n(w),{size:"48"},{default:t(()=>[a(n(kl))]),_:1}),s("p",null,p(O.value.item_name),1),e[157]||(e[157]=s("p",{class:"placeholder-text"},"暂无配图，请参考规则内容进行理解",-1))])])]))])):$("",!0)]),_:1},8,["modelValue","title"]),a(oe,{modelValue:Pe.value,"onUpdate:modelValue":e[39]||(e[39]=i=>Pe.value=i),title:"新建质检配置",width:"500px"},{footer:t(()=>[a(n(k),{onClick:e[38]||(e[38]=i=>Pe.value=!1)},{default:t(()=>e[162]||(e[162]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:xt},{default:t(()=>e[163]||(e[163]=[m("确定")])),_:1})]),default:t(()=>[s("div",Di,[a(Ga,{title:`将为 GDB 文件 '${vl()}' 创建新的质检配置`,type:"info",closable:!1,"show-icon":""},null,8,["title"])]),a(De,{model:le.value,"label-width":"80px",style:{"margin-top":"20px"}},{default:t(()=>[a(z,{label:"配置名称",required:""},{default:t(()=>[a(B,{modelValue:le.value.name,"onUpdate:modelValue":e[35]||(e[35]=i=>le.value.name=i),placeholder:"请输入配置名称"},null,8,["modelValue"]),e[159]||(e[159]=s("div",{class:"form-tip"}," 注意：在同一GDB文件下不能有重名配置 ",-1))]),_:1}),a(z,{label:"配置描述"},{default:t(()=>[a(B,{modelValue:le.value.description,"onUpdate:modelValue":e[36]||(e[36]=i=>le.value.description=i),type:"textarea",rows:3,placeholder:"请输入配置描述（可选）"},null,8,["modelValue"])]),_:1}),a(z,{label:"全局容差",required:""},{default:t(()=>[a(n(me),{modelValue:le.value.globalTolerance,"onUpdate:modelValue":e[37]||(e[37]=i=>le.value.globalTolerance=i),precision:3,step:.001,min:.001,max:1,style:{width:"200px"}},null,8,["modelValue"]),e[160]||(e[160]=s("span",{class:"tolerance-unit"},"米",-1)),e[161]||(e[161]=s("div",{class:"form-tip"}," 全局容差用于几何精度检查，默认值为0.001米 ",-1))]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(oe,{modelValue:Be.value,"onUpdate:modelValue":e[44]||(e[44]=i=>Be.value=i),title:"创建配置副本",width:"500px"},{footer:t(()=>[a(n(k),{onClick:e[43]||(e[43]=i=>Be.value=!1)},{default:t(()=>e[169]||(e[169]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:$t},{default:t(()=>e[170]||(e[170]=[m("创建副本")])),_:1})]),default:t(()=>{var i,r;return[s("div",Ii,[a(Ga,{title:"该配置与当前GDB文件不匹配，需要创建副本后才能编辑修改质检项",type:"warning",closable:!1,"show-icon":""}),s("div",qi,[s("p",null,[e[164]||(e[164]=s("strong",null,"原配置：",-1)),m(p((i=Ce.value)==null?void 0:i.name)+" (关联: "+p(((r=Ce.value)==null?void 0:r.gdb_filename)||"通用配置")+")",1)]),s("p",null,[e[165]||(e[165]=s("strong",null,"当前GDB：",-1)),m(p(vl()),1)])])]),a(De,{model:ne.value,"label-width":"80px",style:{"margin-top":"20px"}},{default:t(()=>[a(z,{label:"副本名称",required:""},{default:t(()=>[a(B,{modelValue:ne.value.name,"onUpdate:modelValue":e[40]||(e[40]=b=>ne.value.name=b),placeholder:"请输入副本名称"},null,8,["modelValue"]),e[166]||(e[166]=s("div",{class:"form-tip"}," 副本将关联到当前GDB文件，可以自由编辑修改 ",-1))]),_:1}),a(z,{label:"副本描述"},{default:t(()=>[a(B,{modelValue:ne.value.description,"onUpdate:modelValue":e[41]||(e[41]=b=>ne.value.description=b),type:"textarea",rows:3,placeholder:"请输入副本描述（可选）"},null,8,["modelValue"])]),_:1}),a(z,{label:"全局容差",required:""},{default:t(()=>[a(n(me),{modelValue:ne.value.globalTolerance,"onUpdate:modelValue":e[42]||(e[42]=b=>ne.value.globalTolerance=b),precision:3,step:.001,min:.001,max:1,style:{width:"200px"}},null,8,["modelValue"]),e[167]||(e[167]=s("span",{class:"tolerance-unit"},"米",-1)),e[168]||(e[168]=s("div",{class:"form-tip"}," 全局容差用于几何精度检查，默认值为0.001米 ",-1))]),_:1})]),_:1},8,["model"])]}),_:1},8,["modelValue"]),a(oe,{modelValue:Ae.value,"onUpdate:modelValue":e[50]||(e[50]=i=>Ae.value=i),title:"选择质检项",width:"90%",top:"5vh"},{footer:t(()=>[a(n(k),{onClick:e[49]||(e[49]=i=>Ae.value=!1)},{default:t(()=>e[175]||(e[175]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:ys,disabled:!f.value},{default:t(()=>e[176]||(e[176]=[m(" 确定选择 ")])),_:1},8,["disabled"])]),default:t(()=>{var i;return[s("div",Gi,[s("div",Ei,[a(Rs,{gutter:20},{default:t(()=>[a(bl,{span:6},{default:t(()=>[a(n(X),{modelValue:Q.value,"onUpdate:modelValue":e[45]||(e[45]=r=>Q.value=r),placeholder:"选择一级分类",onChange:ps,clearable:"",filterable:""},{default:t(()=>[(d(!0),v(G,null,E(ml.value,r=>(d(),C(n(I),{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(bl,{span:6},{default:t(()=>[a(n(X),{modelValue:W.value,"onUpdate:modelValue":e[46]||(e[46]=r=>W.value=r),placeholder:"选择二级分类",onChange:cs,clearable:"",filterable:""},{default:t(()=>[(d(!0),v(G,null,E(xe.value,r=>(d(),C(n(I),{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(bl,{span:6},{default:t(()=>[a(n(X),{modelValue:rl.value,"onUpdate:modelValue":e[47]||(e[47]=r=>rl.value=r),placeholder:"选择对象类型",onChange:ms,clearable:""},{default:t(()=>[(d(!0),v(G,null,E(ls.value,r=>(d(),C(n(I),{key:r,label:r,value:r},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(bl,{span:6},{default:t(()=>[a(B,{modelValue:dl.value,"onUpdate:modelValue":e[48]||(e[48]=r=>dl.value=r),placeholder:"搜索质检项名称",onInput:vs,clearable:""},{prefix:t(()=>[a(n(w),null,{default:t(()=>[a(Da)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),s("div",zi,[a(Z,{data:as.value,onRowClick:fs,"highlight-current-row":"","current-row-key":(i=f.value)==null?void 0:i.id,"row-key":"id",height:"400"},{default:t(()=>[a(y,{prop:"item_name",label:"质检项名称","min-width":"200","show-overflow-tooltip":""}),a(y,{prop:"level1_name",label:"一级分类",width:"120"}),a(y,{prop:"level2_name",label:"二级分类",width:"120"}),a(y,{prop:"object_type",label:"对象类型",width:"120"},{default:t(({row:r})=>[a(S,{type:ye(r.object_type),size:"small"},{default:t(()=>[m(p(r.object_type),1)]),_:2},1032,["type"])]),_:1}),a(y,{prop:"param_type",label:"参数类型",width:"100",align:"center"},{default:t(({row:r})=>[r.param_type&&r.param_type!=="无"?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(r.param_type),1)]),_:2},1024)):(d(),v("span",Ti,"无"))]),_:1}),a(y,{prop:"rule_content",label:"质检规则","min-width":"300","show-overflow-tooltip":""}),a(y,{label:"操作",width:"100",align:"center"},{default:t(({row:r})=>[a(n(k),{size:"small",onClick:Je(b=>Ca(r),["stop"])},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Js))]),_:1}),e[171]||(e[171]=m(" 详情 "))]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","current-row-key"])]),f.value?(d(),v("div",Ri,[a(Fl,null,{default:t(()=>e[172]||(e[172]=[m("选中的质检项")])),_:1}),s("div",ji,[s("div",Mi,[s("h4",null,p(f.value.item_name),1),s("div",Ni,[a(S,{type:ye(f.value.object_type),size:"small"},{default:t(()=>[m(p(f.value.object_type),1)]),_:1},8,["type"]),f.value.param_type&&f.value.param_type!=="无"?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(f.value.param_type),1)]),_:1})):$("",!0)])]),s("div",Pi,[s("p",null,[e[173]||(e[173]=s("strong",null,"质检规则：",-1)),m(p(f.value.rule_content),1)]),f.value.param_name?(d(),v("p",Bi,[e[174]||(e[174]=s("strong",null,"参数说明：",-1)),m(p(f.value.param_name),1)])):$("",!0)])])])):$("",!0)])]}),_:1},8,["modelValue"]),a(oe,{modelValue:Fe.value,"onUpdate:modelValue":e[64]||(e[64]=i=>Fe.value=i),title:"配置质检项",width:"70%"},{footer:t(()=>[a(n(k),{onClick:e[63]||(e[63]=i=>Fe.value=!1)},{default:t(()=>e[185]||(e[185]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:$s,disabled:!wa.value},{default:t(()=>e[186]||(e[186]=[m(" 添加到配置 ")])),_:1},8,["disabled"])]),default:t(()=>[f.value?(d(),v("div",Ai,[s("div",Fi,[s("h4",null,p(f.value.item_name),1),s("p",Oi,p(f.value.rule_content),1),s("div",Hi,[a(S,{type:ye(f.value.object_type),size:"small"},{default:t(()=>[m(p(f.value.object_type),1)]),_:1},8,["type"]),f.value.param_type&&f.value.param_type!=="无"?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(f.value.param_type),1)]),_:1})):$("",!0)])]),a(Fl),a(De,{model:L.value,"label-width":"120px"},{default:t(()=>[f.value.param_type&&f.value.param_type!=="无"?(d(),C(z,{key:0,label:f.value.param_name||"参数值",required:""},{default:t(()=>[f.value.param_type==="数值"?(d(),v("div",Yi,[a(n(me),{modelValue:L.value.paramValue,"onUpdate:modelValue":e[51]||(e[51]=i=>L.value.paramValue=i),min:ae(f.value).min,max:ae(f.value).max,step:ae(f.value).step,precision:ae(f.value).precision,placeholder:ae(f.value).placeholder,style:{width:"200px"}},null,8,["modelValue","min","max","step","precision","placeholder"]),s("span",Xi,p(ae(f.value).unit),1)])):f.value.param_type==="范围"?(d(),v("div",Qi,[a(n(me),{modelValue:L.value.paramRange.min,"onUpdate:modelValue":e[52]||(e[52]=i=>L.value.paramRange.min=i),min:re(f.value).min,max:L.value.paramRange.max||re(f.value).max,placeholder:"最小值",style:{width:"120px"}},null,8,["modelValue","min","max"]),e[177]||(e[177]=s("span",{class:"range-separator"},"至",-1)),a(n(me),{modelValue:L.value.paramRange.max,"onUpdate:modelValue":e[53]||(e[53]=i=>L.value.paramRange.max=i),min:L.value.paramRange.min||re(f.value).min,max:re(f.value).max,placeholder:"最大值",style:{width:"120px"}},null,8,["modelValue","min","max"]),s("span",Wi,p(re(f.value).unit),1)])):f.value.param_type==="单选"?(d(),C(n(X),{key:2,modelValue:L.value.paramValue,"onUpdate:modelValue":e[54]||(e[54]=i=>L.value.paramValue=i),placeholder:"请选择坐标系",filterable:"",style:{width:"300px"}},{default:t(()=>[(d(!0),v(G,null,E(nl.value,i=>(d(),C(n(I),{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):f.value.param_type==="文件上传"?(d(),C(n(Ql),{key:3,action:ll("/api/quality/template-upload"),headers:Ll.value,"on-success":Rt,"on-error":pa,"show-file-list":!0,limit:1,accept:".gdb,.zip",style:{width:"300px"}},{tip:t(()=>e[179]||(e[179]=[s("div",{class:"el-upload__tip"}," 支持 .gdb 或 .zip 格式的模板文件 ",-1)])),default:t(()=>[a(n(k),{type:"primary"},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Wl))]),_:1}),e[178]||(e[178]=m(" 上传模板文件 "))]),_:1})]),_:1},8,["action","headers"])):f.value.param_type==="图层选择"&&f.value.item_name==="工作范围检查"?(d(),C(n(X),{key:4,modelValue:L.value.paramValue,"onUpdate:modelValue":e[55]||(e[55]=i=>L.value.paramValue=i),placeholder:"请选择范围图层（面图层）",style:{width:"300px"},filterable:"",onChange:Ht},{default:t(()=>[(d(!0),v(G,null,E(ma(),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):f.value.param_type==="双图层选择"?(d(),v("div",Ki,[s("div",Zi,[s("label",Ji,p(va(f.value))+"：",1),a(n(X),{modelValue:L.value.sourceLayer,"onUpdate:modelValue":e[56]||(e[56]=i=>L.value.sourceLayer=i),placeholder:`请选择${Nl(f.value)}图层`,style:{width:"250px"},filterable:""},{default:t(()=>[(d(!0),v(G,null,E(_a(f.value),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),s("div",er,[s("label",lr,p(fa(f.value))+"：",1),a(n(X),{modelValue:L.value.targetLayer,"onUpdate:modelValue":e[57]||(e[57]=i=>L.value.targetLayer=i),placeholder:`请选择${Pl(f.value)}图层`,style:{width:"250px"},filterable:""},{default:t(()=>[(d(!0),v(G,null,E(ya(f.value),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),s("div",ar,[s("small",tr,p(ba(f.value,L.value.sourceLayer,L.value.targetLayer)),1)])])):(d(),C(B,{key:6,modelValue:L.value.paramValue,"onUpdate:modelValue":e[58]||(e[58]=i=>L.value.paramValue=i),placeholder:"请输入参数值",style:{width:"300px"}},null,8,["modelValue"])),s("div",sr,[m(p(f.value.param_name)+" ",1),f.value.item_name==="工作范围检查"?(d(),v("span",nr," - 选择面图层作为范围边界 ")):$("",!0),f.value.param_type==="双图层选择"?(d(),v("span",or," - 交互检查：选择两个图层进行关系检查 ")):$("",!0)])]),_:1},8,["label"])):$("",!0),ga(f.value)?(d(),C(z,{key:1,label:"适用图层",required:""},{default:t(()=>[Pt()?(d(),v("div",ur,[a(qa,{size:"small"},{default:t(()=>[a(n(k),{onClick:Bt,disabled:Ue()},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(wl))]),_:1}),e[180]||(e[180]=m(" 全选 "))]),_:1},8,["disabled"]),jl("点要素")?(d(),C(n(k),{key:0,onClick:e[59]||(e[59]=i=>Ml("点要素")),disabled:Ue()},{default:t(()=>e[181]||(e[181]=[m(" 选择所有点图层 ")])),_:1},8,["disabled"])):$("",!0),jl("线要素")?(d(),C(n(k),{key:1,onClick:e[60]||(e[60]=i=>Ml("线要素")),disabled:Ue()},{default:t(()=>e[182]||(e[182]=[m(" 选择所有线图层 ")])),_:1},8,["disabled"])):$("",!0),jl("面要素")?(d(),C(n(k),{key:2,onClick:e[61]||(e[61]=i=>Ml("面要素")),disabled:Ue()},{default:t(()=>e[183]||(e[183]=[m(" 选择所有面图层 ")])),_:1},8,["disabled"])):$("",!0),a(n(k),{onClick:At,disabled:Ue()},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(en))]),_:1}),e[184]||(e[184]=m(" 清空 "))]),_:1},8,["disabled"])]),_:1})])):$("",!0),a(n(X),{modelValue:L.value.selectedLayers,"onUpdate:modelValue":e[62]||(e[62]=i=>L.value.selectedLayers=i),placeholder:"请选择适用的图层",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%","margin-top":"8px"},disabled:Ue(),onChange:Ft},{default:t(()=>[a(Ea,{label:"汇总选项"},{default:t(()=>[(d(!0),v(G,null,E(ca(),i=>(d(),C(n(I),{key:i.value,label:i.label,value:i.value,disabled:i.disabled},{default:t(()=>[s("div",ir,[s("span",rr,p(i.label),1),a(S,{size:"small",type:"info"},{default:t(()=>[m(p(i.count)+"个 ",1)]),_:2},1024)])]),_:2},1032,["label","value","disabled"]))),128))]),_:1}),a(Ea,{label:"具体图层"},{default:t(()=>[(d(!0),v(G,null,E(he(),i=>(d(),C(n(I),{key:i.name,label:Xt(i),value:i.name,disabled:!yl(i,f.value.object_type)},{default:t(()=>[s("div",dr,[s("span",pr,p(i.name),1),a(S,{size:"small",type:gs(i.type)},{default:t(()=>[m(p(i.type),1)]),_:2},1032,["type"])])]),_:2},1032,["label","value","disabled"]))),128))]),_:1})]),_:1},8,["modelValue","disabled"]),s("div",cr,p(Mt()),1)]),_:1})):$("",!0)]),_:1},8,["model"])])):$("",!0)]),_:1},8,["modelValue"]),a(oe,{modelValue:Oe.value,"onUpdate:modelValue":e[75]||(e[75]=i=>Oe.value=i),title:"编辑质检项配置",width:"70%"},{footer:t(()=>[a(n(k),{onClick:e[74]||(e[74]=i=>Oe.value=!1)},{default:t(()=>e[195]||(e[195]=[m("取消")])),_:1}),a(n(k),{type:"primary",onClick:xs,disabled:!Va.value},{default:t(()=>e[196]||(e[196]=[m(" 保存修改 ")])),_:1},8,["disabled"])]),default:t(()=>[h.value?(d(),v("div",mr,[s("div",vr,[s("h4",null,p(h.value.item_name),1),s("p",fr,p(h.value.rule_content),1),s("div",_r,[a(S,{type:ye(h.value.object_type),size:"small"},{default:t(()=>[m(p(h.value.object_type),1)]),_:1},8,["type"]),h.value.param_type&&h.value.param_type!=="无"?(d(),C(S,{key:0,type:"info",size:"small"},{default:t(()=>[m(p(h.value.param_type),1)]),_:1})):$("",!0)])]),a(Fl),a(De,{model:x.value,"label-width":"120px"},{default:t(()=>[ga(h.value)?(d(),C(z,{key:0,label:"适用图层",required:""},{default:t(()=>[s("div",yr,[a(n(k),{size:"small",onClick:hs},{default:t(()=>e[187]||(e[187]=[m("全选")])),_:1}),a(n(k),{size:"small",onClick:ws},{default:t(()=>e[188]||(e[188]=[m("点图层")])),_:1}),a(n(k),{size:"small",onClick:ks},{default:t(()=>e[189]||(e[189]=[m("线图层")])),_:1}),a(n(k),{size:"small",onClick:Vs},{default:t(()=>e[190]||(e[190]=[m("面图层")])),_:1}),a(n(k),{size:"small",onClick:Cs},{default:t(()=>e[191]||(e[191]=[m("清空")])),_:1})]),a(n(X),{modelValue:x.value.selectedLayers,"onUpdate:modelValue":e[65]||(e[65]=i=>x.value.selectedLayers=i),placeholder:"请选择适用的图层",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%","margin-top":"8px"}},{default:t(()=>[a(za,{label:"汇总选项"},{default:t(()=>[(d(!0),v(G,null,E(ca(),i=>(d(),C(n(I),{key:i.value,label:`${i.label} (${i.count}个)`,value:i.value,disabled:i.disabled,class:"summary-option"},null,8,["label","value","disabled"]))),128))]),_:1}),a(za,{label:"具体图层"},{default:t(()=>[(d(!0),v(G,null,E(Ye(),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1})]),_:1},8,["modelValue"]),s("div",gr,p(Nt()),1)]),_:1})):$("",!0),h.value.param_type&&h.value.param_type!=="无"?(d(),C(z,{key:1,label:h.value.param_name||"参数值",required:""},{default:t(()=>[h.value.param_type==="数值"?(d(),v("div",br,[a(n(me),{modelValue:x.value.paramValue,"onUpdate:modelValue":e[66]||(e[66]=i=>x.value.paramValue=i),min:ae(h.value).min,max:ae(h.value).max,step:ae(h.value).step,precision:ae(h.value).precision,placeholder:ae(h.value).placeholder,style:{width:"200px"}},null,8,["modelValue","min","max","step","precision","placeholder"]),s("span",hr,p(ae(h.value).unit),1)])):h.value.param_type==="范围"?(d(),v("div",wr,[a(n(me),{modelValue:x.value.paramRange.min,"onUpdate:modelValue":e[67]||(e[67]=i=>x.value.paramRange.min=i),min:re(h.value).min,max:x.value.paramRange.max||re(h.value).max,placeholder:"最小值",style:{width:"120px"}},null,8,["modelValue","min","max"]),e[192]||(e[192]=s("span",{class:"range-separator"},"至",-1)),a(n(me),{modelValue:x.value.paramRange.max,"onUpdate:modelValue":e[68]||(e[68]=i=>x.value.paramRange.max=i),min:x.value.paramRange.min||re(h.value).min,max:re(h.value).max,placeholder:"最大值",style:{width:"120px"}},null,8,["modelValue","min","max"]),s("span",kr,p(re(h.value).unit),1)])):h.value.param_type==="单选"?(d(),C(n(X),{key:2,modelValue:x.value.paramValue,"onUpdate:modelValue":e[69]||(e[69]=i=>x.value.paramValue=i),placeholder:"请选择坐标系",filterable:"",style:{width:"300px"}},{default:t(()=>[(d(!0),v(G,null,E(nl.value,i=>(d(),C(n(I),{key:i.value,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):h.value.param_type==="文件上传"?(d(),C(n(Ql),{key:3,action:ll("/api/quality/template-upload"),headers:Ll.value,"on-success":jt,"on-error":pa,"show-file-list":!0,limit:1,accept:".gdb,.zip",style:{width:"300px"}},{tip:t(()=>e[194]||(e[194]=[s("div",{class:"el-upload__tip"}," 支持 .gdb 或 .zip 格式的模板文件 ",-1)])),default:t(()=>[a(n(k),{type:"primary"},{default:t(()=>[a(n(w),null,{default:t(()=>[a(n(Wl))]),_:1}),e[193]||(e[193]=m(" 上传模板文件 "))]),_:1})]),_:1},8,["action","headers"])):h.value.param_type==="图层选择"&&h.value.item_name==="工作范围检查"?(d(),C(n(X),{key:4,modelValue:x.value.paramValue,"onUpdate:modelValue":e[70]||(e[70]=i=>x.value.paramValue=i),placeholder:"请选择范围图层（面图层）",style:{width:"300px"},filterable:"",onChange:Ls},{default:t(()=>[(d(!0),v(G,null,E(ma(),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):h.value.param_type==="双图层选择"?(d(),v("div",Vr,[s("div",Cr,[s("label",Lr,p(va(h.value))+"：",1),a(n(X),{modelValue:x.value.sourceLayer,"onUpdate:modelValue":e[71]||(e[71]=i=>x.value.sourceLayer=i),placeholder:`请选择${Nl(h.value)}图层`,style:{width:"250px"},filterable:""},{default:t(()=>[(d(!0),v(G,null,E(_a(h.value),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),s("div",xr,[s("label",$r,p(fa(h.value))+"：",1),a(n(X),{modelValue:x.value.targetLayer,"onUpdate:modelValue":e[72]||(e[72]=i=>x.value.targetLayer=i),placeholder:`请选择${Pl(h.value)}图层`,style:{width:"250px"},filterable:""},{default:t(()=>[(d(!0),v(G,null,E(ya(h.value),i=>(d(),C(n(I),{key:i.name,label:`${i.name} (${i.type})`,value:i.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),s("div",Ur,[s("small",Sr,p(ba(h.value,x.value.sourceLayer,x.value.targetLayer)),1)])])):(d(),C(B,{key:6,modelValue:x.value.paramValue,"onUpdate:modelValue":e[73]||(e[73]=i=>x.value.paramValue=i),placeholder:"请输入参数值",style:{width:"300px"}},null,8,["modelValue"])),s("div",Dr,[m(p(h.value.param_name)+" ",1),h.value.item_name==="工作范围检查"?(d(),v("span",Ir," - 选择面图层作为范围边界 ")):$("",!0),h.value.param_type==="双图层选择"?(d(),v("span",qr," - 交互检查：选择两个图层进行关系检查 ")):$("",!0)])]),_:1},8,["label"])):$("",!0)]),_:1},8,["model"])])):$("",!0)]),_:1},8,["modelValue"])])}}}),jr=ln(Er,[["__scopeId","data-v-45af152c"]]);export{jr as default};

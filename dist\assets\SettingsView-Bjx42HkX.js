import{d as Pt,r as u,N as Y,u as It,O as ee,M as te,o as ae,a as Nt,c as w,b as l,e as a,H as se,I as oe,f as v,n as V,w as o,C as I,i as f,E as r,ai as Et,F as E,m,av as Ne,A as _,B as $,R as Ee,P as Dt,Q as Rt,t as De,s as Re,y as le,x as Ue,aw as Ut,ar as zt,W as Lt,ax as ze,J as Ot,ap as Le,v as Oe,a2 as qt,z as Wt,ab as Bt,an as Jt,T as ne,l as p,_ as Ft}from"./index-E0SsINqw.js";const jt={class:"settings"},At={class:"settings-nav"},Yt={class:"nav-list"},$t=["onClick"],Ht={class:"nav-item-text"},Gt={class:"content-wrapper"},Qt={key:0,class:"loading-container"},Xt={key:1},Kt={class:"section-header"},Zt={class:"header-left"},ea={class:"task-list"},ta={key:0,class:"empty-tasks"},aa={class:"section-header"},sa={class:"header-left"},oa={style:{width:"100%"}},la={class:"form-actions"},na={class:"section-header"},ia={class:"header-left"},ra={class:"port-control"},da={key:0,class:"port-status"},ua={class:"form-actions"},ca={class:"section-header"},ma={class:"header-left"},pa={class:"permission-section"},ga={class:"subsection-title"},va={class:"permission-item"},fa={class:"permission-control"},_a={class:"permission-description"},ha={key:0,class:"warning-text"},ya={key:1,class:"info-text"},wa={class:"section-header"},ba={class:"header-left"},ka={class:"theme-content"},Ca={class:"theme-grid"},xa=["onClick"],Ta={class:"theme-info"},Va={class:"theme-name"},Sa={class:"theme-desc"},Ma={class:"theme-actions"},Pa={class:"section-header"},Ia={class:"header-left"},Na={key:0,class:"progress-control"},Ea={class:"form-actions"},Da={class:"section-header"},Ra={class:"header-left"},Ua={class:"register-control"},za={class:"status-text"},La={class:"form-item-tip"},Oa={class:"restart-header"},qa=Pt({__name:"SettingsView",setup(Wa){const b=u("monitor"),H=u(!1),S=u(""),qe=t=>({monitor:Ne,data:Bt,config:De,permission:Ue,theme:ze,ui:Le,user:Oe})[t]||Jt,We=[{key:"monitor",label:"实时任务状态",description:"监控任务状态"},{key:"data",label:"运行记录导出",description:"导出运行记录"},{key:"config",label:"服务器参数配置",description:"服务器参数配置"},{key:"permission",label:"权限配置",description:"系统权限配置"},{key:"theme",label:"主题配置",description:"自定义界面主题"},{key:"ui",label:"导航菜单配置",description:"导航菜单配置"},{key:"user",label:"用户注册控制",description:"用户账户管理"}],d=Y({limits:1,server_host:"127.0.0.1",server_port:9997,mode:"offline"}),i=Y({visibleItems:{home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},itemModes:{home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},itemProgress:{home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:75,settings:75,about:75}}),Be=[{key:"home",label:"首页",description:"系统首页和仪表板"},{key:"tools",label:"我的工具",description:"个人工具管理"},{key:"market",label:"工具市场",description:"浏览和申请工具"},{key:"quality",label:"数据质检",description:"数据质量检查平台"},{key:"coordinate",label:"坐标转换",description:"坐标系转换工具"},{key:"cadtogis",label:"CAD转GIS",description:"CAD文件转换工具"},{key:"requirement",label:"需求提交",description:"功能需求申请"},{key:"layerPreview",label:"图层预览",description:"地理数据预览"},{key:"userManagement",label:"用户管理",description:"用户账户管理（管理员）"},{key:"settings",label:"设置",description:"系统设置（管理员）"},{key:"about",label:"关于",description:"系统信息"}],M=u(null),G=u(null),z=u([]),L=u(!1),ie=u(!1),q=u(!1),Je=u();u();const W=u(!1),B=u("."),re=u(!1),D=Y({allowExternalCoordinate:!1}),de=u(!1),R=u(!1),N=u(!1),Te=It(),k=u(!1),Q=u(!0),ue=u(!0),ce=u(!0),me=u(!0),pe=u([]),Fe=u({running_count:0,pending_count:0}),ge=u(!1);let O=null;const y=Y({tableName:"task_records_all",dateRange:null}),ve=u(!1),je=[{text:"最近一周",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*7),[e,t]}},{text:"最近一个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*30),[e,t]}},{text:"最近三个月",value:()=>{const t=new Date,e=new Date;return e.setTime(e.getTime()-3600*1e3*24*90),[e,t]}}];let J=null,fe=null;ee(()=>d.server_port,()=>{fe&&clearTimeout(fe),fe=setTimeout(()=>{R.value=!1,N.value=!1},300)},{flush:"post"}),ee(()=>b.value,t=>{});const Ae=async()=>{ue.value=!0;try{const t=await f.post("/api/settings/get",{},{timeout:1e4});t.data.success?(Object.assign(d,t.data.data),M.value=JSON.parse(JSON.stringify(t.data.data)),d.mode==="online"&&Ve().catch(console.error)):r.error(t.data.message||"获取设置失败")}catch(t){console.error("获取设置失败:",t),r.error("获取设置失败")}finally{ue.value=!1,ye()}},Ve=async()=>{try{const t=await f.post("/api/settings/ips",{},{timeout:5e3});t.data.success&&Array.isArray(t.data.data)?(z.value=t.data.data,d.mode==="online"&&!z.value.includes(d.server_host)&&(d.server_host=z.value[0]||"127.0.0.1")):z.value=["127.0.0.1"]}catch(t){console.error("获取IP列表失败:",t),z.value=["127.0.0.1"]}},Ye=async t=>{t==="offline"?d.server_host="127.0.0.1":setTimeout(()=>{Ve().catch(console.error)},0)},_e=te(()=>M.value&&d.server_port!==M.value.server_port),Se=te(()=>M.value?d.limits!==M.value.limits||d.server_host!==M.value.server_host||d.server_port!==M.value.server_port||d.mode!==M.value.mode:!1),$e=te(()=>R.value?N.value?"端口可用":"端口已被占用，请更换其他端口":"请先检测端口占用情况"),He=te(()=>Se.value?_e.value?R.value&&N.value&&!L.value:!L.value:!1),Ge=async()=>{if(!Se.value){r.info("未检测到任何更改，无需保存");return}if(_e.value&&(!R.value||!N.value)){r.warning("端口更改后必须检测端口占用，并确保端口可用后才能保存！");return}ne.confirm("确定要保存当前设置吗？保存后需重启服务器才能生效。","确认保存",{confirmButtonText:"保存",cancelButtonText:"取消",type:"warning"}).then(async()=>{L.value=!0;try{const t=await f.post("/api/settings/set",{...d});t.data.success?(r.success("设置已保存，将在重启服务器后生效"),M.value=JSON.parse(JSON.stringify(d))):r.error(t.data.message||"保存失败")}catch{r.error("保存失败")}finally{L.value=!1}})},Qe=async()=>{ne.confirm("确认要重启服务器吗？","确认重启",{confirmButtonText:"重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{q.value=!0;try{const t=await f.post("/api/settings/restart",{});t.data.success?(r.success("服务器重启命令已发送"),W.value=!0,Me(),Pe()):t.data.needForce?ne.confirm(t.data.message||"当前有任务正在进行，是否强制重启？","警告",{confirmButtonText:"强制重启",cancelButtonText:"取消",type:"warning"}).then(async()=>{q.value=!0;try{const e=await f.post("/api/settings/restart",{force:!0});e.data.success?(r.success("服务器重启命令已发送"),W.value=!0,Me(),Pe()):r.error(e.data.message||"重启失败")}finally{q.value=!1}}):r.error(t.data.message||"重启失败")}catch{r.error("重启失败")}finally{q.value=!1}})};function Me(){B.value=".",J&&clearInterval(J),J=setInterval(()=>{B.value=B.value.length<3?B.value+".":"."},500)}function Pe(){const t=window.location.protocol;let e=d.server_host;e==="0.0.0.0"&&(e=window.location.hostname||"127.0.0.1");const n=d.server_port;setTimeout(()=>{W.value=!1,Te.logout(),window.location.href=`${t}//${e}:${n}/`},5e3)}const Xe=async()=>{re.value=!0;try{const t=await f.post("/api/settings/check_port",{port:d.server_port});await new Promise(e=>setTimeout(e,200)),R.value=!0,t.data.success?N.value=!0:N.value=!1}catch{await new Promise(e=>setTimeout(e,200)),R.value=!0,N.value=!1}finally{re.value=!1}},Ke=async()=>{try{const t=await f.get("/api/settings/coordinate-permission");t.data.success&&(D.allowExternalCoordinate=t.data.data.allow_external_access)}catch(t){console.error("加载坐标转换权限设置失败:",t),r.error("加载权限设置失败")}},Ze=async t=>{var e;de.value=!0;try{const n=await f.put("/api/settings/coordinate-permission",{allow_external_access:t},{headers:{"X-Username":(e=Te.user)==null?void 0:e.username}});n.data.success?r.success("坐标转换权限设置更新成功"):(r.error(n.data.message||"更新权限设置失败"),D.allowExternalCoordinate=!t)}catch(n){console.error("更新坐标转换权限设置失败:",n),r.error("更新权限设置失败"),D.allowExternalCoordinate=!t}finally{de.value=!1}},et=async()=>{ce.value=!0;try{const t=await f.post("/api/settings/get-nav-settings",{},{timeout:8e3});if(t.data.success){const e={...t.data.data.visibleItems,userManagement:!0,settings:!0};Object.assign(i.visibleItems,e),t.data.data.itemModes&&Object.assign(i.itemModes,t.data.data.itemModes),t.data.data.itemProgress&&Object.assign(i.itemProgress,t.data.data.itemProgress),G.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}else{const e={...i.visibleItems,userManagement:!0,settings:!0};G.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}}catch(t){console.error("获取导航栏设置失败:",t);const e={...i.visibleItems,userManagement:!0,settings:!0};G.value=JSON.parse(JSON.stringify({visibleItems:e,itemModes:i.itemModes,itemProgress:i.itemProgress}))}finally{ce.value=!1,ye()}},tt=async()=>{ie.value=!0;try{const t={visibleItems:{...i.visibleItems,userManagement:!0,settings:!0},itemModes:{...i.itemModes,userManagement:"production",settings:"production"},itemProgress:{...i.itemProgress,userManagement:100,settings:100}},e=await f.post("/api/settings/set-nav-settings",t);e.data.success?(r.success("导航栏设置已保存"),i.visibleItems=t.visibleItems,i.itemModes=t.itemModes,i.itemProgress=t.itemProgress,G.value=JSON.parse(JSON.stringify(t)),window.dispatchEvent(new CustomEvent("navSettingsChanged",{detail:t}))):r.error(e.data.message||"保存导航栏设置失败")}catch{r.error("保存导航栏设置失败")}finally{ie.value=!1}},at=()=>{ne.confirm("确定要重置导航栏设置为默认值吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{i.visibleItems={home:!0,tools:!0,market:!0,quality:!0,coordinate:!0,cadtogis:!0,requirement:!0,layerPreview:!0,userManagement:!0,settings:!0,about:!0},i.itemModes={home:"production",tools:"production",market:"production",quality:"production",coordinate:"production",cadtogis:"production",requirement:"production",layerPreview:"production",userManagement:"production",settings:"production",about:"production"},i.itemProgress={home:75,tools:75,market:75,quality:75,coordinate:75,cadtogis:75,requirement:75,layerPreview:75,userManagement:100,settings:100,about:75},r.success("已重置为默认设置")})},st=async()=>{me.value=!0;try{const t=await f.post("/api/get_allow_register",{},{timeout:5e3});t.data&&typeof t.data.allow_register=="boolean"?k.value=t.data.allow_register:t.data&&typeof t.data.allow_register=="string"?k.value=t.data.allow_register==="true":k.value=!1}catch(t){console.error("获取注册开关状态失败:",t),k.value=!1}finally{me.value=!1,ye()}},ot=async t=>{try{await f.post("/api/set_allow_register",{allow_register:t})}catch{k.value=!t}},he=async()=>{ge.value=!0;try{const t=await f.post("/api/task/current/list",{});t.data.success?(pe.value=t.data.data.tasks||[],Fe.value={running_count:t.data.data.running_count||0,pending_count:t.data.data.pending_count||0}):r.error(t.data.message||"获取任务列表失败")}catch(t){console.error("获取任务列表失败:",t),r.error("获取任务列表失败")}finally{ge.value=!1}},lt=t=>{if(!t)return"";try{return new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch{return t}},nt=()=>{O&&clearInterval(O),he(),O=setInterval(()=>{he()},5e3)},it=()=>{O&&(clearInterval(O),O=null)},rt=async()=>{var t,e;if(!y.tableName){r.warning("请选择要导出的数据表");return}ve.value=!0;try{const n={table_name:y.tableName,date_range:y.dateRange},T=await f.post("/api/task/export-excel",n,{responseType:"blob"}),h=new Blob([T.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),X=window.URL.createObjectURL(h),g=document.createElement("a");g.href=X;const be=new Date().toISOString().slice(0,19).replace(/:/g,"-");g.download=`${y.tableName}_${be}.xlsx`,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(X),r.success("导出成功")}catch(n){console.error("导出失败:",n),(e=(t=n.response)==null?void 0:t.data)!=null&&e.message?r.error(n.response.data.message):r.error("导出失败")}finally{ve.value=!1}},dt=()=>{y.tableName="task_records_all",y.dateRange=null},ye=()=>{Q.value=ue.value||ce.value||me.value},ut=async()=>{Q.value=!0;const t=[Ae().catch(console.error),et().catch(console.error),st().catch(console.error)];try{await Promise.allSettled(t)}catch(e){console.error("加载数据时发生错误:",e)}setTimeout(()=>{Q.value=!1},100)};ae(()=>{ut(),Ke(),nt()}),Nt(()=>{it(),J&&clearInterval(J)});const ct=async t=>{H.value||t===b.value||(H.value=!0,S.value=b.value,b.value=t,await new Promise(e=>setTimeout(e,300)),S.value="",H.value=!1)},we=u([{name:"default",displayName:"默认",description:"经典蓝紫渐变",gradient:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},{name:"blue",displayName:"蓝色",description:"专业商务风格",gradient:"linear-gradient(135deg, #4299e1 0%, #3182ce 100%)"},{name:"purple",displayName:"紫色",description:"优雅现代风格",gradient:"linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)"},{name:"green",displayName:"绿色",description:"清新自然风格",gradient:"linear-gradient(135deg, #48bb78 0%, #38a169 100%)"},{name:"orange",displayName:"橙色",description:"活力温暖风格",gradient:"linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)"},{name:"red",displayName:"红色",description:"热情活力风格",gradient:"linear-gradient(135deg, #f56565 0%, #e53e3e 100%)"},{name:"pink",displayName:"粉色",description:"温柔浪漫风格",gradient:"linear-gradient(135deg, #f687b3 0%, #ed64a6 100%)"},{name:"indigo",displayName:"靛蓝",description:"科技感风格",gradient:"linear-gradient(135deg, #63b3ed 0%, #4299e1 100%)"},{name:"brown",displayName:"棕色",description:"复古怀旧风格",gradient:"linear-gradient(135deg, #795548 0%, #6b463f 100%)"},{name:"gray",displayName:"灰色",description:"中性稳重风格",gradient:"linear-gradient(135deg, #4a5568 0%, #2d3748 100%)"},{name:"dark",displayName:"深色",description:"适合暗光环境",gradient:"linear-gradient(135deg, #1a202c 0%, #2d3748 100%)"},{name:"light",displayName:"浅色",description:"适合明亮环境",gradient:"linear-gradient(135deg, #f3f3f3 0%, #e0e0e0 100%)"}]),C=u(localStorage.getItem("currentTheme")||"default"),mt=t=>{C.value=t},F=t=>{const e=document.querySelector("#app");e&&(e.classList.forEach(n=>{n.startsWith("theme-")&&n!=="theme-initialized"&&e.classList.remove(n)}),e.classList.add(`theme-${t}`))},pt=async()=>{try{window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:C.value}})),localStorage.setItem("currentTheme",C.value);const t=await f.post("/api/set_default_theme",{theme:C.value});t.data&&t.data.success?r.success("主题已应用并保存"):r.warning("主题已应用，但保存失败")}catch{r.warning("主题已应用，但保存失败")}},gt=()=>{C.value="default",window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}})),localStorage.setItem("currentTheme","default");const t=document.querySelector("#app");t&&(t.classList.forEach(e=>{e.startsWith("theme-")&&e!=="theme-initialized"&&t.classList.remove(e)}),t.classList.add("theme-default")),r.success("已重置为默认主题")},vt=async()=>{try{const t=localStorage.getItem("currentTheme");t?we.value.some(T=>T.name===t)?(C.value=t,F(t),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:t}}))):(C.value="default",F("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}}))):(C.value="default",F("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}})));const e=await f.post("/api/get_default_theme");if(e.data&&e.data.success&&e.data.theme){const n=e.data.theme;we.value.some(h=>h.name===n)&&!t&&(C.value=n,F(n),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:n}})))}}catch(t){console.error("获取默认主题失败:",t),C.value="default",F("default"),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:"default"}}))}};ae(()=>{vt()});const U=u(!1),ft=async()=>{try{const t=await f.post("/api/get_fme_visibility");t.data&&typeof t.data.fme_visibility=="boolean"?U.value=t.data.fme_visibility:t.data&&typeof t.data.fme_visibility=="string"?U.value=t.data.fme_visibility==="true":U.value=!1}catch{U.value=!1}},_t=async t=>{try{await f.post("/api/set_fme_visibility",{fme_visibility:t}),r.success("设置已保存，重启后端服务后生效")}catch{r.error("保存失败"),U.value=!t}};ae(()=>{ft()});const ht=(t,e)=>{i.itemProgress[t]=Math.max(0,Math.min(100,e))};ee(()=>i.itemModes,t=>{Object.entries(t).forEach(([e,n])=>{n==="production"?i.itemProgress[e]=100:n==="development"&&i.itemProgress[e]===100&&(i.itemProgress[e]=75)})},{deep:!0});const c=Y({data:[],columns:[{prop:"task_id",label:"任务ID",minWidth:120},{prop:"tool_name",label:"工具名称",minWidth:120},{prop:"project",label:"项目",minWidth:100},{prop:"submitter",label:"提交者",minWidth:100},{prop:"submit_time",label:"提交时间",minWidth:160},{prop:"task_name",label:"任务名称",minWidth:120},{prop:"status",label:"状态",minWidth:80},{prop:"time_consuming",label:"运行时间(秒)",minWidth:100},{prop:"file_size",label:"文件大小",minWidth:100},{prop:"file_name",label:"文件名",minWidth:150},{prop:"up_nums",label:"文件数量",minWidth:80}],loading:!1,pagination:{total:0,page:1,page_size:10,total_pages:1}}),j=async()=>{c.loading=!0;try{const t={table_name:y.tableName,date_range:y.dateRange,page:c.pagination.page,page_size:c.pagination.page_size},e=await f.post("/api/task/records",t);if(e.data.success){if(c.data=e.data.data.records,c.pagination=e.data.data.pagination,e.data.data.columns){const n={任务ID:"task_id",工具名称:"tool_name",项目:"project",提交者:"submitter",提交时间:"submit_time",任务名称:"task_name",状态:"status","运行时间(秒)":"time_consuming",文件大小:"file_size",文件名:"file_name",文件数量:"up_nums"};c.columns=e.data.data.columns.map(T=>({label:T,prop:n[T]||T,minWidth:100}))}}else c.data=[],c.pagination.total=0}catch{c.data=[],c.pagination.total=0}finally{c.loading=!1}},yt=t=>{c.pagination.page=t,j()},wt=t=>{c.pagination.page_size=t,c.pagination.page=1,j()},bt=()=>{c.pagination.page=1,j()};return ee(()=>y.tableName,()=>{c.pagination.page=1,j()}),ae(()=>{j()}),(t,e)=>{const n=v("el-icon"),T=v("el-skeleton"),h=v("el-button"),X=v("el-empty"),g=v("el-table-column"),be=v("el-tag"),ke=v("el-table"),A=v("el-option"),Ce=v("el-select"),x=v("el-form-item"),kt=v("el-date-picker"),Ct=v("el-pagination"),K=v("el-form"),xe=v("el-input-number"),Ie=v("el-radio"),xt=v("el-radio-group"),Tt=v("el-input"),Vt=v("el-alert"),Z=v("el-switch"),St=v("el-dialog"),Mt=Rt("loading");return p(),w("div",jt,[l("div",At,[l("div",Yt,[(p(),w(se,null,oe(We,s=>l("div",{key:s.key,class:V(["nav-item",{active:b.value===s.key,disabled:H.value}]),onClick:P=>ct(s.key)},[a(n,null,{default:o(()=>[(p(),I(Et(qe(s.key))))]),_:2},1024),l("span",Ht,E(s.label),1)],10,$t)),64))])]),l("div",Gt,[Q.value?(p(),w("div",Qt,[a(T,{rows:6,animated:""})])):(p(),w("div",Xt,[l("div",{class:V(["settings-section",{active:b.value==="monitor",prev:S.value==="monitor"}])},[l("div",Kt,[l("div",Zt,[a(n,{class:"section-icon"},{default:o(()=>[a(m(Ne))]),_:1}),e[11]||(e[11]=l("span",null,"实时任务状态",-1))]),a(h,{type:"primary",size:"small",onClick:he,loading:ge.value},{default:o(()=>[a(n,null,{default:o(()=>[a(m($))]),_:1}),e[12]||(e[12]=_(" 刷新 "))]),_:1},8,["loading"])]),l("div",ea,[pe.value.length===0?(p(),w("div",ta,[a(X,{description:"暂无任务"})])):(p(),I(ke,{key:1,data:pe.value,border:"",style:{width:"100%"}},{default:o(()=>[a(g,{prop:"task_id",label:"任务ID",width:"180"}),a(g,{label:"状态",width:"100"},{default:o(s=>[a(be,{type:s.row.status==="running"?"success":"warning",size:"small"},{default:o(()=>[_(E(s.row.status==="running"?"运行中":"等待中"),1)]),_:2},1032,["type"])]),_:1}),a(g,{prop:"tool_name",label:"工具名称","min-width":"120"}),a(g,{prop:"submitter",label:"提交者",width:"100"}),a(g,{prop:"project",label:"项目",width:"120"}),a(g,{prop:"file_name",label:"文件名","min-width":"150","show-overflow-tooltip":""}),a(g,{prop:"up_nums",label:"文件数",width:"80",align:"center"}),a(g,{label:"提交时间",width:"160"},{default:o(s=>[_(E(lt(s.row.submit_time)),1)]),_:1})]),_:1},8,["data"]))])],2),l("div",{class:V(["settings-section",{active:b.value==="data",prev:S.value==="data"}])},[l("div",aa,[l("div",sa,[a(n,{class:"section-icon"},{default:o(()=>[a(m(Ee))]),_:1}),e[13]||(e[13]=l("span",null,"运行记录导出",-1))])]),a(K,{"label-width":"120px",class:"export-form",style:{width:"100%","max-width":"none",background:"transparent",padding:"0"}},{default:o(()=>[a(x,{label:"选择数据表"},{default:o(()=>[a(Ce,{modelValue:y.tableName,"onUpdate:modelValue":e[0]||(e[0]=s=>y.tableName=s),placeholder:"请选择要导出的数据表",style:{"max-width":"240px",width:"100%"}},{default:o(()=>[a(A,{label:"任务记录总表",value:"task_records_all"}),a(A,{label:"当前任务记录",value:"task_records"})]),_:1},8,["modelValue"])]),_:1}),a(x,{label:"日期范围"},{default:o(()=>[a(kt,{modelValue:y.dateRange,"onUpdate:modelValue":e[1]||(e[1]=s=>y.dateRange=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",shortcuts:je,onChange:bt,style:{"max-width":"320px",width:"100%"}},null,8,["modelValue"])]),_:1}),a(x,{"label-width":"0",style:{width:"100%","margin-bottom":"0"}},{default:o(()=>[l("div",oa,[Dt((p(),I(ke,{data:c.data,border:"",style:{width:"100%"},"show-overflow-tooltip":!0},{default:o(()=>[(p(!0),w(se,null,oe(c.columns,s=>(p(),I(g,{key:s.prop,prop:s.prop,label:s.label,"min-width":s.minWidth||100,"show-overflow-tooltip":""},null,8,["prop","label","min-width"]))),128))]),_:1},8,["data"])),[[Mt,c.loading]]),a(Ct,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:c.pagination.total,"page-size":c.pagination.page_size,"current-page":c.pagination.page,"page-sizes":[10,20,50,100],onSizeChange:wt,onCurrentChange:yt,style:{"margin-top":"8px"}},null,8,["total","page-size","current-page"])])]),_:1}),a(x,{"label-width":"0"},{default:o(()=>[l("div",la,[a(h,{type:"primary",onClick:rt,loading:ve.value},{default:o(()=>[a(n,null,{default:o(()=>[a(m(Ee))]),_:1}),e[14]||(e[14]=_(" 导出Excel "))]),_:1},8,["loading"]),a(h,{onClick:dt},{default:o(()=>[a(n,null,{default:o(()=>[a(m($))]),_:1}),e[15]||(e[15]=_(" 重置 "))]),_:1})])]),_:1})]),_:1})],2),l("div",{class:V(["settings-section",{active:b.value==="config",prev:S.value==="config"}])},[l("div",na,[l("div",ia,[a(n,{class:"section-icon"},{default:o(()=>[a(m(De))]),_:1}),e[16]||(e[16]=l("span",null,"服务器参数配置",-1))])]),a(K,{"label-width":"120px",model:d,ref_key:"formRef",ref:Je,class:"settings-form"},{default:o(()=>[a(x,{label:"最大并发任务数"},{default:o(()=>[a(xe,{modelValue:d.limits,"onUpdate:modelValue":e[2]||(e[2]=s=>d.limits=s),min:1,max:8},null,8,["modelValue"])]),_:1}),a(x,{label:"运行模式"},{default:o(()=>[a(xt,{modelValue:d.mode,"onUpdate:modelValue":e[3]||(e[3]=s=>d.mode=s),onChange:Ye},{default:o(()=>[a(Ie,{label:"offline"},{default:o(()=>e[17]||(e[17]=[_("离线（本地127.0.0.1）")])),_:1}),a(Ie,{label:"online"},{default:o(()=>e[18]||(e[18]=[_("在线（局域网/公网）")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(x,{label:"IP"},{default:o(()=>[d.mode==="online"?(p(),I(Ce,{key:0,modelValue:d.server_host,"onUpdate:modelValue":e[4]||(e[4]=s=>d.server_host=s),filterable:"",placeholder:"请选择IP"},{default:o(()=>[(p(!0),w(se,null,oe(z.value,s=>(p(),I(A,{key:s,label:s,value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):(p(),I(Tt,{key:1,modelValue:d.server_host,"onUpdate:modelValue":e[5]||(e[5]=s=>d.server_host=s),disabled:""},null,8,["modelValue"]))]),_:1}),a(x,{label:"端口"},{default:o(()=>[l("div",ra,[a(xe,{modelValue:d.server_port,"onUpdate:modelValue":e[6]||(e[6]=s=>d.server_port=s),min:1024,max:65535},null,8,["modelValue"]),a(h,{onClick:Xe,loading:re.value},{default:o(()=>e[19]||(e[19]=[_("检测端口占用")])),_:1},8,["loading"])]),_e.value?(p(),w("div",da,[a(Vt,{type:R.value?N.value?"success":"error":"warning",title:$e.value,closable:!1,"show-icon":""},null,8,["type","title"])])):Re("",!0)]),_:1}),a(x,{label:"后端运行任务是否隐藏进程窗口"},{default:o(()=>[a(Z,{modelValue:U.value,"onUpdate:modelValue":e[7]||(e[7]=s=>U.value=s),"active-text":"隐藏（推荐生产环境）","inactive-text":"显示（推荐调试）",disabled:L.value,onChange:_t},null,8,["modelValue","disabled"]),e[20]||(e[20]=l("div",{class:"form-item-tip"},"更改后需重启后端服务才能生效。",-1))]),_:1}),a(x,null,{default:o(()=>[l("div",ua,[a(h,{type:"primary",onClick:Ge,loading:L.value,disabled:!He.value},{default:o(()=>[a(n,null,{default:o(()=>[a(m(le))]),_:1}),e[21]||(e[21]=_(" 保存设置 "))]),_:1},8,["loading","disabled"]),a(h,{type:"danger",onClick:Qe,loading:q.value},{default:o(()=>[a(n,null,{default:o(()=>[a(m($))]),_:1}),e[22]||(e[22]=_(" 重启服务器 "))]),_:1},8,["loading"])])]),_:1})]),_:1},8,["model"])],2),l("div",{class:V(["settings-section",{active:b.value==="permission",prev:S.value==="permission"}])},[l("div",ca,[l("div",ma,[a(n,{class:"section-icon"},{default:o(()=>[a(m(Ue))]),_:1}),e[23]||(e[23]=l("span",null,"权限配置",-1))])]),a(K,{"label-width":"160px",class:"settings-form"},{default:o(()=>[l("div",pa,[l("h3",ga,[a(n,null,{default:o(()=>[a(m(Ut))]),_:1}),e[24]||(e[24]=_(" 坐标转换权限 "))]),l("div",va,[a(x,{label:"外网访问权限"},{default:o(()=>[l("div",fa,[a(Z,{modelValue:D.allowExternalCoordinate,"onUpdate:modelValue":e[8]||(e[8]=s=>D.allowExternalCoordinate=s),loading:de.value,onChange:Ze,"active-text":"允许外网访问","inactive-text":"仅限局域网","active-color":D.allowExternalCoordinate?"#67c23a":"#409eff"},null,8,["modelValue","loading","active-color"]),l("div",_a,[D.allowExternalCoordinate?(p(),w("p",ha,[a(n,null,{default:o(()=>[a(m(zt))]),_:1}),e[25]||(e[25]=_(" 警告：允许外网访问坐标转换功能可能存在安全风险 "))])):(p(),w("p",ya,[a(n,null,{default:o(()=>[a(m(Lt))]),_:1}),e[26]||(e[26]=_(" 当前仅允许局域网用户使用坐标转换功能 "))]))])])]),_:1})])])]),_:1})],2),l("div",{class:V(["settings-section theme-section",{active:b.value==="theme",prev:S.value==="theme"}])},[l("div",wa,[l("div",ba,[a(n,{class:"section-icon"},{default:o(()=>[a(m(ze))]),_:1}),e[27]||(e[27]=l("span",null,"主题配置",-1))])]),l("div",ka,[l("div",Ca,[(p(!0),w(se,null,oe(we.value,s=>(p(),w("div",{key:s.name,class:V(["theme-item",{active:C.value===s.name}]),onClick:P=>mt(s.name)},[l("div",{class:"theme-preview",style:Ot({background:s.gradient})},null,4),l("div",Ta,[l("div",Va,E(s.displayName),1),l("div",Sa,E(s.description),1)])],10,xa))),128))]),l("div",Ma,[a(h,{type:"primary",onClick:pt},{default:o(()=>[a(n,null,{default:o(()=>[a(m(le))]),_:1}),e[28]||(e[28]=_(" 应用主题 "))]),_:1}),a(h,{onClick:gt},{default:o(()=>[a(n,null,{default:o(()=>[a(m($))]),_:1}),e[29]||(e[29]=_(" 重置默认 "))]),_:1})])])],2),l("div",{class:V(["settings-section",{active:b.value==="ui",prev:S.value==="ui"}])},[l("div",Pa,[l("div",Ia,[a(n,{class:"section-icon"},{default:o(()=>[a(m(Le))]),_:1}),e[30]||(e[30]=l("span",null,"导航菜单配置",-1))])]),a(ke,{data:Be,border:"",class:"nav-table"},{default:o(()=>[a(g,{label:"显示",width:"80",align:"center"},{default:o(s=>[a(Z,{modelValue:i.visibleItems[s.row.key],"onUpdate:modelValue":P=>i.visibleItems[s.row.key]=P,disabled:s.row.key==="userManagement"||s.row.key==="settings"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(g,{prop:"label",label:"菜单项","min-width":"120"}),a(g,{prop:"description",label:"说明","min-width":"180"}),a(g,{label:"模式",width:"120"},{default:o(s=>[a(Ce,{modelValue:i.itemModes[s.row.key],"onUpdate:modelValue":P=>i.itemModes[s.row.key]=P,size:"small",disabled:s.row.key==="userManagement"||s.row.key==="settings"},{default:o(()=>[a(A,{label:"生产",value:"production"}),a(A,{label:"开发",value:"development"})]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),a(g,{label:"开发进度",width:"180"},{default:o(s=>[i.itemModes[s.row.key]==="development"?(p(),w("div",Na,[a(xe,{modelValue:i.itemProgress[s.row.key],"onUpdate:modelValue":P=>i.itemProgress[s.row.key]=P,min:0,max:100,step:5,size:"small",disabled:s.row.key==="userManagement"||s.row.key==="settings",onChange:P=>ht(s.row.key,P)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])):Re("",!0)]),_:1})]),_:1}),l("div",Ea,[a(h,{type:"primary",onClick:tt,loading:ie.value},{default:o(()=>[a(n,null,{default:o(()=>[a(m(le))]),_:1}),e[31]||(e[31]=_(" 保存设置 "))]),_:1},8,["loading"]),a(h,{onClick:at},{default:o(()=>[a(n,null,{default:o(()=>[a(m($))]),_:1}),e[32]||(e[32]=_(" 重置默认 "))]),_:1})])],2),l("div",{class:V(["settings-section",{active:b.value==="user",prev:S.value==="user"}])},[l("div",Da,[l("div",Ra,[a(n,{class:"section-icon"},{default:o(()=>[a(m(Oe))]),_:1}),e[33]||(e[33]=l("span",null,"用户注册控制",-1))])]),a(K,{"label-width":"120px",class:"settings-form"},{default:o(()=>[l("div",Ua,[a(Z,{modelValue:k.value,"onUpdate:modelValue":e[9]||(e[9]=s=>k.value=s),"active-text":"允许","inactive-text":"禁止",onChange:ot},null,8,["modelValue"]),l("div",{class:V(["status-indicator",{"is-active":k.value}])},[k.value?(p(),I(n,{key:0},{default:o(()=>[a(m(le))]),_:1})):(p(),I(n,{key:1},{default:o(()=>[a(m(qt))]),_:1})),l("span",za,E(k.value?"当前允许新用户注册":"当前禁止新用户注册"),1)],2)]),l("div",La,E(k.value?"开放注册后，新用户可以自行注册账号。建议在需要时再开放注册。":"关闭注册后，只有管理员可以创建新用户账号。"),1)]),_:1})],2)]))]),a(St,{modelValue:W.value,"onUpdate:modelValue":e[10]||(e[10]=s=>W.value=s),"show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1,width:"400px",class:"restart-dialog"},{header:o(()=>[l("div",Oa,[a(n,{class:"restart-icon"},{default:o(()=>[a(m(Wt))]),_:1}),l("span",null,"服务器重启中"+E(B.value),1)])]),default:o(()=>[e[34]||(e[34]=l("div",{class:"restart-content"}," 请勿关闭页面，重启完成后将自动跳转... ",-1))]),_:1},8,["modelValue"])])}}}),Fa=Ft(qa,[["__scopeId","data-v-dc81e8ff"]]);export{Fa as default};

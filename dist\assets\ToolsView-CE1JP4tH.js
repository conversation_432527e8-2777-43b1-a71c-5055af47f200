import{d as dl,u as cl,r as g,M as Oe,O as Re,a as ia,o as pa,c as ee,e as t,w as o,f as T,i as P,E as i,Y as da,P as ie,Z as Ze,b as v,m as A,V as Be,A as y,B as ca,C as I,n as je,F as B,s as Ne,$ as fa,a0 as ma,a1 as va,a2 as fl,Q as ml,a3 as _a,a4 as ga,R as vl,W as _l,H as ya,I as ha,a5 as Je,k as gl,T as ye,a6 as yl,l as U,a7 as pe,_ as hl}from"./index-E0SsINqw.js";import{f as bl}from"./format-CBpsKyOP.js";const wl={class:"tools-container"},Cl={class:"tab-content tab-applications"},zl={class:"search-section"},kl={class:"search-row"},$l={class:"search-buttons"},xl={class:"table-container"},Ul={key:0},Vl={class:"pagination-container"},Tl={key:1,class:"empty-state"},Al={class:"empty-content"},Rl={class:"empty-icon"},Sl={class:"tab-content tab-tools"},Dl={class:"search-section"},Pl={class:"search-row"},Ll={class:"search-buttons"},Il={class:"table-header"},Ml={class:"table-container"},ql={class:"pagination-container"},El={key:0,class:"message-content"},Fl={key:6,class:"color-picker-wrapper"},Ol={class:"color-value"},Bl={key:1,class:"no-params"},jl={class:"dialog-footer"},Nl={class:"dialog-footer"},Yl={class:"dialog-footer"},Hl={class:"pagination-container"},Xl={class:"error-log-content"},Wl={class:"dialog-footer"},Gl=5e3,Ql=dl({__name:"ToolsView",setup(Zl){const z=cl(),ba=gl(),M=g(!1),Se=g([]),De=g([]),he=g(""),Pe=a=>window.location.pathname.startsWith("/gsi/")?`/gsi${a}`:`${P.defaults.baseURL}${a}`,Ke=a=>window.location.pathname.startsWith("/gsi/")?`${window.location.protocol}//${window.location.host}/gsi${a}`:`${window.location.protocol}//${window.location.host}${a}`,be=g(""),G=g("applications"),we=g(!1),Ce=g(!1),de=g(1),ae=g(1),ce=g(1),ze=g(10),ke=g(10),le=g(0),wa=g(0),E=g(localStorage.getItem("token")),j={PARSE_FMW:"/api/parse_fmw",RUN_TOOL:"/api/run_fme",UPDATE_TOOL:"/api/tools/update",UPLOAD_FILE:"/api/upload",UPLOAD_TOOL:"/api/tools/upload",MY_APPLICATIONS:"/api/tools/my-applications",RUN_RECORDS:"/api/tools/run_records",DELETE_RESULT:"/api/tools/delete_result",DOWNLOAD_RESULT:"/api/tools/download_result",UPDATE_COUNT:"/api/tools/update_usacount",VERIFY_RUN_PERMISSION:"/api/tools/verify_run_permission"},Le=()=>({Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}),ea=Oe(()=>Se.value.filter(a=>a.fmw_name.toLowerCase().includes(he.value.toLowerCase())||a.project.toLowerCase().includes(he.value.toLowerCase()))),Ye=Oe(()=>De.value.filter(a=>{const e=(a.status||"").toLowerCase();return e==="已通过"||e==="approved"?a.fmw_name.toLowerCase().includes(be.value.toLowerCase())||a.project.toLowerCase().includes(be.value.toLowerCase()):!1})),Ca=Oe(()=>{const a=(ce.value-1)*ze.value,e=a+ze.value;return ea.value.slice(a,e)}),za=Oe(()=>{const a=(ae.value-1)*ke.value,e=a+ke.value;return Ye.value.slice(a,e)}),He=()=>{de.value=1},Xe=()=>{ae.value=1},ka=()=>{ba.push({name:"market"})},$a=()=>{he.value="",ce.value=1},xa=()=>{be.value="",ae.value=1},Ua=a=>{ae.value=a},Z=async()=>{if(!we.value){we.value=!0,M.value=!0;try{const a=await P.get("/api/tools/list",{params:{username:z.user.username,my_tools:"true"},headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});a.data.success&&(Se.value=a.data.data||[],wa.value=Se.value.length)}catch{i.error("获取工具列表失败，请检查网络连接")}finally{M.value=!1,we.value=!1}}},fe=async()=>{if(!Ce.value){Ce.value=!0,M.value=!0;try{const a=await P.get(j.MY_APPLICATIONS,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username},params:{source:"tools"}});a.data.success&&(De.value=a.data.data||[])}catch{i.error("获取申请列表失败，请检查网络连接")}finally{M.value=!1,Ce.value=!1}}},J=(a,e="yyyy-MM-dd HH:mm")=>{if(!a)return"--";try{return bl(new Date(a),e)}catch{return"--"}};Re(()=>z.user,a=>{a!=null&&a.username?Z():(Se.value=[],De.value=[])},{immediate:!0});const te=g(!1),k=g(null),Q=g([]),m=g({}),F=g(null),$e=g({}),Ie=g(!1),We=g(!1),Va=async a=>{var e,u,p,c,d;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}Ie.value=!1,We.value=!0,k.value={...a,isApplyRun:!1,isRun:!0},M.value=!0,console.log("开始解析工具参数:",{fmw_id:a.fmw_id,username:z.user.username});const n=await P.post(j.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});console.log("解析参数响应:",n.data),console.log("后端返回的原始参数:",n.data.data.parameters);const w=(n.data.data.def_params||"N")==="Y",N=[],W=n.data.data.parameters,ge=n.data.data.parameters_dict,S={},ne={};(!Array.isArray(W)||W.length===0)&&(console.warn("未找到任何参数"),i.warning("该工具没有可配置的参数"));for(const $ of W)try{const _=$.name,r=$.info;if(!_||!r){console.warn("参数格式不正确:",$);continue}if(r.access_mode==="write"){console.log(`跳过write类型参数 ${_}`);continue}console.log(`处理参数 ${_}:`,r);const Y=r.type==="file"||((e=r.prompt)==null?void 0:e.includes("压缩包"));let K={prop:_,label:r.prompt||_,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};r.visibility&&(K.component.visibility=r.visibility);const D=xe(K);let f=w;if(!D){Y&&w&&(ne[_]=r.default_value||"",console.log(`参数 ${_} 不显示但保存到hiddenParams:`,r.default_value));continue}r.type==="file"&&console.log(`文件参数 ${_} 的配置:`,{itemsToSelect:r.itemsToSelect,is_folder:r.is_folder,file_types:r.file_types,selectMultiple:r.selectMultiple});let s={prop:_,label:r.prompt||_,required:r.required,tip:r.prompt||"",type:"text",order:r.order,component:{type:"el-input",props:{placeholder:""}}};if(r.required&&(S[_]=[{required:!0,message:`请输入${r.prompt||_}`,trigger:["blur","change"]}]),r.type==="message"||(u=r.prompt)!=null&&u.includes("消息")||(p=r.prompt)!=null&&p.includes("提示"))s.type="message",s.component={type:"div",props:{class:"message-content"},content:r.prompt||_},delete S[_];else if((r.type==="dropdown"||r.type==="listbox")&&r.options&&r.options.length>0){if(s.type="select",s.component={type:"el-select",props:{placeholder:"",multiple:r.type==="listbox",delimiter:r.delimiter||","},options:r.options.map(x=>typeof x=="object"&&x!==null?{label:x.label||x,value:x.value||x.label||x}:{label:x,value:x})},f&&r.default_value)r.type==="listbox"?m.value[_]=r.default_value.split(r.delimiter||","):m.value[_]=r.default_value;else if(r.options.length>0){const x=r.options[0];m.value[_]=typeof x=="object"?x.value:x}}else if(r.type==="color")s.type="color",s.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[_]=f&&r.default_value?r.default_value:"rgb(255, 255, 255)",r.required&&(S[_]=[{required:!0,message:`请选择${r.prompt||_}`,trigger:["change"]}]);else if(r.type==="file"||(c=r.prompt)!=null&&c.includes("压缩包")){s.type="upload";const x=((d=r.file_types)==null?void 0:d.map(q=>q.replace("*","")).filter(q=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(q.toLowerCase())).join(","))||"";s.component={type:"el-upload",props:{action:Pe(j.UPLOAD_FILE),"on-success":q=>aa(_,q),"on-remove":()=>la(_),"before-remove":ta,"before-upload":q=>{if(q.size>10737418240)return i.error("文件大小不能超过10GB"),!1;if(r.file_types&&r.file_types.length>0){const O=q.name.substring(q.name.lastIndexOf(".")).toLowerCase();if(!r.file_types.some(l=>{const h=l.replace("*","").toLowerCase();return(r.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h))&&O===h})){const l=r.file_types.filter(h=>r.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h.toLowerCase())).join("、");return i.error(`只能上传${l}格式的文件`),!1}}return!0},multiple:r.selectMultiple||!1,"show-file-list":!0,accept:x,headers:Le()}},r.required&&(S[_]=[{required:!0,message:`请上传${r.prompt||"文件"}`,trigger:["change"]}])}else r.type==="number"?(s.type="number",s.component={type:"el-input-number",props:{min:0,max:999999}},m.value[_]=f&&r.default_value?Number(r.default_value):0):r.type==="datetime"?(s.type="datetime",s.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[_]=f&&r.default_value?r.default_value:"",r.required&&(S[_]=[{required:!0,message:`请选择${r.prompt||_}`,trigger:["change"]}])):r.type==="checkbox"||r.type==="boolean"?(s.type="checkbox",s.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[_]=f&&r.default_value?r.default_value:"NO",r.required&&(S[_]=[{required:!0,message:`请选择${r.prompt||_}`,trigger:["change"]}])):(r.type==="text"||!r.type)&&(s.type="text",s.component={type:"el-input",props:{placeholder:""}},m.value[_]=f&&r.default_value?r.default_value:"");r.visibility&&(console.log(`参数 ${_} 的visibility条件:`,r.visibility),s.component.visibility=r.visibility),N.push(s)}catch(_){console.error("处理参数失败:",_),i.error("处理参数失败，请检查网络连接")}Q.value=N.sort(($,_)=>$.order-_.order),console.log("处理后的表单项:",Q.value),$e.value=S,k.value={...a,fmw_path:n.data.data.path,name:n.data.data.name,hiddenParams:ne},te.value=!0}catch(n){console.error("运行工具失败:",n);let b="运行工具失败";if(n.response){const w=n.response.data;b=(w==null?void 0:w.error)||(w==null?void 0:w.message)||"服务器错误",console.error("服务器返回错误:",w)}else n.request?(b="网络请求失败，请检查网络连接",console.error("网络请求错误:",n.request)):console.error("请求配置错误:",n.message);i.error(b)}finally{M.value=!1}},aa=(a,e)=>{e.success?m.value[a]=e.data.path:i.error(e.message||"文件上传失败")},la=a=>{m.value[a]=""},ta=a=>!0,Me=g(!1),H=g([]),L=g(null),oa=a=>a.some(e=>{var p;const u=(p=e.status)==null?void 0:p.toLowerCase();return u==="running"||u==="pending"||u==="processing"||u==="运行中"||u==="处理中"||u==="等待中"});Re(Me,a=>{a?Ta():(de.value=1,L.value&&(clearInterval(L.value),L.value=null))});const Ta=()=>{L.value&&clearInterval(L.value),L.value=setInterval(async()=>{oa(H.value)?(console.log("定时刷新运行记录..."),await qe(!1)):(console.log("没有运行中的任务，停止定时器"),L.value&&(clearInterval(L.value),L.value=null))},Gl)};ia(()=>{L.value&&(clearInterval(L.value),L.value=null)});const Aa=a=>{const e=(a||"").toLowerCase().replace(/\s/g,"");return["approved","已通过","completed","success","已完成"].some(u=>e.includes(u))?"success":["rejected","已驳回","failed","error","运行失败"].some(u=>e.includes(u))?"danger":["pending","审批中"].some(u=>e.includes(u))?"info":["running","processing","运行中","处理中","等待中"].some(u=>e.includes(u))?"warning":"info"},Ra=a=>{switch(a==null?void 0:a.toLowerCase()){case"approved":return"已通过";case"rejected":return"已驳回";case"pending":return"审批中";case"running":case"processing":return"运行中";case"completed":case"success":return"已完成";case"failed":case"error":return"运行失败";case"运行中":return"运行中";case"处理中":return"处理中";case"等待中":return"等待中";case"已完成":return"已完成";case"运行失败":return"运行失败";default:return a||"未知状态"}},Sa=a=>{if(!a)return"-";const e=Math.floor(a/60),u=a%60;return`${e}分${u}秒`},Da=async a=>{try{if(!a.tool_id||!a.task_id||!a.file_name){console.error("缺少必要参数:",{tool_id:a.tool_id,task_id:a.task_id,file_name:a.file_name}),i.error("下载失败：缺少必要参数");return}const e=`models/${a.tool_id}/output/${a.task_id}/${a.file_name}`,u=document.createElement("iframe");u.style.display="none",document.body.appendChild(u);const p=document.createElement("a");p.style.display="none",document.body.appendChild(p);const c=`${j.DOWNLOAD_RESULT}?file_path=${encodeURIComponent(e)}`;p.href=Ke(c),p.download=a.file_name,p.click(),setTimeout(()=>{document.body.removeChild(p),document.body.removeChild(u)},100),i.success("开始下载")}catch(e){console.error("下载运行成果失败:",e),i.error("下载失败，请稍后重试")}},Pa=async a=>{try{await ye.confirm("确定要删除该运行记录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=H.value.findIndex(u=>u.task_id===a.task_id);e!==-1&&(H.value.splice(e,1),le.value--),P.post("/api/tools/delete_result",{task_id:a.task_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}}).then(u=>{u.data.success?i.success("删除成功"):(H.value.splice(e,0,a),le.value++,i.error(u.data.message||"删除失败"))}).catch(u=>{H.value.splice(e,0,a),le.value++,i.error("删除失败，请稍后重试")})}catch(e){e!=="cancel"&&i.error("删除失败，请稍后重试")}},qe=async(a=!0)=>{var e,u,p,c;if(!((e=k.value)!=null&&e.fmw_id)){console.error("缺少 fmw_id，当前工具信息:",k.value),i.error("获取运行记录失败：缺少必要参数");return}if(!((u=z.user)!=null&&u.username)){console.error("缺少 username，当前用户信息:",z.user),i.error("获取运行记录失败：用户未登录");return}a&&(M.value=!0);try{const d={fmw_id:k.value.fmw_id,username:z.user.username,page:de.value,page_size:10};console.log("获取运行记录参数:",d);const n=await P.get(`/api/tools/run_records?fmw_id=${d.fmw_id}&username=${d.username}&page=${d.page}&page_size=${d.page_size}`,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});n.data.success?(H.value=Array.isArray(n.data.data.records)?n.data.data.records:[],le.value=((p=n.data.data.pagination)==null?void 0:p.total)||0,a||!oa(H.value)&&L.value&&(console.log("没有运行中的任务，停止定时器"),clearInterval(L.value),L.value=null)):(i.error(n.data.message||"获取运行记录失败"),H.value=[],le.value=0)}catch(d){console.error("获取运行记录失败:",d),d.response?(console.error("错误响应:",d.response.data),a&&i.error(((c=d.response.data)==null?void 0:c.message)||"获取运行记录失败")):a&&i.error("获取运行记录失败"),H.value=[],le.value=0}finally{a&&(M.value=!1)}},La=a=>{ce.value=a},Ia=a=>{console.log("页码改变:",a),console.log("当前工具信息:",k.value),de.value=a,qe()},Ma=a=>{ye.confirm("确定要删除该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await P.post("/api/tools/delete-application",{applicationId:a.id},{headers:{"X-Username":z.user.username}});e.data.success?(i.success("删除成功"),await fe()):i.error(e.data.message||"删除失败")}catch{i.error("删除申请失败，请检查网络连接")}}).catch(()=>{})},qa=a=>{ye.confirm("确定要撤回该申请吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await P.post("/api/tools/withdraw-application",{applicationId:a.id},{headers:{"X-Username":z.user.username}});e.data.success?(i.success("撤回成功"),await fe()):i.error(e.data.message||"撤回失败")}catch{i.error("撤回申请失败，请检查网络连接")}}).catch(()=>{})};pa(()=>{Z()}),ia(()=>{});const me=g(!1),ve=g(!1),C=g({fmw_name:"",project:"",tools_class:"",description:"",file:null,use_default_params:!1}),X=g({fmw_id:"",file:null}),Ea={fmw_name:[{required:!0,message:"请输入工具名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],project:[{required:!0,message:"请输入所属项目",trigger:"blur"}],tools_class:[{required:!0,message:"请选择工具类型",trigger:"change"}],description:[{required:!0,message:"请输入工具描述",trigger:"blur"}],file:[{required:!0,message:"请上传工具文件",trigger:"change"}]},oe=g(null),se=g(null),Fa=()=>{te.value=!1},Oa=()=>{var a;m.value={},Q.value=[],F.value&&F.value.resetFields(),(a=F.value)!=null&&a.$el&&F.value.$el.querySelectorAll(".el-upload").forEach(u=>{var c;const p=(c=u.__vueParentComponent)==null?void 0:c.ctx;p&&typeof p.clearFiles=="function"&&p.clearFiles()})},Ba=()=>{me.value=!1},ja=()=>{re.value&&re.value.resetFields(),C.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles()},Na=()=>{ve.value=!1},Ya=()=>{Ee.value&&Ee.value.resetFields(),X.value={fmw_id:"",file:null,file_path:""},se.value&&typeof se.value.clearFiles=="function"&&se.value.clearFiles()},Ha=()=>{me.value=!0},re=g(),Ee=g();Re(()=>m.value,a=>{const e={};Q.value.forEach(u=>{xe(u)&&$e.value[u.prop]&&(e[u.prop]=$e.value[u.prop])}),F.value&&(F.value.clearValidate(),F.value.rules=e)},{deep:!0});const Xa=async()=>{var a;if(!Ue.value){Ue.value=!0;try{if(!k.value){i.error("工具信息不完整");return}const e=Q.value.filter(d=>xe(d));console.log("可见的表单项:",e.map(d=>d.prop));for(const d of e)if(d.required&&!m.value[d.prop]){let n="";d.type==="file"||d.type==="upload"?n=`请上传${d.label}`:d.type==="select"||d.type==="dropdown"||d.type==="listbox"?n=`请选择${d.label}`:n=`请填写${d.label}`,i.error(n);return}const p={task_id:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,fmw_id:k.value.fmw_id,fmw_name:k.value.fmw_name,fmw_path:k.value.fmw_path,params:{}};for(const d of e){const n=m.value[d.prop];n!=null&&!d.prop.endsWith("_value")&&(d.type==="color"?p.params[d.prop]=m.value[`${d.prop}_value`]:p.params[d.prop]=n)}if(k.value.hiddenParams&&(Object.assign(p.params,k.value.hiddenParams),console.log("合并的hiddenParams:",k.value.hiddenParams)),console.log("提交的请求数据:",p),Ie.value)try{const d=await P.post(j.UPDATE_COUNT,{id:k.value.id,username:z.user.username});if(!d.data.success){i.error(d.data.message||"更新使用次数失败");return}const n=De.value.find(b=>b.id===k.value.id);n&&(n.count=(parseInt(n.count)||0)+1,n.remaining_count=(parseInt(n.usage_count)||0)-n.count)}catch(d){console.error("更新使用次数失败:",d),i.error("更新使用次数失败，请稍后重试");return}const c=await P.post(j.RUN_TOOL,p,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});c.data.success?(i.success("任务提交成功"),m.value={},Q.value=[],F.value&&F.value.resetFields(),(a=F.value)!=null&&a.$el&&F.value.$el.querySelectorAll(".el-upload").forEach(n=>{var w;const b=(w=n.__vueParentComponent)==null?void 0:w.ctx;b&&typeof b.clearFiles=="function"&&b.clearFiles()}),te.value=!1,await qe(),await fe()):i.error(c.data.message||"任务提交失败")}catch(e){console.error("提交任务失败:",e),i.error("提交失败，请稍后重试")}finally{Ue.value=!1}}},Wa=async()=>{if(!Ve.value&&(Ve.value=!0,!!re.value))try{if(await re.value.validate(),!C.value.file_path){i.error("请上传工具文件");return}const a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",u={fmw_id:Array.from({length:40},()=>a[Math.floor(Math.random()*a.length)]).join(""),fmw_name:C.value.fmw_name,project:C.value.project,tools_class:C.value.tools_class,description:C.value.description,fmw_path:C.value.file_path,file_path:C.value.file_path,data:new Date().toISOString(),def_params:C.value.use_default_params?"Y":"N"},p=await P.post(`${j.UPLOAD_TOOL}`,u,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});p.data.success?(i.success("工具上传成功"),me.value=!1,C.value={fmw_name:"",project:"",description:"",file:null,file_path:"",use_default_params:!1},re.value&&re.value.resetFields(),oe.value&&typeof oe.value.clearFiles=="function"&&oe.value.clearFiles(),await Z()):i.error(p.data.message||"上传失败")}catch(a){console.error("上传工具失败:",a),i.error("参数未填写完整")}finally{Ve.value=!1}},Ga=async()=>{if(!_e.value){if(_e.value=!0,!X.value.file_path){i.error("请先上传工具文件"),_e.value=!1;return}try{const a={fmw_id:X.value.fmw_id,file_path:X.value.file_path},e=await P.post(`${j.UPDATE_TOOL}`,a,{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});e.data.success?(i.success("工具更新成功"),ve.value=!1,X.value={fmw_id:"",file:null,file_path:""},Ee.value&&Ee.value.resetFields(),se.value&&typeof se.value.clearFiles=="function"&&se.value.clearFiles(),await Z()):i.error(e.data.message||"更新失败")}catch(a){console.error("更新工具失败:",a),i.error("更新失败，请检查网络连接")}finally{_e.value=!1}}},sa=async a=>{try{k.value=a,Me.value=!0,await qe()}catch(e){console.error("获取运行记录失败:",e),i.error("获取运行记录失败，请检查网络连接")}},Qa=a=>{X.value={fmw_id:a.fmw_id,file:null},ve.value=!0},Za=async a=>{try{if(await ye.confirm("是否确认下载该工具？","下载确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}),!a.fmw_path){i.error("下载失败：缺少必要参数");return}const e=`/api/tools/download?fmw_path=${encodeURIComponent(a.fmw_path)}`;window.open(Ke(e)),i.success("已发起下载")}catch(e){if(e==="cancel")return;i.error("下载失败，请稍后重试")}},Ja=async a=>{try{await ye.confirm("确定要删除该工具吗？删除后无法恢复。","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await P.post("/api/tools/delete",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});e.data.success?(i.success("删除成功"),await Z()):i.error(e.data.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除工具失败:",e),i.error("删除失败，请检查网络连接"))}},Ka=async a=>{try{const e=await P.post("/api/tools/detail",{fmw_id:a.fmw_id},{headers:{Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(e.data.success){const u=e.data.data;ye.alert(`
        <div class="tool-detail">
          <p><strong>工具名称：</strong>${u.fmw_name}</p>
          <p><strong>所属项目：</strong>${u.project}</p>
          <p><strong>上传时间：</strong>${J(u.created_at)}</p>
          <p><strong>运行次数：</strong>${u.run_times}</p>
          <p><strong>工具描述：</strong>${u.description||"暂无描述"}</p>
        </div>
        `,"工具详情",{confirmButtonText:"确定",dangerouslyUseHTMLString:!0,customClass:"tool-detail-dialog"})}else i.error(e.data.message||"获取详情失败")}catch(e){console.error("获取工具详情失败:",e),i.error("获取详情失败，请检查网络连接")}},el=(a,e)=>{if(!e){m.value[a]="rgb(255, 255, 255)";return}m.value[a]=e;const u=e.match(/(\d+),\s*(\d+),\s*(\d+)/);if(u){const[,p,c,d]=u;m.value[`${a}_value`]=`${p},${c},${d}`}else m.value[`${a}_value`]="255,255,255"},xe=a=>{var u;if(!((u=a.component)!=null&&u.visibility))return!0;const e=a.component.visibility;if(!e.if||!Array.isArray(e.if))return!0;for(const p of e.if){const{condition:c,then:d}=p;let n=!1;if(c.allOf)n=c.allOf.every(b=>{if(b.equals){const{parameter:w,value:N}=b.equals;return m.value[w]===N}else if(b.isEnabled){const{parameter:w}=b.isEnabled;return!!m.value[w]}return!1});else if(c.equals){const{parameter:b,value:w}=c.equals;n=m.value[b]===w}else if(c.isEnabled){const{parameter:b}=c.isEnabled;n=!!m.value[b]}if(n)return d==="visibleEnabled"||d==="visibleDisabled"}return!1},al=async a=>{var e,u,p,c,d,n,b,w,N,W,ge;try{if(!a||!a.fmw_id){i.error("工具信息不完整");return}console.log("handleApplyRun 接收到的 row:",a);try{const D=await P.post(j.VERIFY_RUN_PERMISSION,{id:a.id,username:z.user.username},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(!D.data.success){i.error(D.data.message||"验证运行权限失败");return}}catch(D){console.error("验证运行权限失败:",D),D.response?i.error(((e=D.response.data)==null?void 0:e.message)||"验证运行权限失败"):i.error("验证运行权限失败，请稍后重试");return}Ie.value=!0,We.value=!1,k.value={...a,isApplyRun:!0,isRun:!1},console.log("handleApplyRun 设置后的状态:",{isApplyRun:Ie.value,isRun:We.value,currentTool:k.value,id:k.value.id,fmw_id:k.value.fmw_id}),M.value=!0;const S=await P.post(j.PARSE_FMW,{fmw_id:a.fmw_id},{headers:{"Content-Type":"application/json",Authorization:`Bearer ${E.value}`,"X-Username":z.user.username}});if(!((u=S.data)!=null&&u.success)||!((c=(p=S.data)==null?void 0:p.data)!=null&&c.parameters)){const D=((n=(d=S.data)==null?void 0:d.data)==null?void 0:n.error)||"解析工具参数失败";i.error(D);return}const $=(S.data.data.def_params||"N")==="Y",_=[],r=S.data.data.parameters,Y={},K={};for(const D of r)try{const f=D.name,s=D.info;if(!f||!s||s.access_mode==="write")continue;const x=s.type==="file"||((b=s.prompt)==null?void 0:b.includes("压缩包"));let q={prop:f,label:s.prompt||f,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};s.visibility&&(q.component.visibility=s.visibility);const ue=xe(q);let O=$;if(!ue){x&&$&&(K[f]=s.default_value||"",console.log(`参数 ${f} 不显示但保存到hiddenParams:`,s.default_value));continue}let V={prop:f,label:s.prompt||f,required:s.required,tip:s.prompt||"",type:"text",order:s.order,component:{type:"el-input",props:{placeholder:""}}};if(s.required&&(Y[f]=[{required:!0,message:`请输入${s.prompt||f}`,trigger:["blur","change"]}]),s.type==="message"||(w=s.prompt)!=null&&w.includes("消息")||(N=s.prompt)!=null&&N.includes("提示"))V.type="message",V.component={type:"div",props:{class:"message-content"},content:s.prompt||f},delete Y[f];else if((s.type==="dropdown"||s.type==="listbox")&&s.options&&s.options.length>0){if(V.type="select",V.component={type:"el-select",props:{placeholder:""},options:s.options.map(l=>typeof l=="object"&&l!==null?{label:l.label||l,value:l.value||l.label||l}:{label:l,value:l})},O&&s.default_value)m.value[f]=s.default_value;else if(s.options.length>0){const l=s.options[0];m.value[f]=typeof l=="object"?l.value:l}}else if(s.type==="color")V.type="color",V.component={type:"el-color-picker",props:{showAlpha:!1,colorFormat:"rgb",showPicker:!0,size:"default",popperClass:"custom-color-picker"}},m.value[f]=O&&s.default_value?s.default_value:"rgb(255, 255, 255)",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}]);else if(s.type==="file"||(W=s.prompt)!=null&&W.includes("压缩包")){V.type="upload";const l=((ge=s.file_types)==null?void 0:ge.map(h=>h.replace("*","")).filter(h=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(h.toLowerCase())).join(","))||"";V.component={type:"el-upload",props:{action:Pe(j.UPLOAD_FILE),"on-success":h=>aa(f,h),"on-remove":()=>la(f),"before-remove":ta,"before-upload":h=>{if(h.size>10737418240)return i.error("文件大小不能超过10GB"),!1;if(s.file_types&&s.file_types.length>0){const Te=h.name.substring(h.name.lastIndexOf(".")).toLowerCase();if(!s.file_types.some(Qe=>{const Ae=Qe.replace("*","").toLowerCase();return(s.is_folder||![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae))&&Te===Ae})){const Qe=s.file_types.filter(Ae=>s.is_folder?!0:![".zip",".rar",".7z",".7zip",".zipx",".tar",".tar.gz",".tar.bz2",".gz",".bz2",".rvz",".tgz"].includes(Ae.toLowerCase())).join("、");return i.error(`只能上传${Qe}格式的文件`),!1}}return!0},multiple:s.selectMultiple||!1,"show-file-list":!0,accept:l,headers:Le()}},s.required&&(Y[f]=[{required:!0,message:`请上传${s.prompt||"文件"}`,trigger:["change"]}])}else s.type==="number"?(V.type="number",V.component={type:"el-input-number",props:{min:0,max:999999}},m.value[f]=O&&s.default_value?Number(s.default_value):0):s.type==="datetime"?(V.type="datetime",V.component={type:"el-date-picker",props:{type:"datetime",placeholder:"",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",style:"width: 100%"}},m.value[f]=O&&s.default_value?s.default_value:"",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}])):s.type==="checkbox"||s.type==="boolean"?(V.type="checkbox",V.component={type:"el-checkbox",props:{class:"custom-checkbox"}},m.value[f]=O&&s.default_value?s.default_value:"NO",s.required&&(Y[f]=[{required:!0,message:`请选择${s.prompt||f}`,trigger:["change"]}])):(s.type==="text"||!s.type)&&(V.type="text",V.component={type:"el-input",props:{placeholder:""}},m.value[f]=O&&s.default_value?s.default_value:"");s.visibility&&(console.log(`参数 ${f} 的visibility条件:`,s.visibility),V.component.visibility=s.visibility),_.push(V)}catch{console.error("处理参数失败，请检查网络连接")}Q.value=_.sort((D,f)=>D.order-f.order),$e.value=Y,k.value={...a,fmw_path:S.data.data.path,name:S.data.data.name,hiddenParams:K},te.value=!0}catch{i.error("运行工具失败")}finally{M.value=!1}},Fe=g(!1),ra=g(""),ll=a=>{ra.value=a,Fe.value=!0},tl=a=>{ze.value=a,ce.value=1},ol=a=>{ke.value=a,ae.value=1},sl=(a,e)=>{a.success&&a.data&&a.data.path?(X.value.file_path=a.data.path,X.value.file=e):(i.error(a.message||"文件上传失败"),X.value.file_path="")},rl=a=>{i.error("文件上传失败，请检查网络连接"),X.value.file_path=""};g(0);const nl=(a,e)=>{a.success&&a.data&&a.data.path?(C.value.file_path=a.data.path,C.value.file=e):(i.error(a.message||"文件上传失败"),C.value.file_path="")},ul=a=>{i.error("文件上传失败，请检查网络连接"),C.value.file_path=""},il=async a=>{a.props.name!==G.value&&(a.props.name==="applications"&&!Ce.value?await fe():a.props.name==="tools"&&!we.value&&await Z())},na=g(!1);Re(G,(a,e)=>{a!==e&&(na.value=!0,setTimeout(()=>{na.value=!1},400))});const ua=yl(),pl=a=>{const e=a.row||a;return ua.query.highlightId&&String(e.id)===String(ua.query.highlightId)?"highlight-row":""},Ue=g(!1),Ve=g(!1),_e=g(!1);return pa(async()=>{G.value==="applications"?await fe():G.value==="tools"&&await Z()}),Re(G,async a=>{a==="applications"&&!Ce.value?await fe():a==="tools"&&!we.value&&await Z()}),(a,e)=>{var O,V;const u=T("el-input"),p=T("el-icon"),c=T("el-button"),d=T("el-card"),n=T("el-table-column"),b=T("el-tooltip"),w=T("el-button-group"),N=T("el-table"),W=T("el-pagination"),ge=T("el-tab-pane"),S=T("el-tabs"),ne=T("el-upload"),$=T("el-option"),_=T("el-select"),r=T("el-input-number"),Y=T("el-date-picker"),K=T("el-checkbox"),D=T("el-color-picker"),f=T("el-form-item"),s=T("el-form"),x=T("el-dialog"),q=T("el-tag"),ue=ml("loading");return U(),ee("div",wl,[t(S,{modelValue:G.value,"onUpdate:modelValue":e[6]||(e[6]=l=>G.value=l),class:"tools-tabs",onTabClick:il},{default:o(()=>[t(ge,{label:"我的申请",name:"applications"},{default:o(()=>[t(da,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ie(v("div",Cl,[t(d,{class:"search-card"},{default:o(()=>[v("div",zl,[v("div",kl,[t(u,{modelValue:be.value,"onUpdate:modelValue":e[0]||(e[0]=l=>be.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":A(Be),onClear:Xe,onInput:Xe},null,8,["modelValue","prefix-icon"]),v("div",$l,[t(c,{type:"primary",onClick:Xe},{default:o(()=>[t(p,null,{default:o(()=>[t(A(Be))]),_:1}),e[22]||(e[22]=y(" 搜索 "))]),_:1}),t(c,{onClick:xa},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ca))]),_:1}),e[23]||(e[23]=y(" 重置 "))]),_:1})])])])]),_:1}),t(d,{class:"table-card"},{header:o(()=>e[24]||(e[24]=[v("div",{class:"table-header"},[v("span",{class:"title"},"我的申请")],-1)])),default:o(()=>[v("div",xl,[Ye.value.length>0?(U(),ee("div",Ul,[ie((U(),I(N,{data:za.value,style:{width:"100%"},"row-class-name":pl},{default:o(()=>[t(n,{type:"index",label:"序号",width:"80",align:"center"}),t(n,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"usage_count",label:"申请运行次数",width:"120",align:"center"},{default:o(({row:l})=>[v("span",{class:je({highlight:l.usage_count>0})},B(l.usage_count),3)]),_:1}),t(n,{label:"已运行次数",width:"120",align:"center"},{default:o(({row:l})=>[v("span",{class:je({highlight:l.count>0})},B(l.count),3)]),_:1}),t(n,{prop:"end_date",label:"使用截止日期",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.end_date,"yyyy-MM-dd"),placement:"top"},{default:o(()=>[v("span",null,B(J(l.end_date,"yyyy-MM-dd")),1)]),_:2},1032,["content"])]),_:1}),t(n,{prop:"created_at",label:"申请时间",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[v("span",null,B(J(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(n,{label:"操作",width:"300",align:"center",fixed:"right"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:h=>al(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(fa))]),_:1}),e[25]||(e[25]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:h=>sa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ma))]),_:1}),e[26]||(e[26]=y(" 运行成果 "))]),_:2},1032,["onClick"]),l.status==="已通过"?(U(),I(c,{key:0,type:"danger",size:"small",onClick:h=>Ma(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(va))]),_:1}),e[27]||(e[27]=y(" 删除 "))]),_:2},1032,["onClick"])):Ne("",!0),l.status==="审批中"?(U(),I(c,{key:1,type:"warning",size:"small",onClick:h=>qa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(fl))]),_:1}),e[28]||(e[28]=y(" 撤回 "))]),_:2},1032,["onClick"])):Ne("",!0)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ue,M.value]]),v("div",Vl,[t(W,{"current-page":ae.value,"onUpdate:currentPage":e[1]||(e[1]=l=>ae.value=l),"page-size":ke.value,"onUpdate:pageSize":e[2]||(e[2]=l=>ke.value=l),"page-sizes":[10,20,50,100],total:Ye.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ol,onCurrentChange:Ua},null,8,["current-page","page-size","total"])])])):ie((U(),ee("div",Tl,[v("div",Al,[v("div",Rl,[t(p,{size:"64"},{default:o(()=>[t(A(_a))]),_:1})]),e[30]||(e[30]=v("h3",{class:"empty-title"},"暂无工具申请",-1)),e[31]||(e[31]=v("p",{class:"empty-description"}," 您还没有申请任何工具，去工具市场申请您需要的工具吧！ ",-1)),t(c,{type:"primary",size:"large",onClick:ka,class:"go-market-button"},{default:o(()=>[t(p,null,{default:o(()=>[t(A(_a))]),_:1}),e[29]||(e[29]=y(" 去工具市场申请 "))]),_:1})])])),[[ue,M.value]])])]),_:1})],512),[[Ze,G.value==="applications"]])]),_:1})]),_:1}),t(ge,{label:"我的工具",name:"tools"},{default:o(()=>[t(da,{name:"tab-slide",mode:"out-in"},{default:o(()=>[ie(v("div",Sl,[t(d,{class:"search-card"},{default:o(()=>[v("div",Dl,[v("div",Pl,[t(u,{modelValue:he.value,"onUpdate:modelValue":e[3]||(e[3]=l=>he.value=l),placeholder:"工具名称或项目名称",class:"search-input",clearable:"","prefix-icon":A(Be),onClear:He,onInput:He},null,8,["modelValue","prefix-icon"]),v("div",Ll,[t(c,{type:"primary",onClick:He},{default:o(()=>[t(p,null,{default:o(()=>[t(A(Be))]),_:1}),e[32]||(e[32]=y(" 搜索 "))]),_:1}),t(c,{onClick:$a},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ca))]),_:1}),e[33]||(e[33]=y(" 重置 "))]),_:1})])])])]),_:1}),t(d,{class:"table-card"},{header:o(()=>[v("div",Il,[e[35]||(e[35]=v("span",{class:"title"},"我的工具",-1)),t(c,{type:"primary",onClick:Ha},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ga))]),_:1}),e[34]||(e[34]=y(" 上传工具 "))]),_:1})])]),default:o(()=>[v("div",Ml,[ie((U(),I(N,{data:Ca.value,style:{width:"100%"}},{default:o(()=>[t(n,{type:"index",label:"序号",width:"80",align:"center"}),t(n,{prop:"fmw_name",label:"工具名称","min-width":"200","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"project",label:"所属项目","min-width":"150","show-overflow-tooltip":"",align:"left"}),t(n,{prop:"run_times",label:"运行次数",width:"120",align:"center"},{default:o(({row:l})=>[v("span",{class:je({highlight:l.run_times>0})},B(l.run_times),3)]),_:1}),t(n,{prop:"created_at",label:"上传时间",width:"180",align:"center"},{default:o(({row:l})=>[t(b,{content:J(l.created_at,"yyyy-MM-dd HH:mm:ss"),placement:"top"},{default:o(()=>[v("span",null,B(J(l.created_at,"yyyy-MM-dd HH:mm")),1)]),_:2},1032,["content"])]),_:1}),t(n,{label:"工具更新",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"primary",size:"small",onClick:h=>Qa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ga))]),_:1}),e[36]||(e[36]=y(" 更新 "))]),_:2},1032,["onClick"])]),_:1}),t(n,{label:"工具下载",width:"120",align:"center"},{default:o(({row:l})=>[t(c,{type:"success",size:"small",onClick:h=>Za(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(vl))]),_:1}),e[37]||(e[37]=y(" 下载 "))]),_:2},1032,["onClick"])]),_:1}),t(n,{label:"操作",width:"400",align:"center",fixed:"right"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[t(c,{type:"primary",size:"small",onClick:h=>Va(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(fa))]),_:1}),e[38]||(e[38]=y(" 运行 "))]),_:2},1032,["onClick"]),t(c,{type:"success",size:"small",onClick:h=>sa(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(ma))]),_:1}),e[39]||(e[39]=y(" 运行成果 "))]),_:2},1032,["onClick"]),t(c,{type:"danger",size:"small",onClick:h=>Ja(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(va))]),_:1}),e[40]||(e[40]=y(" 删除 "))]),_:2},1032,["onClick"]),t(c,{type:"info",size:"small",onClick:h=>Ka(l)},{default:o(()=>[t(p,null,{default:o(()=>[t(A(_l))]),_:1}),e[41]||(e[41]=y(" 详情 "))]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ue,M.value]]),v("div",ql,[t(W,{"current-page":ce.value,"onUpdate:currentPage":e[4]||(e[4]=l=>ce.value=l),"page-size":ze.value,"onUpdate:pageSize":e[5]||(e[5]=l=>ze.value=l),"page-sizes":[10,20,50,100],total:ea.value.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:tl,onCurrentChange:La},null,8,["current-page","page-size","total"])])])]),_:1})],512),[[Ze,G.value==="tools"]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(x,{modelValue:te.value,"onUpdate:modelValue":e[8]||(e[8]=l=>te.value=l),title:`运行工具 - ${(O=k.value)==null?void 0:O.fmw_name}`,width:"655px","close-on-click-modal":!1,class:"run-dialog","destroy-on-close":!0,onClose:Fa,onAfterClose:Oa},{footer:o(()=>[v("span",jl,[t(c,{onClick:e[7]||(e[7]=l=>te.value=!1)},{default:o(()=>e[43]||(e[43]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Xa,loading:Ue.value,disabled:Ue.value},{default:o(()=>e[44]||(e[44]=[y("提交任务")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(s,{ref_key:"formRef",ref:F,model:m.value,rules:$e.value,"label-width":"200px",size:"small",class:"run-form"},{default:o(()=>[Q.value.length>0?(U(!0),ee(ya,{key:0},ha(Q.value,l=>ie((U(),I(f,{key:l.prop,label:l.type==="message"?"":l.label,prop:l.prop,required:l.required,class:je({"message-form-item":l.type==="message"})},{default:o(()=>{var h,Ge,Te;return[l.type==="message"?(U(),ee("div",El,B(l.component.content),1)):l.type==="upload"?(U(),I(ne,pe({key:1,ref_for:!0},l.component.props,{class:["upload-area",{"is-error":((Te=(Ge=(h=F.value)==null?void 0:h.fields)==null?void 0:Ge.find(R=>R.prop===l.prop))==null?void 0:Te.validateState)==="error"}],drag:""}),{default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(A(Je))]),_:1}),e[42]||(e[42]=v("div",{class:"el-upload__text"},[y(" 拖拽文件到此处"),v("br"),y("或"),v("em",null,"点击上传")],-1))]),_:2},1040,["class"])):l.type==="select"?(U(),I(_,pe({key:2,modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props,{"collapse-tags":l.component.props.multiple,"collapse-tags-tooltip":!0,style:{width:"100%"}}),{default:o(()=>[(U(!0),ee(ya,null,ha(l.component.options,R=>(U(),I($,{key:R.value,label:R.label,value:R.value,title:R.label},null,8,["label","value","title"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue","collapse-tags"])):l.type==="number"?(U(),I(r,pe({key:3,modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props,{style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="datetime"?(U(),I(Y,pe({key:4,modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"])):l.type==="checkbox"?(U(),I(K,pe({key:5,modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props,{"true-value":"YES","false-value":"NO"}),null,16,["modelValue","onUpdate:modelValue"])):l.type==="color"?(U(),ee("div",Fl,[t(D,pe({modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props,{onChange:R=>el(l.prop,R),style:{width:"100%"}}),null,16,["modelValue","onUpdate:modelValue","onChange"]),v("span",Ol,B(m.value[`${l.prop}_value`]||"255,255,255"),1)])):(U(),I(u,pe({key:7,modelValue:m.value[l.prop],"onUpdate:modelValue":R=>m.value[l.prop]=R,ref_for:!0},l.component.props),null,16,["modelValue","onUpdate:modelValue"]))]}),_:2},1032,["label","prop","required","class"])),[[Ze,xe(l)]])),128)):(U(),ee("div",Bl," 暂无参数需要填写 "))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),t(x,{modelValue:me.value,"onUpdate:modelValue":e[15]||(e[15]=l=>me.value=l),title:"上传工具",width:"500px","close-on-click-modal":!1,class:"upload-dialog",onClose:Ba,onAfterClose:ja},{footer:o(()=>[v("span",Nl,[t(c,{onClick:e[14]||(e[14]=l=>me.value=!1)},{default:o(()=>e[48]||(e[48]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Wa,loading:Ve.value,disabled:Ve.value},{default:o(()=>e[49]||(e[49]=[y("确定")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(s,{ref_key:"uploadFormRef",ref:re,model:C.value,rules:Ea,"label-width":"100px",size:"small"},{default:o(()=>[t(f,{label:"工具名称",prop:"fmw_name"},{default:o(()=>[t(u,{modelValue:C.value.fmw_name,"onUpdate:modelValue":e[9]||(e[9]=l=>C.value.fmw_name=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"所属项目",prop:"project"},{default:o(()=>[t(u,{modelValue:C.value.project,"onUpdate:modelValue":e[10]||(e[10]=l=>C.value.project=l),placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具类型",prop:"tools_class"},{default:o(()=>[t(_,{modelValue:C.value.tools_class,"onUpdate:modelValue":e[11]||(e[11]=l=>C.value.tools_class=l),placeholder:"请选择工具类型",style:{width:"100%"}},{default:o(()=>[t($,{label:"格式转换",value:"格式转换"}),t($,{label:"坐标系转换",value:"坐标系转换"}),t($,{label:"数据结构转换",value:"数据结构转换"}),t($,{label:"数据清洗",value:"数据清洗"}),t($,{label:"几何处理",value:"几何处理"}),t($,{label:"属性处理",value:"属性处理"}),t($,{label:"数据合并",value:"数据合并"}),t($,{label:"数据融合",value:"数据融合"}),t($,{label:"数据获取",value:"数据同步"}),t($,{label:"批处理",value:"批处理"}),t($,{label:"数据质检",value:"数据质检"}),t($,{label:"空间分析",value:"空间分析"}),t($,{label:"统计分析",value:"统计分析"})]),_:1},8,["modelValue"])]),_:1}),t(f,{label:"工具描述",prop:"description"},{default:o(()=>[t(u,{modelValue:C.value.description,"onUpdate:modelValue":e[12]||(e[12]=l=>C.value.description=l),type:"textarea",rows:3,placeholder:""},null,8,["modelValue"])]),_:1}),t(f,{label:"工具文件",prop:"file"},{default:o(()=>[t(ne,{ref_key:"uploadRef",ref:oe,class:"upload-demo",action:Pe("/api/upload"),headers:Le(),"on-success":nl,"on-error":ul,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[45]||(e[45]=[v("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(A(Je))]),_:1}),e[46]||(e[46]=v("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),v("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1}),t(f,{label:"使用默认参数",prop:"use_default_params"},{default:o(()=>[t(K,{modelValue:C.value.use_default_params,"onUpdate:modelValue":e[13]||(e[13]=l=>C.value.use_default_params=l)},{default:o(()=>e[47]||(e[47]=[y(" 勾选此项代表运行工具时自动读取默认参数 ")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(x,{modelValue:ve.value,"onUpdate:modelValue":e[17]||(e[17]=l=>ve.value=l),title:"更新工具",width:"500px","close-on-click-modal":!1,onClose:Na,onAfterClose:Ya},{footer:o(()=>[v("span",Yl,[t(c,{onClick:e[16]||(e[16]=l=>ve.value=!1)},{default:o(()=>e[52]||(e[52]=[y("取消")])),_:1}),t(c,{type:"primary",onClick:Ga,loading:_e.value,disabled:_e.value},{default:o(()=>e[53]||(e[53]=[y("确认更新")])),_:1},8,["loading","disabled"])])]),default:o(()=>[t(ne,{ref_key:"updateUploadRef",ref:se,class:"upload-demo",action:Pe("/api/upload"),headers:Le(),"on-success":sl,"on-error":rl,limit:1,accept:".fmw",drag:"","show-file-list":!0,"auto-upload":!0},{tip:o(()=>e[50]||(e[50]=[v("div",{class:"el-upload__tip"}," 只能上传 fmw 文件 ",-1)])),default:o(()=>[t(p,{class:"el-icon--upload"},{default:o(()=>[t(A(Je))]),_:1}),e[51]||(e[51]=v("div",{class:"el-upload__text"},[y(" 将文件拖到此处，或"),v("em",null,"点击上传")],-1))]),_:1},8,["action","headers"])]),_:1},8,["modelValue"]),t(x,{modelValue:Me.value,"onUpdate:modelValue":e[19]||(e[19]=l=>Me.value=l),title:`运行成果 - ${(V=k.value)==null?void 0:V.fmw_name}`,width:"832px","close-on-click-modal":!1,class:"result-dialog","destroy-on-close":!0},{default:o(()=>[ie((U(),I(N,{data:H.value,style:{width:"100%"},border:"","cell-style":{padding:"8px 0"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",padding:"8px 0"}},{default:o(()=>[t(n,{type:"index",label:"序号","min-width":"80",align:"center"}),t(n,{prop:"submit_time",label:"提交时间","min-width":"180",align:"center"},{default:o(({row:l})=>[y(B(J(l.submit_time,"yyyy-MM-dd HH:mm:ss")),1)]),_:1}),t(n,{prop:"status",label:"状态","min-width":"100",align:"center"},{default:o(({row:l})=>[t(q,{type:Aa(l.status)},{default:o(()=>[y(B(Ra(l.status)),1)]),_:2},1032,["type"])]),_:1}),t(n,{prop:"time_consuming",label:"运行耗时","min-width":"100",align:"center"},{default:o(({row:l})=>[y(B(Sa(l.time_consuming)),1)]),_:1}),t(n,{prop:"file_size",label:"文件大小","min-width":"100",align:"center"}),t(n,{label:"操作","min-width":"200",align:"center"},{default:o(({row:l})=>[t(w,null,{default:o(()=>[l.status==="failed"?(U(),I(c,{key:0,type:"warning",size:"small",onClick:h=>ll(l.error_message)},{default:o(()=>e[54]||(e[54]=[y(" 日志 ")])),_:2},1032,["onClick"])):Ne("",!0),l.file_name&&l.file_size!=="0.0MB"?(U(),I(c,{key:1,type:"primary",size:"small",onClick:h=>Da(l)},{default:o(()=>e[55]||(e[55]=[y(" 下载 ")])),_:2},1032,["onClick"])):Ne("",!0),t(c,{type:"danger",size:"small",onClick:h=>Pa(l)},{default:o(()=>e[56]||(e[56]=[y(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[ue,M.value]]),v("div",Hl,[t(W,{"current-page":de.value,"onUpdate:currentPage":e[18]||(e[18]=l=>de.value=l),"page-size":10,total:le.value,layout:"total, prev, pager, next, jumper",onCurrentChange:Ia},null,8,["current-page","total"])])]),_:1},8,["modelValue","title"]),t(x,{modelValue:Fe.value,"onUpdate:modelValue":e[21]||(e[21]=l=>Fe.value=l),title:"错误日志",width:"60%","close-on-click-modal":!1},{footer:o(()=>[v("span",Wl,[t(c,{onClick:e[20]||(e[20]=l=>Fe.value=!1)},{default:o(()=>e[57]||(e[57]=[y("关闭")])),_:1})])]),default:o(()=>[v("div",Xl,[v("pre",null,B(ra.value),1)])]),_:1},8,["modelValue"])])}}}),et=hl(Ql,[["__scopeId","data-v-ad1b3ccc"]]);export{et as default};

import{d as ze,r,N as K,M as N,o as $e,c as I,b as n,e as a,H as Q,I as W,n as X,w as l,f as i,i as c,E as o,C as Y,ai as De,F as h,m as _,v as ee,A as d,ad as re,ak as Be,B as Se,a1 as Ae,x as Fe,L as Re,T as te,l as b,_ as Te}from"./index-E0SsINqw.js";const Pe={class:"user-mgmt"},qe={class:"content-container"},Le={class:"nav-menu"},Me={class:"nav-list"},Ee=["onClick"],Ne={class:"nav-item-text"},Ie={class:"content-wrapper"},Oe={class:"section-header"},Ze={class:"header-left"},je={class:"table-container"},He={class:"action-btn-group"},Je={style:{display:"inline-block"}},Ge={style:{display:"inline-block"}},Ke={class:"table-pagination"},Qe={class:"section-header"},We={class:"header-left"},Xe={class:"table-container"},Ye={class:"table-pagination"},he={style:{"text-align":"center"}},et={style:{margin:"16px 0"}},tt={style:{"font-weight":"bold"}},at=ze({__name:"UserManagementView",setup(lt){const oe=[{key:"user",label:"用户账号管理",description:"管理系统用户账号"},{key:"dept",label:"部门管理",description:"管理部门信息"}],de=s=>({user:ee,dept:Re})[s]||ee,w=r("user"),$=r(!1),D=r(""),ue=async s=>{$.value||s===w.value||($.value=!0,D.value=w.value,w.value=s,await new Promise(e=>setTimeout(e,300)),D.value="",$.value=!1)},O=r([]),B=r([]),S=r([]),U=r(!1),Z=r(),p=K({username:"",password:"",real_name:"",role:"",department:""}),ie={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{pattern:/^[a-zA-Z0-9]+$/,message:"仅限字母和数字",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:8,message:"至少8位",trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"需含大小写字母和数字",trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"必须是中文",trigger:"blur"}],role:[{required:!0,message:"请选择类型",trigger:"change"}],department:[{required:!0,message:"请选择部门",trigger:"change"}]},x=r(!1),j=r(),u=K({username:"",real_name:"",role:"",department:""}),me={real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5]+$/,message:"必须是中文",trigger:"blur"}],role:[{required:!0,message:"请选择类型",trigger:"change"}],department:[{required:!0,message:"请选择部门",trigger:"change"}]},A=r(!1),ae=r(""),C=r(!1),H=r(),F=K({department:""}),pe={department:[{required:!0,message:"请输入部门名称",trigger:"blur"}]},y=r(""),R=r(1),T=r(10),ve=N(()=>O.value.length),fe=N(()=>{const s=(R.value-1)*T.value;return O.value.slice(s,s+T.value)}),P=r(1),q=r(10),ge=N(()=>S.value.length),ce=N(()=>{const s=(P.value-1)*q.value;return S.value.slice(s,s+q.value)}),z=async()=>{const s=await c.post("/api/user/list");s.data.success?O.value=s.data.users:o.error(s.data.message||"获取用户失败")},J=async()=>{const s=await c.post("/api/get_departments");s.data&&Array.isArray(s.data.departments)?(B.value=s.data.departments,S.value=s.data.departments.map(e=>({department:e}))):(B.value=[],S.value=[])},_e=()=>{Z.value&&Z.value.validate(async s=>{if(s){const e=await c.post("/api/user/add",p);e.data.success?(o.success("新增用户成功"),U.value=!1,z()):o.error(e.data.message||"新增用户失败")}})},be=s=>{u.username=s.username,u.real_name=s.real_name,u.role=s.role,u.department=s.department,x.value=!0},we=()=>{j.value&&j.value.validate(async s=>{if(s){const e=await c.post("/api/user/update",u);e.data.success?(o.success("修改成功"),x.value=!1,z()):o.error(e.data.message||"修改失败")}})},ye=s=>{if(s.username==="admin"){o.warning("admin账号禁止删除");return}if(s.role==="admin"&&s.username!=="admin"){o.warning("禁止删除其他管理员账号");return}te.confirm(`确定要删除用户【${s.username}】吗？`,"二次确认",{type:"warning",confirmButtonText:"删除",cancelButtonText:"取消"}).then(async()=>{const e=await c.post("/api/user/delete",{username:s.username});e.data.success?(o.success("删除成功"),z()):o.error(e.data.message||"删除失败")}).catch(()=>{})},Ve=async s=>{if(s.username===y.value){o.warning("不能重置自己密码");return}if(s.role==="admin"&&s.username!=="admin"){o.warning("禁止重置其他管理员密码");return}te.confirm(`确定要重置用户【${s.username}】的密码吗？`,"二次确认",{type:"warning",confirmButtonText:"重置",cancelButtonText:"取消"}).then(async()=>{const e=await c.post("/api/user/reset_pwd",{username:s.username});e.data.success?(ae.value=e.data.new_password,A.value=!0):o.error(e.data.message||"重置失败")}).catch(()=>{})},ke=()=>{H.value&&H.value.validate(async s=>{if(s){const e=await c.post("/api/department/add",F);e.data.success?(o.success("新增部门成功"),C.value=!1,J()):o.error(e.data.message||"新增部门失败")}})},Ue=s=>{te.confirm(`确定要删除部门【${s.department}】吗？`,"二次确认",{type:"warning",confirmButtonText:"删除",cancelButtonText:"取消"}).then(async()=>{const e=await c.post("/api/department/delete",{department:s.department});e.data.success?(o.success("删除成功"),J(),z()):o.error(e.data.message||"删除失败")}).catch(()=>{})};return $e(()=>{try{const s=sessionStorage.getItem("user");if(s){const e=JSON.parse(s);y.value=e.username}}catch{y.value=""}z(),J()}),(s,e)=>{const f=i("el-icon"),m=i("el-button"),g=i("el-table-column"),xe=i("el-tag"),le=i("el-tooltip"),se=i("el-table"),ne=i("el-pagination"),Ce=i("OfficeBuilding"),V=i("el-input"),v=i("el-form-item"),k=i("el-option"),L=i("el-select"),G=i("el-form"),M=i("el-dialog");return b(),I("div",Pe,[n("div",qe,[n("div",Le,[n("div",Me,[(b(),I(Q,null,W(oe,t=>n("div",{key:t.key,class:X(["nav-item",{active:w.value===t.key,disabled:$.value}]),onClick:E=>ue(t.key)},[a(f,null,{default:l(()=>[(b(),Y(De(de(t.key))))]),_:2},1024),n("span",Ne,h(t.label),1)],10,Ee)),64))])]),n("div",Ie,[n("div",{class:X(["content-section",{active:w.value==="user",prev:D.value==="user"}])},[n("div",Oe,[n("div",Ze,[a(f,{class:"section-icon"},{default:l(()=>[a(_(ee))]),_:1}),e[26]||(e[26]=n("span",null,"用户账号管理",-1))]),a(m,{type:"primary",onClick:e[0]||(e[0]=t=>U.value=!0)},{default:l(()=>[a(f,null,{default:l(()=>[a(_(re))]),_:1}),e[27]||(e[27]=d(" 新增用户 "))]),_:1})]),n("div",je,[a(se,{data:fe.value,border:""},{default:l(()=>[a(g,{prop:"username",label:"用户名",width:"120","show-overflow-tooltip":""}),a(g,{prop:"real_name",label:"真实姓名",width:"100","show-overflow-tooltip":""}),a(g,{prop:"role",label:"类型",width:"150",align:"center"},{default:l(t=>[a(xe,{type:t.row.role==="admin"?"danger":"info",size:"small"},{default:l(()=>[d(h(t.row.role==="admin"?"管理员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),a(g,{prop:"department",label:"部门","min-width":"100","show-overflow-tooltip":""}),a(g,{prop:"created_at",label:"注册时间",width:"200","show-overflow-tooltip":""}),a(g,{label:"操作",width:"250",fixed:"right"},{default:l(t=>[n("div",He,[a(m,{size:"small",type:"primary",onClick:E=>be(t.row)},{default:l(()=>[a(f,null,{default:l(()=>[a(_(Be))]),_:1}),e[28]||(e[28]=d(" 编辑 "))]),_:2},1032,["onClick"]),a(le,{content:t.row.username==="admin"||t.row.username===y.value||t.row.role==="admin"&&t.row.username!=="admin"?"无权限":"重置密码",placement:"top"},{default:l(()=>[n("div",Je,[a(m,{size:"small",type:"warning",onClick:E=>Ve(t.row),disabled:t.row.username==="admin"||t.row.username===y.value||t.row.role==="admin"&&t.row.username!=="admin"},{default:l(()=>[a(f,null,{default:l(()=>[a(_(Se))]),_:1}),e[29]||(e[29]=d(" 重置 "))]),_:2},1032,["onClick","disabled"])])]),_:2},1032,["content"]),a(le,{content:t.row.username==="admin"||t.row.role==="admin"&&t.row.username!=="admin"?"无权限":"删除用户",placement:"top"},{default:l(()=>[n("div",Ge,[a(m,{size:"small",type:"danger",onClick:E=>ye(t.row),disabled:t.row.username==="admin"||t.row.role==="admin"&&t.row.username!=="admin"},{default:l(()=>[a(f,null,{default:l(()=>[a(_(Ae))]),_:1}),e[30]||(e[30]=d(" 删除 "))]),_:2},1032,["onClick","disabled"])])]),_:2},1032,["content"])])]),_:1})]),_:1},8,["data"]),n("div",Ke,[a(ne,{"current-page":R.value,"onUpdate:currentPage":e[1]||(e[1]=t=>R.value=t),"page-size":T.value,"onUpdate:pageSize":e[2]||(e[2]=t=>T.value=t),"page-sizes":[5,10,20,50],total:ve.value,layout:"total, sizes, prev, pager, next, jumper",background:"",onSizeChange:e[3]||(e[3]=t=>R.value=1)},null,8,["current-page","page-size","total"])])])],2),n("div",{class:X(["content-section",{active:w.value==="dept",prev:D.value==="dept"}])},[n("div",Qe,[n("div",We,[a(f,{class:"section-icon"},{default:l(()=>[a(Ce)]),_:1}),e[31]||(e[31]=n("span",null,"部门管理",-1))]),a(m,{type:"success",onClick:e[4]||(e[4]=t=>C.value=!0)},{default:l(()=>[a(f,null,{default:l(()=>[a(_(re))]),_:1}),e[32]||(e[32]=d(" 新增部门 "))]),_:1})]),n("div",Xe,[a(se,{data:ce.value,style:{width:"400px","margin-top":"8px"},border:"",class:"dept-table"},{default:l(()=>[a(g,{prop:"department",label:"部门名称"}),a(g,{label:"操作",width:"100"},{default:l(t=>[a(m,{size:"small",type:"danger",onClick:E=>Ue(t.row)},{default:l(()=>e[33]||(e[33]=[d("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),n("div",Ye,[a(ne,{"current-page":P.value,"onUpdate:currentPage":e[5]||(e[5]=t=>P.value=t),"page-size":q.value,"onUpdate:pageSize":e[6]||(e[6]=t=>q.value=t),"page-sizes":[5,10,20,50],total:ge.value,layout:"total, sizes, prev, pager, next, jumper",background:"",onSizeChange:e[7]||(e[7]=t=>P.value=1)},null,8,["current-page","page-size","total"])])])],2)])]),a(M,{modelValue:U.value,"onUpdate:modelValue":e[14]||(e[14]=t=>U.value=t),title:"新增用户",width:"400px"},{footer:l(()=>[a(m,{onClick:e[13]||(e[13]=t=>U.value=!1)},{default:l(()=>e[34]||(e[34]=[d("取消")])),_:1}),a(m,{type:"primary",onClick:_e},{default:l(()=>e[35]||(e[35]=[d("确定")])),_:1})]),default:l(()=>[a(G,{model:p,rules:ie,ref_key:"addUserFormRef",ref:Z,"label-width":"80px"},{default:l(()=>[a(v,{label:"用户名",prop:"username"},{default:l(()=>[a(V,{modelValue:p.username,"onUpdate:modelValue":e[8]||(e[8]=t=>p.username=t),placeholder:"仅限字母和数字"},null,8,["modelValue"])]),_:1}),a(v,{label:"密码",prop:"password"},{default:l(()=>[a(V,{modelValue:p.password,"onUpdate:modelValue":e[9]||(e[9]=t=>p.password=t),type:"password","show-password":"",placeholder:"至少8位，含大小写字母和数字"},null,8,["modelValue"])]),_:1}),a(v,{label:"真实姓名",prop:"real_name"},{default:l(()=>[a(V,{modelValue:p.real_name,"onUpdate:modelValue":e[10]||(e[10]=t=>p.real_name=t),placeholder:"中文姓名"},null,8,["modelValue"])]),_:1}),a(v,{label:"类型",prop:"role"},{default:l(()=>[a(L,{modelValue:p.role,"onUpdate:modelValue":e[11]||(e[11]=t=>p.role=t),placeholder:"请选择类型"},{default:l(()=>[a(k,{label:"管理员",value:"admin"}),a(k,{label:"普通用户",value:"user"})]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"部门",prop:"department"},{default:l(()=>[a(L,{modelValue:p.department,"onUpdate:modelValue":e[12]||(e[12]=t=>p.department=t),placeholder:"请选择部门"},{default:l(()=>[(b(!0),I(Q,null,W(B.value,t=>(b(),Y(k,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(M,{modelValue:x.value,"onUpdate:modelValue":e[20]||(e[20]=t=>x.value=t),title:"编辑用户",width:"400px"},{footer:l(()=>[a(m,{onClick:e[19]||(e[19]=t=>x.value=!1)},{default:l(()=>e[36]||(e[36]=[d("取消")])),_:1}),a(m,{type:"primary",onClick:we},{default:l(()=>e[37]||(e[37]=[d("保存")])),_:1})]),default:l(()=>[a(G,{model:u,rules:me,ref_key:"editUserFormRef",ref:j,"label-width":"80px"},{default:l(()=>[a(v,{label:"用户名"},{default:l(()=>[a(V,{modelValue:u.username,"onUpdate:modelValue":e[15]||(e[15]=t=>u.username=t),disabled:""},null,8,["modelValue"])]),_:1}),a(v,{label:"真实姓名",prop:"real_name"},{default:l(()=>[a(V,{modelValue:u.real_name,"onUpdate:modelValue":e[16]||(e[16]=t=>u.real_name=t)},null,8,["modelValue"])]),_:1}),a(v,{label:"类型",prop:"role"},{default:l(()=>[a(L,{modelValue:u.role,"onUpdate:modelValue":e[17]||(e[17]=t=>u.role=t),disabled:y.value!=="admin"},{default:l(()=>[a(k,{label:"管理员",value:"admin"}),a(k,{label:"普通用户",value:"user"})]),_:1},8,["modelValue","disabled"])]),_:1}),a(v,{label:"部门",prop:"department"},{default:l(()=>[a(L,{modelValue:u.department,"onUpdate:modelValue":e[18]||(e[18]=t=>u.department=t)},{default:l(()=>[(b(!0),I(Q,null,W(B.value,t=>(b(),Y(k,{key:t,label:t,value:t},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(M,{modelValue:A.value,"onUpdate:modelValue":e[22]||(e[22]=t=>A.value=t),title:"重置密码",width:"350px"},{default:l(()=>[n("div",he,[a(f,{style:{"font-size":"32px",color:"#67c23a"}},{default:l(()=>[a(_(Fe))]),_:1}),n("div",et,[e[38]||(e[38]=d("新密码：")),n("span",tt,h(ae.value),1)]),e[40]||(e[40]=n("div",{style:{color:"#f56c6c","margin-bottom":"12px"}},"请妥善保存新密码，页面关闭后将无法再次查看！",-1)),a(m,{type:"primary",onClick:e[21]||(e[21]=t=>A.value=!1)},{default:l(()=>e[39]||(e[39]=[d("知道了")])),_:1})])]),_:1},8,["modelValue"]),a(M,{modelValue:C.value,"onUpdate:modelValue":e[25]||(e[25]=t=>C.value=t),title:"新增部门",width:"350px"},{footer:l(()=>[a(m,{onClick:e[24]||(e[24]=t=>C.value=!1)},{default:l(()=>e[41]||(e[41]=[d("取消")])),_:1}),a(m,{type:"primary",onClick:ke},{default:l(()=>e[42]||(e[42]=[d("确定")])),_:1})]),default:l(()=>[a(G,{model:F,rules:pe,ref_key:"addDeptFormRef",ref:H,"label-width":"80px"},{default:l(()=>[a(v,{label:"部门名称",prop:"department"},{default:l(()=>[a(V,{modelValue:F.department,"onUpdate:modelValue":e[23]||(e[23]=t=>F.department=t),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),nt=Te(at,[["__scopeId","data-v-d69e9039"]]);export{nt as default};

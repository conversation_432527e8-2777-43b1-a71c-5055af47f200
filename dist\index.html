<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/gsi/favicon.ico">
    <link href="//at.alicdn.com/t/c/font_4447428_7gyy6n3qpg.css" rel="stylesheet">
    <style>
      link[rel="icon"] {
        filter: invert(1);
      }
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeoStream</title>
    <script>
      (function() {
        try {
          var xhr = new XMLHttpRequest();
          var apiUrl = window.location.pathname.startsWith('/gsi/')
            ? '/gsi/api/get_default_theme'
            : '/api/get_default_theme';
          xhr.open('POST', apiUrl, false); // 同步请求
          xhr.setRequestHeader('Content-Type', 'application/json');
          xhr.send('{}');
          if (xhr.status === 200) {
            var res = JSON.parse(xhr.responseText);
            if (res && res.success && res.default_theme) {
              document.body.className = 'theme-' + res.default_theme;
            }
          }
        } catch (e) {}
      })();
    </script>
    <script type="module" crossorigin src="/gsi/assets/index-E0SsINqw.js"></script>
    <link rel="stylesheet" crossorigin href="/gsi/assets/index-CsTf23g5.css">
  </head>
  <body>
    <div id="theme-loading" style="height:100vh;display:flex;align-items:center;justify-content:center;">
    </div>
    <div id="app"></div>
  </body>
</html> 
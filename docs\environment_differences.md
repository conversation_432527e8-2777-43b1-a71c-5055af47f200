# 开发环境与生产环境配置差异说明

## 1. 服务器配置差异

### 开发环境
```xml
<Server>
    <Host>*************</Host>
    <Port>9997</Port>
    <Debug>true</Debug>
</Server>

<Frontend>
    <IP>*************</IP>
    <Port>9996</Port>
</Frontend>
```

### 生产环境
```xml
<Server>
    <Host>生产环境IP</Host>
    <Port>9997</Port>
    <Debug>false</Debug>
</Server>

<Frontend>
    <IP>生产环境IP</IP>
    <Port>9996</Port>
</Frontend>
```

## 2. CORS配置差异

### 开发环境
- 允许的源包括：
  - http://localhost:5173
  - http://127.0.0.1:5173
  - http://*************:9996
  - http://localhost:9996
  - http://127.0.0.1:9996
  - http://*************:5173
  - http://*************:9997
  - http://localhost:9997
  - http://127.0.0.1:9997

### 生产环境
- 需要添加的源：
  - http://生产环境域名:9996
  - http://生产环境域名:9997
  - https://生产环境域名:9996（如果使用HTTPS）
  - https://生产环境域名:9997（如果使用HTTPS）

## 3. 文件路径配置差异

### 开发环境
- FME路径：`C:\Program Files\FME\fme.exe`
- 数据库路径：`GeoStream.db`
- 文件上传路径：相对路径 `models/{fmw_id}/`

### 生产环境
- FME路径：需要根据实际安装路径配置
- 数据库路径：需要配置绝对路径
- 文件上传路径：需要配置绝对路径

## 4. 调试模式差异

### 开发环境
- Debug模式：开启
- 日志级别：DEBUG
- 错误信息：详细显示

### 生产环境
- Debug模式：关闭
- 日志级别：INFO或ERROR
- 错误信息：简化显示

## 5. 安全配置差异

### 开发环境
- 允许所有CORS源
- 支持所有HTTP方法
- 允许所有请求头

### 生产环境
- 只允许特定域名
- 限制HTTP方法
- 限制请求头

## 6. 性能配置差异

### 开发环境
- 无并发限制
- 无超时限制
- 无文件大小限制

### 生产环境
- 需要配置并发限制
- 需要配置超时限制
- 需要配置文件大小限制

## 7. 数据库配置差异

### 开发环境
- 使用本地SQLite数据库
- 无连接池
- 无备份机制

### 生产环境
- 建议使用MySQL/PostgreSQL
- 需要配置连接池
- 需要配置备份机制

## 8. 日志配置差异

### 开发环境
- 日志输出到控制台
- 详细日志记录
- 无日志轮转

### 生产环境
- 日志输出到文件
- 关键日志记录
- 需要配置日志轮转

## 9. 缓存配置差异

### 开发环境
- 无缓存
- 无CDN
- 无静态资源优化

### 生产环境
- 需要配置缓存
- 建议使用CDN
- 需要静态资源优化

## 10. 监控配置差异

### 开发环境
- 无监控
- 无告警
- 无性能指标

### 生产环境
- 需要配置监控
- 需要配置告警
- 需要配置性能指标

## 注意事项

1. 部署前检查清单：
   - [ ] 更新所有IP地址为生产环境IP
   - [ ] 更新CORS配置
   - [ ] 关闭调试模式
   - [ ] 配置正确的文件路径
   - [ ] 配置安全限制
   - [ ] 配置性能参数
   - [ ] 配置日志系统
   - [ ] 配置监控系统

2. 常见问题：
   - 文件上传失败：检查文件路径权限
   - API请求失败：检查CORS配置
   - 工具更新失败：检查文件系统权限
   - 数据库操作失败：检查数据库连接配置

3. 建议：
   - 使用环境变量管理配置
   - 实现配置热重载
   - 添加健康检查接口
   - 实现优雅降级机制 
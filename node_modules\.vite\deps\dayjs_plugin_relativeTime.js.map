{"version": 3, "sources": ["../../dayjs/plugin/relativeTime.js"], "sourcesContent": ["!function(r,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(r=\"undefined\"!=typeof globalThis?globalThis:r||self).dayjs_plugin_relativeTime=e()}(this,(function(){\"use strict\";return function(r,e,t){r=r||{};var n=e.prototype,o={future:\"in %s\",past:\"%s ago\",s:\"a few seconds\",m:\"a minute\",mm:\"%d minutes\",h:\"an hour\",hh:\"%d hours\",d:\"a day\",dd:\"%d days\",M:\"a month\",MM:\"%d months\",y:\"a year\",yy:\"%d years\"};function i(r,e,t,o){return n.fromToBase(r,e,t,o)}t.en.relativeTime=o,n.fromToBase=function(e,n,i,d,u){for(var f,a,s,l=i.$locale().relativeTime||o,h=r.thresholds||[{l:\"s\",r:44,d:\"second\"},{l:\"m\",r:89},{l:\"mm\",r:44,d:\"minute\"},{l:\"h\",r:89},{l:\"hh\",r:21,d:\"hour\"},{l:\"d\",r:35},{l:\"dd\",r:25,d:\"day\"},{l:\"M\",r:45},{l:\"MM\",r:10,d:\"month\"},{l:\"y\",r:17},{l:\"yy\",d:\"year\"}],m=h.length,c=0;c<m;c+=1){var y=h[c];y.d&&(f=d?t(e).diff(i,y.d,!0):i.diff(e,y.d,!0));var p=(r.rounding||Math.round)(Math.abs(f));if(s=f>0,p<=y.r||!y.r){p<=1&&c>0&&(y=h[c-1]);var v=l[y.l];u&&(p=u(\"\"+p)),a=\"string\"==typeof v?v.replace(\"%d\",p):v(p,n,y.l,s);break}}if(n)return a;var M=s?l.future:l.past;return\"function\"==typeof M?M(a):M.replace(\"%s\",a)},n.to=function(r,e){return i(r,e,this,!0)},n.from=function(r,e){return i(r,e,this)};var d=function(r){return r.$u?t.utc():t()};n.toNow=function(r){return this.to(d(this),r)},n.fromNow=function(r){return this.from(d(this),r)}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,4BAA0B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,YAAE,KAAG,CAAC;AAAE,YAAI,IAAE,EAAE,WAAU,IAAE,EAAC,QAAO,SAAQ,MAAK,UAAS,GAAE,iBAAgB,GAAE,YAAW,IAAG,cAAa,GAAE,WAAU,IAAG,YAAW,GAAE,SAAQ,IAAG,WAAU,GAAE,WAAU,IAAG,aAAY,GAAE,UAAS,IAAG,WAAU;AAAE,iBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAO,EAAE,WAAWH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,QAAC;AAAC,UAAE,GAAG,eAAa,GAAE,EAAE,aAAW,SAASF,IAAEG,IAAEC,IAAEC,IAAE,GAAE;AAAC,mBAAQ,GAAE,GAAE,GAAE,IAAED,GAAE,QAAQ,EAAE,gBAAc,GAAE,IAAE,EAAE,cAAY,CAAC,EAAC,GAAE,KAAI,GAAE,IAAG,GAAE,SAAQ,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,SAAQ,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,OAAM,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,MAAK,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,IAAG,GAAE,QAAO,GAAE,EAAC,GAAE,KAAI,GAAE,GAAE,GAAE,EAAC,GAAE,MAAK,GAAE,OAAM,CAAC,GAAE,IAAE,EAAE,QAAO,IAAE,GAAE,IAAE,GAAE,KAAG,GAAE;AAAC,gBAAI,IAAE,EAAE,CAAC;AAAE,cAAE,MAAI,IAAEC,KAAE,EAAEL,EAAC,EAAE,KAAKI,IAAE,EAAE,GAAE,IAAE,IAAEA,GAAE,KAAKJ,IAAE,EAAE,GAAE,IAAE;AAAG,gBAAI,KAAG,EAAE,YAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AAAE,gBAAG,IAAE,IAAE,GAAE,KAAG,EAAE,KAAG,CAAC,EAAE,GAAE;AAAC,mBAAG,KAAG,IAAE,MAAI,IAAE,EAAE,IAAE,CAAC;AAAG,kBAAI,IAAE,EAAE,EAAE,CAAC;AAAE,oBAAI,IAAE,EAAE,KAAG,CAAC,IAAG,IAAE,YAAU,OAAO,IAAE,EAAE,QAAQ,MAAK,CAAC,IAAE,EAAE,GAAEG,IAAE,EAAE,GAAE,CAAC;AAAE;AAAA,YAAK;AAAA,UAAC;AAAC,cAAGA,GAAE,QAAO;AAAE,cAAI,IAAE,IAAE,EAAE,SAAO,EAAE;AAAK,iBAAM,cAAY,OAAO,IAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,MAAK,CAAC;AAAA,QAAC,GAAE,EAAE,KAAG,SAASJ,IAAEC,IAAE;AAAC,iBAAO,EAAED,IAAEC,IAAE,MAAK,IAAE;AAAA,QAAC,GAAE,EAAE,OAAK,SAASD,IAAEC,IAAE;AAAC,iBAAO,EAAED,IAAEC,IAAE,IAAI;AAAA,QAAC;AAAE,YAAI,IAAE,SAASD,IAAE;AAAC,iBAAOA,GAAE,KAAG,EAAE,IAAI,IAAE,EAAE;AAAA,QAAC;AAAE,UAAE,QAAM,SAASA,IAAE;AAAC,iBAAO,KAAK,GAAG,EAAE,IAAI,GAAEA,EAAC;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,iBAAO,KAAK,KAAK,EAAE,IAAI,GAAEA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["r", "e", "t", "o", "n", "i", "d"]}
{"name": "@types/xml2js", "version": "0.4.14", "description": "TypeScript definitions for xml2js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/xml2js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/michelsalib"}, {"name": "<PERSON>", "githubUsername": "jason<PERSON>", "url": "https://github.com/jasonrm"}, {"name": "<PERSON>", "githubUsername": "ccurrens", "url": "https://github.com/ccurrens"}, {"name": "<PERSON>", "githubUsername": "ed<PERSON><PERSON><PERSON>", "url": "https://github.com/edwardhinkle"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/claasahl"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "redlick<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/redlickigrzegorz"}, {"name": "<PERSON>", "githubUsername": "72636c", "url": "https://github.com/72636c"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/xml2js"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "a43b2f37a10e3e13f691aed45f7417c7ceb8d61e5fbc7626e6a0dad5d1917c94", "typeScriptVersion": "4.5"}
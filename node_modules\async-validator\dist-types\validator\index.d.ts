declare const _default: {
    string: import("..").ExecuteValidator;
    method: import("..").ExecuteValidator;
    number: import("..").ExecuteValidator;
    boolean: import("..").ExecuteValidator;
    regexp: import("..").ExecuteValidator;
    integer: import("..").ExecuteValidator;
    float: import("..").ExecuteValidator;
    array: import("..").ExecuteValidator;
    object: import("..").ExecuteValidator;
    enum: import("..").ExecuteValidator;
    pattern: import("..").ExecuteValidator;
    date: import("..").ExecuteValidator;
    url: import("..").ExecuteValidator;
    hex: import("..").ExecuteValidator;
    email: import("..").ExecuteValidator;
    required: import("..").ExecuteValidator;
    any: import("..").ExecuteValidator;
};
export default _default;

// This file is generated automatically by `scripts/build/fp.ts`. Please, don't change it.

import { differenceInCalendarISOWeekYears as fn } from "../differenceInCalendarISOWeekYears.js";
import { convertToFP } from "./_lib/convertToFP.js";

export const differenceInCalendarISOWeekYearsWithOptions = convertToFP(fn, 3);

// Fallback for modularized imports:
export default differenceInCalendarISOWeekYearsWithOptions;

"use strict";
exports.frCA = void 0;
var _index = require("./fr/_lib/formatDistance.cjs");
var _index2 = require("./fr/_lib/formatRelative.cjs");
var _index3 = require("./fr/_lib/localize.cjs");
var _index4 = require("./fr/_lib/match.cjs");

var _index5 = require("./fr-CA/_lib/formatLong.cjs"); // Same as fr
// Unique for fr-CA
/**
 * @category Locales
 * @summary French locale (Canada).
 * @language French
 * @iso-639-2 fra
 * <AUTHOR> [@izeau](https://github.com/izeau)
 * <AUTHOR> [@fbonzon](https://github.com/fbonzon)
 * <AUTHOR> [@gpetrioli](https://github.com/gpetrioli)
 */
const frCA = (exports.frCA = {
  code: "fr-CA",
  formatDistance: _index.formatDistance,
  formatLong: _index5.formatLong,
  formatRelative: _index2.formatRelative,
  localize: _index3.localize,
  match: _index4.match,

  // Unique for fr-CA
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});

import { formatDistance } from "./ht/_lib/formatDistance.js";
import { formatLong } from "./ht/_lib/formatLong.js";
import { formatRelative } from "./ht/_lib/formatRelative.js";
import { localize } from "./ht/_lib/localize.js";
import { match } from "./ht/_lib/match.js";

/**
 * @category Locales
 * @summary Haitian Creole locale.
 * @language Haitian Creole
 * @iso-639-2 hat
 * <AUTHOR> [@rmariuzzo](https://github.com/rmariuzzo)
 * <AUTHOR> [@watsongm24](https://github.com/watsongm24)
 */
export const ht = {
  code: "ht",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default ht;

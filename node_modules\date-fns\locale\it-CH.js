import { formatDistance } from "./it/_lib/formatDistance.js";
import { formatRelative } from "./it/_lib/formatRelative.js";
import { localize } from "./it/_lib/localize.js";
import { match } from "./it/_lib/match.js";
import { formatLong } from "./it-CH/_lib/formatLong.js";

/**
 * @category Locales
 * @summary Italian locale (Switzerland).
 * @language Italian
 * @iso-639-2 ita
 * <AUTHOR> [@maic66](https://github.com/maic66)
 */
export const itCH = {
  code: "it-CH",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default itCH;

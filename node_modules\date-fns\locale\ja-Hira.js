import { formatDistance } from "./ja-<PERSON>ra/_lib/formatDistance.js";
import { formatLong } from "./ja-Hira/_lib/formatLong.js";
import { formatRelative } from "./ja-Hira/_lib/formatRelative.js";
import { localize } from "./ja-Hira/_lib/localize.js";
import { match } from "./ja-Hira/_lib/match.js";

/**
 * @category Locales
 * @summary Japanese (Hiragana) locale.
 * @language Japanese (Hiragana)
 * @iso-639-2 jpn
 * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)
 */
export const jaHira = {
  code: "ja-<PERSON>ra",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default jaHira;

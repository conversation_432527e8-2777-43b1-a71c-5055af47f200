"use strict";
exports.mt = void 0;
var _index = require("./mt/_lib/formatDistance.cjs");
var _index2 = require("./mt/_lib/formatLong.cjs");
var _index3 = require("./mt/_lib/formatRelative.cjs");
var _index4 = require("./mt/_lib/localize.cjs");
var _index5 = require("./mt/_lib/match.cjs");

/**
 * @category Locales
 * @summary Maltese locale.
 * @language Maltese
 * @iso-639-2 mlt
 * <AUTHOR> [@amatzon](@link https://github.com/amatzon)
 * <AUTHOR> [@bryanMt](@link https://github.com/bryanMt)
 */
const mt = (exports.mt = {
  code: "mt",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});

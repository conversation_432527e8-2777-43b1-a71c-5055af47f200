"use strict";
exports.pt = void 0;
var _index = require("./pt/_lib/formatDistance.cjs");
var _index2 = require("./pt/_lib/formatLong.cjs");
var _index3 = require("./pt/_lib/formatRelative.cjs");
var _index4 = require("./pt/_lib/localize.cjs");
var _index5 = require("./pt/_lib/match.cjs");

/**
 * @category Locales
 * @summary Portuguese locale.
 * @language Portuguese
 * @iso-639-2 por
 * <AUTHOR> [@dfreire](https://github.com/dfreire)
 * <AUTHOR> [@adrm](https://github.com/adrm)
 */
const pt = (exports.pt = {
  code: "pt",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});

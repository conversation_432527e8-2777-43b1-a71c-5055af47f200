{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/infinite-scroll/index.ts"], "sourcesContent": ["import InfiniteScroll from './src'\n\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nconst _InfiniteScroll = InfiniteScroll as SFCWithInstall<typeof InfiniteScroll>\n\n_InfiniteScroll.install = (app: App) => {\n  app.directive('InfiniteScroll', _InfiniteScroll)\n}\n\nexport default _InfiniteScroll\nexport const ElInfiniteScroll = _InfiniteScroll\n"], "names": [], "mappings": ";;AACK,MAAC,eAAe,GAAG,eAAe;AACvC,eAAe,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AACnC,EAAE,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;AACnD,CAAC,CAAC;AAEU,MAAC,gBAAgB,GAAG;;;;"}
{"version": 3, "file": "use-menu-css-var.mjs", "sources": ["../../../../../../packages/components/menu/src/use-menu-css-var.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport useMenuColor from './use-menu-color'\n\nimport type { MenuProps } from './menu'\n\nexport const useMenuCssVar = (props: MenuProps, level: number) => {\n  const ns = useNamespace('menu')\n  return computed(() =>\n    ns.cssVarBlock({\n      'text-color': props.textColor || '',\n      'hover-text-color': props.textColor || '',\n      'bg-color': props.backgroundColor || '',\n      'hover-bg-color': useMenuColor(props).value || '',\n      'active-color': props.activeTextColor || '',\n      level: `${level}`,\n    })\n  )\n}\n"], "names": [], "mappings": ";;;;AAGY,MAAC,aAAa,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC/C,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AAClC,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC;AACvC,IAAI,YAAY,EAAE,KAAK,CAAC,SAAS,IAAI,EAAE;AACvC,IAAI,kBAAkB,EAAE,KAAK,CAAC,SAAS,IAAI,EAAE;AAC7C,IAAI,UAAU,EAAE,KAAK,CAAC,eAAe,IAAI,EAAE;AAC3C,IAAI,gBAAgB,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE;AACrD,IAAI,cAAc,EAAE,KAAK,CAAC,eAAe,IAAI,EAAE;AAC/C,IAAI,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACrB,GAAG,CAAC,CAAC,CAAC;AACN;;;;"}
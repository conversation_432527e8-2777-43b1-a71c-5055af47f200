{"version": 3, "file": "message2.mjs", "sources": ["../../../../../../packages/components/message/src/message.vue"], "sourcesContent": ["<template>\n  <transition\n    :name=\"ns.b('fade')\"\n    @before-enter=\"isStartTransition = true\"\n    @before-leave=\"onClose\"\n    @after-leave=\"$emit('destroy')\"\n  >\n    <div\n      v-show=\"visible\"\n      :id=\"id\"\n      ref=\"messageRef\"\n      :class=\"[\n        ns.b(),\n        { [ns.m(type)]: type },\n        ns.is('closable', showClose),\n        ns.is('plain', plain),\n        customClass,\n      ]\"\n      :style=\"customStyle\"\n      role=\"alert\"\n      @mouseenter=\"clearTimer\"\n      @mouseleave=\"startTimer\"\n    >\n      <el-badge\n        v-if=\"repeatNum > 1\"\n        :value=\"repeatNum\"\n        :type=\"badgeType\"\n        :class=\"ns.e('badge')\"\n      />\n      <el-icon v-if=\"iconComponent\" :class=\"[ns.e('icon'), typeClass]\">\n        <component :is=\"iconComponent\" />\n      </el-icon>\n      <slot>\n        <p v-if=\"!dangerouslyUseHTMLString\" :class=\"ns.e('content')\">\n          {{ message }}\n        </p>\n        <!-- Caution here, message could've been compromised, never use user's input as message -->\n        <p v-else :class=\"ns.e('content')\" v-html=\"message\" />\n      </slot>\n      <el-icon v-if=\"showClose\" :class=\"ns.e('closeBtn')\" @click.stop=\"close\">\n        <Close />\n      </el-icon>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, watch } from 'vue'\nimport { useEventListener, useResizeObserver, useTimeoutFn } from '@vueuse/core'\nimport { TypeComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport ElBadge from '@element-plus/components/badge'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { messageEmits, messageProps } from './message'\nimport { getLastOffset, getOffsetOrSpace } from './instance'\nimport type { BadgeProps } from '@element-plus/components/badge'\nimport type { CSSProperties } from 'vue'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElMessage',\n})\n\nconst props = defineProps(messageProps)\nconst emit = defineEmits(messageEmits)\n\nconst isStartTransition = ref(false)\n\nconst { ns, zIndex } = useGlobalComponentSettings('message')\nconst { currentZIndex, nextZIndex } = zIndex\n\nconst messageRef = ref<HTMLDivElement>()\nconst visible = ref(false)\nconst height = ref(0)\n\nlet stopTimer: (() => void) | undefined = undefined\n\nconst badgeType = computed<BadgeProps['type']>(() =>\n  props.type ? (props.type === 'error' ? 'danger' : props.type) : 'info'\n)\nconst typeClass = computed(() => {\n  const type = props.type\n  return { [ns.bm('icon', type)]: type && TypeComponentsMap[type] }\n})\nconst iconComponent = computed(\n  () => props.icon || TypeComponentsMap[props.type] || ''\n)\n\nconst lastOffset = computed(() => getLastOffset(props.id))\nconst offset = computed(\n  () => getOffsetOrSpace(props.id, props.offset) + lastOffset.value\n)\nconst bottom = computed(() => height.value + offset.value)\nconst customStyle = computed<CSSProperties>(() => ({\n  top: `${offset.value}px`,\n  zIndex: currentZIndex.value,\n}))\n\nfunction startTimer() {\n  if (props.duration === 0) return\n  ;({ stop: stopTimer } = useTimeoutFn(() => {\n    close()\n  }, props.duration))\n}\n\nfunction clearTimer() {\n  stopTimer?.()\n}\n\nfunction close() {\n  visible.value = false\n\n  // if the message has never started a transition, we can destroy it immediately\n  nextTick(() => {\n    if (!isStartTransition.value) {\n      props.onClose?.()\n      emit('destroy')\n    }\n  })\n}\n\nfunction keydown({ code }: KeyboardEvent) {\n  if (code === EVENT_CODE.esc) {\n    // press esc to close the message\n    close()\n  }\n}\n\nonMounted(() => {\n  startTimer()\n  nextZIndex()\n  visible.value = true\n})\n\nwatch(\n  () => props.repeatNum,\n  () => {\n    clearTimer()\n    startTimer()\n  }\n)\n\nuseEventListener(document, 'keydown', keydown)\n\nuseResizeObserver(messageRef, () => {\n  height.value = messageRef.value!.getBoundingClientRect().height\n})\n\ndefineExpose({\n  visible,\n  bottom,\n  close,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_Transition", "_unref"], "mappings": ";;;;;;;;;;;mCA6Dc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAM,MAAA,EAAE,OAAU,GAAA,cAAA,CAAA;AASlB,IAAM,MAAA,iBAAA,GAAoB,IAAI,KAAK,CAAA,CAAA;AAEnC,IAAA,MAAM,EAAE,EAAA,EAAI,MAAO,EAAA,GAAI,2BAA2B,SAAS,CAAA,CAAA;AAC3D,IAAM,MAAA,EAAE,aAAe,EAAA,UAAA,EAAe,GAAA,MAAA,CAAA;AAEtC,IAAA,MAAM,aAAa,GAAoB,EAAA,CAAA;AACvC,IAAM,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA,CAAA;AACzB,IAAM,MAAA,MAAA,GAAS,IAAI,CAAC,CAAA,CAAA;AAEpB,IAAA,IAAI,SAAsC,GAAA,KAAA,CAAA,CAAA;AAE1C,IAAA,MAAM,SAAY,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,KAAA,OAAA,GAAA,QAAA,GAAA,KAAA,CAAA,IAAA,GAAA,MAAA,CAAA,CAAA;AAAA,IAA6B,MAAA,SAC/B,GAAA,QAAA,CAAM;AAA4C,MAClE,MAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AACA,MAAM,OAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAY,YAAe,CAAA,GAAA,IAAA,IAAA,iBAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AAC/B,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,aAAS,GAAA,QAAY,CAAC,MAAG,KAAQ,CAAkB,IAAA,IAAA,iBAAM,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IAClE,MAAC,UAAA,GAAA,QAAA,CAAA,MAAA,aAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACD,IAAA,MAAM,MAAgB,GAAA,QAAA,CAAA,MAAA,gBAAA,CAAA,KAAA,CAAA,EAAA,EAAA,KAAA,CAAA,MAAA,CAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAAA,YACR,GAAA,QAA0B,CAAA,MAAA,MAAA,CAAA,KAAA,GAAA,MAAe,CAAA,KAAA,CAAA,CAAA;AAAA,IACvD,MAAA,WAAA,GAAA,QAAA,CAAA,OAAA;AAEA,MAAA,GAAA,iBAA4B,CAAA,EAAA,CAAA;AAC5B,MAAA,MAAe,EAAA,aAAA,CAAA,KAAA;AAAA,KAAA,CACb;AAA4D,IAC9D,SAAA,UAAA,GAAA;AACA,MAAA,IAAM,cAAkB,KAAA,CAAA;AACxB,QAAM,OAAA;AAA6C,MACjD,CAAA,EAAA,IAAQ,EAAA,SAAY,EAAA,GAAA,YAAA,CAAA,MAAA;AAAA,eACE,CAAA;AAAA,OACtB,EAAA,KAAA,CAAA,QAAA,CAAA,EAAA;AAEF,KAAA;AACE,IAAI,SAAA;AACH,MAAA,SAAS,IAAU,IAAA,GAAA,kBAAuB,EAAA,CAAA;AACzC,KAAM;AAAA,IACR,cAAiB,GAAA;AAAA,MACnB,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,QAAsB,CAAA,MAAA;AACpB,QAAY,IAAA,EAAA,CAAA;AAAA,QACd,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA;AAEA,UAAA,CAAA,EAAS,GAAQ,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACf,UAAA,IAAQ,CAAQ,SAAA,CAAA,CAAA;AAGhB,SAAA;AACE,OAAI,CAAA,CAAA;AACF,KAAA;AACA,IAAA,SAAA,OAAc,CAAA,EAAA,IAAA,EAAA,EAAA;AAAA,MAChB,IAAA,IAAA,KAAA,UAAA,CAAA,GAAA,EAAA;AAAA,QACD,KAAA,EAAA,CAAA;AAAA,OACH;AAEA,KAAS;AACP,IAAI,SAAA,CAAA;AAEF,MAAM,UAAA,EAAA,CAAA;AAAA,MACR,UAAA,EAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAW,KAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;AACX,MAAW,UAAA,EAAA,CAAA;AACX,MAAA,UAAgB,EAAA,CAAA;AAAA,KACjB,CAAA,CAAA;AAED,IAAA,gBAAA,CAAA,QAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA;AAAA,IAAA,iBACc,CAAA,UAAA,EAAA,MAAA;AAAA,MACZ,MAAM,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,CAAA,MAAA,CAAA;AACJ,KAAW,CAAA,CAAA;AACX,IAAW,MAAA,CAAA;AAAA,MACb,OAAA;AAAA,MACF,MAAA;AAEA,MAAiB,KAAA;AAEjB,KAAA,CAAA,CAAA;AACE,IAAA,OAAA,CAAA,IAAe,EAAA,MAAA,KAAA;AAA0C,MAC1D,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,UAAA,EAAA;AAED,QAAa,IAAA,EAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACX,aAAA,EAAA,CAAA,MAAA,KAAA,iBAAA,CAAA,KAAA,GAAA,IAAA;AAAA,QACA,aAAA,EAAA,IAAA,CAAA,OAAA;AAAA,QACA,YAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA;AAAA,QACD,SAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
{"version": 3, "file": "next.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/next.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Next from './next.vue'\n\nexport const paginationNextProps = buildProps({\n  disabled: Boolean,\n  currentPage: {\n    type: Number,\n    default: 1,\n  },\n  pageCount: {\n    type: Number,\n    default: 50,\n  },\n  nextText: {\n    type: String,\n  },\n  nextIcon: {\n    type: iconPropType,\n  },\n} as const)\n\nexport type PaginationNextProps = ExtractPropTypes<typeof paginationNextProps>\n\nexport type NextInstance = InstanceType<typeof Next> & unknown\n"], "names": [], "mappings": ";;;AACY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,CAAC;;;;"}
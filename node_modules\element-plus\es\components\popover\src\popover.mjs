import { dropdownProps } from '../../dropdown/src/dropdown.mjs';
import { buildProps } from '../../../utils/vue/props/runtime.mjs';
import { useTooltipTriggerProps } from '../../tooltip/src/trigger.mjs';
import { useTooltipContentProps } from '../../tooltip/src/content.mjs';
import { isBoolean } from '../../../utils/types.mjs';

const popoverProps = buildProps({
  trigger: useTooltipTriggerProps.trigger,
  triggerKeys: useTooltipTriggerProps.triggerKeys,
  placement: dropdownProps.placement,
  disabled: useTooltipTriggerProps.disabled,
  visible: useTooltipContentProps.visible,
  transition: useTooltipContentProps.transition,
  popperOptions: dropdownProps.popperOptions,
  tabindex: dropdownProps.tabindex,
  content: useTooltipContentProps.content,
  popperStyle: useTooltipContentProps.popperStyle,
  popperClass: useTooltipContentProps.popperClass,
  enterable: {
    ...useTooltipContentProps.enterable,
    default: true
  },
  effect: {
    ...useTooltipContentProps.effect,
    default: "light"
  },
  teleported: useTooltipContentProps.teleported,
  title: String,
  width: {
    type: [String, Number],
    default: 150
  },
  offset: {
    type: Number,
    default: void 0
  },
  showAfter: {
    type: Number,
    default: 0
  },
  hideAfter: {
    type: Number,
    default: 200
  },
  autoClose: {
    type: Number,
    default: 0
  },
  showArrow: {
    type: Boolean,
    default: true
  },
  persistent: {
    type: Boolean,
    default: true
  },
  "onUpdate:visible": {
    type: Function
  }
});
const popoverEmits = {
  "update:visible": (value) => isBoolean(value),
  "before-enter": () => true,
  "before-leave": () => true,
  "after-enter": () => true,
  "after-leave": () => true
};

export { popoverEmits, popoverProps };
//# sourceMappingURL=popover.mjs.map

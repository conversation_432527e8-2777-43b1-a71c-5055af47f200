{"version": 3, "file": "arrow.mjs", "sources": ["../../../../../../packages/components/popper/src/arrow.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Arrow from './arrow.vue'\n\nexport const popperArrowProps = buildProps({\n  arrowOffset: {\n    type: Number,\n    default: 5,\n  },\n} as const)\nexport type PopperArrowProps = ExtractPropTypes<typeof popperArrowProps>\n\nexport type PopperArrowInstance = InstanceType<typeof Arrow> & unknown\n\n/** @deprecated use `popperArrowProps` instead, and it will be deprecated in the next major version */\nexport const usePopperArrowProps = popperArrowProps\n\n/** @deprecated use `PopperArrowProps` instead, and it will be deprecated in the next major version */\nexport type UsePopperArrowProps = PopperArrowProps\n\n/** @deprecated use `PopperArrowInstance` instead, and it will be deprecated in the next major version */\nexport type ElPopperArrowInstance = PopperArrowInstance\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,CAAC,EAAE;AACS,MAAC,mBAAmB,GAAG;;;;"}
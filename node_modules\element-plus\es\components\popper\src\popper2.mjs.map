{"version": 3, "file": "popper2.mjs", "sources": ["../../../../../../packages/components/popper/src/popper.vue"], "sourcesContent": ["<template>\n  <slot />\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref } from 'vue'\nimport { POPPER_INJECTION_KEY } from './constants'\nimport { popperProps } from './popper'\n\nimport type { Instance as PopperInstance } from '@popperjs/core'\nimport type { ElPopperInjectionContext } from './constants'\n\ndefineOptions({\n  name: '<PERSON>Pop<PERSON>',\n  inheritAttrs: false,\n})\nconst props = defineProps(popperProps)\n\nconst triggerRef = ref<HTMLElement>()\nconst popperInstanceRef = ref<PopperInstance>()\nconst contentRef = ref<HTMLElement>()\nconst referenceRef = ref<HTMLElement>()\nconst role = computed(() => props.role)\n\nconst popperProvides = {\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n  /**\n   * @description popperjs instance\n   */\n  popperInstanceRef,\n  /**\n   * @description popper content element\n   */\n  contentRef,\n  /**\n   * @description popper reference element\n   */\n  referenceRef,\n  /**\n   * @description role determines how aria attributes are distributed\n   */\n  role,\n} as ElPopperInjectionContext\n\ndefineExpose(popperProvides)\n\nprovide(POPPER_INJECTION_KEY, popperProvides)\n</script>\n"], "names": ["_renderSlot"], "mappings": ";;;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAGA,IAAA,MAAM,aAAa,GAAiB,EAAA,CAAA;AACpC,IAAA,MAAM,oBAAoB,GAAoB,EAAA,CAAA;AAC9C,IAAA,MAAM,aAAa,GAAiB,EAAA,CAAA;AACpC,IAAA,MAAM,eAAe,GAAiB,EAAA,CAAA;AACtC,IAAA,MAAM,IAAO,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,IAAI,CAAA,CAAA;AAEtC,IAAA,MAAM,cAAiB,GAAA;AAAA,MAAA,UAAA;AAAA,MAAA,iBAAA;AAAA,MAAA,UAAA;AAAA,MAIrB,YAAA;AAAA,MAAA,IAAA;AAAA,KAAA,CAAA;AAAA,IAAA,MAAA,CAAA,cAAA,CAAA,CAAA;AAAA,IAIA,OAAA,CAAA,oBAAA,EAAA,cAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,GAIA;AAAA,CAAA,CAAA,CAAA;AAAA,aAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,YAAA,CAAA,CAAA,CAAA;;;;"}
{"version": 3, "file": "bar.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/bar.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Bar from './bar.vue'\n\nexport const barProps = buildProps({\n  always: {\n    type: Boolean,\n    default: true,\n  },\n  minSize: {\n    type: Number,\n    required: true,\n  },\n} as const)\nexport type BarProps = ExtractPropTypes<typeof barProps>\n\nexport type BarInstance = InstanceType<typeof Bar> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,QAAQ,GAAG,UAAU,CAAC;AACnC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,CAAC;;;;"}
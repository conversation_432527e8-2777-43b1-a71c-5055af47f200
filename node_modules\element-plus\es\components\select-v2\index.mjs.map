{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/select-v2/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Select from './src/select.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSelectV2: SFCWithInstall<typeof Select> = withInstall(Select)\nexport default ElSelectV2\n\nexport * from './src/token'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC,MAAM;;;;"}
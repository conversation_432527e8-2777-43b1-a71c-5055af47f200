{"version": 3, "file": "useOption.mjs", "sources": ["../../../../../../packages/components/select/src/useOption.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, toRaw, watch } from 'vue'\nimport { get, isEqual } from 'lodash-unified'\nimport {\n  ensureArray,\n  escapeStringRegexp,\n  isObject,\n  throwError,\n} from '@element-plus/utils'\nimport { selectGroupKey, selectKey } from './token'\nimport { COMPONENT_NAME } from './option'\n\nimport type { OptionInternalInstance, OptionProps, OptionStates } from './type'\n\nexport function useOption(props: OptionProps, states: OptionStates) {\n  // inject\n  const select = inject(selectKey)\n  if (!select) {\n    throwError(COMPONENT_NAME, 'usage: <el-select><el-option /></el-select/>')\n  }\n  const selectGroup = inject(selectGroupKey, { disabled: false })\n\n  // computed\n  const itemSelected = computed(() => {\n    return contains(ensureArray(select.props.modelValue), props.value)\n  })\n\n  const limitReached = computed(() => {\n    if (select.props.multiple) {\n      const modelValue = ensureArray(select.props.modelValue ?? [])\n      return (\n        !itemSelected.value &&\n        modelValue.length >= select.props.multipleLimit &&\n        select.props.multipleLimit > 0\n      )\n    } else {\n      return false\n    }\n  })\n\n  const currentLabel = computed(() => {\n    return props.label || (isObject(props.value) ? '' : props.value)\n  })\n\n  const currentValue = computed(() => {\n    return props.value || props.label || ''\n  })\n\n  const isDisabled = computed(() => {\n    return props.disabled || states.groupDisabled || limitReached.value\n  })\n\n  const instance = getCurrentInstance()! as OptionInternalInstance\n  const contains = <T>(arr: T[] = [], target: T) => {\n    if (!isObject(props.value)) {\n      return arr && arr.includes(target)\n    } else {\n      const valueKey = select.props.valueKey\n      return (\n        arr &&\n        arr.some((item) => {\n          return toRaw(get(item, valueKey)) === get(target, valueKey)\n        })\n      )\n    }\n  }\n\n  const hoverItem = () => {\n    if (!props.disabled && !selectGroup.disabled) {\n      select.states.hoveringIndex = select.optionsArray.indexOf(instance.proxy)\n    }\n  }\n\n  const updateOption = (query: string) => {\n    const regexp = new RegExp(escapeStringRegexp(query), 'i')\n    states.visible = regexp.test(String(currentLabel.value)) || props.created\n  }\n\n  watch(\n    () => currentLabel.value,\n    () => {\n      if (!props.created && !select.props.remote) select.setSelected()\n    }\n  )\n\n  watch(\n    () => props.value,\n    (val, oldVal) => {\n      const { remote, valueKey } = select.props\n      const shouldUpdate = remote ? val !== oldVal : !isEqual(val, oldVal)\n      if (shouldUpdate) {\n        select.onOptionDestroy(oldVal, instance.proxy)\n        select.onOptionCreate(instance.proxy)\n      }\n\n      if (!props.created && !remote) {\n        if (\n          valueKey &&\n          isObject(val) &&\n          isObject(oldVal) &&\n          val[valueKey] === oldVal[valueKey]\n        ) {\n          return\n        }\n        select.setSelected()\n      }\n    }\n  )\n\n  watch(\n    () => selectGroup.disabled,\n    () => {\n      states.groupDisabled = selectGroup.disabled\n    },\n    { immediate: true }\n  )\n\n  return {\n    select,\n    currentLabel,\n    currentValue,\n    itemSelected,\n    isDisabled,\n    hoverItem,\n    updateOption,\n  }\n}\n"], "names": ["ensureArray"], "mappings": ";;;;;;;;AAUO,SAAS,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE;AACzC,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACnC,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,UAAU,CAAC,cAAc,EAAE,8CAA8C,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;AAClE,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,QAAQ,CAACA,SAAW,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACvE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;AAC/B,MAAM,MAAM,UAAU,GAAGA,SAAW,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACvF,MAAM,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC;AACtH,KAAK,MAAM;AACX,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACrE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;AAC5C,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,aAAa,IAAI,YAAY,CAAC,KAAK,CAAC;AACxE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,MAAM,KAAK;AACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAChC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC7C,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;AACvC,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACpE,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;AAClD,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChF,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9D,IAAI,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC;AAC9E,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM;AACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;AAC9C,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;AAC3B,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACzE,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE;AACnC,MAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC,EAAE;AAC/F,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,WAAW,CAAC,QAAQ,EAAE,MAAM;AAC1C,IAAI,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC,QAAQ,CAAC;AAChD,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1B,EAAE,OAAO;AACT,IAAI,MAAM;AACV,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}
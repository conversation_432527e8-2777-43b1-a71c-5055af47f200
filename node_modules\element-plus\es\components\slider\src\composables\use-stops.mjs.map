{"version": 3, "file": "use-stops.mjs", "sources": ["../../../../../../../packages/components/slider/src/composables/use-stops.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { debugWarn } from '@element-plus/utils'\nimport type { CSSProperties, ComputedRef } from 'vue'\nimport type { SliderInitData, SliderProps } from '../slider'\n\ntype Stops = {\n  stops: ComputedRef<number[]>\n  getStopStyle: (position: number) => CSSProperties\n}\n\nexport const useStops = (\n  props: SliderProps,\n  initData: SliderInitData,\n  minValue: ComputedRef<number>,\n  maxValue: ComputedRef<number>\n): Stops => {\n  const stops = computed(() => {\n    if (!props.showStops || props.min > props.max) return []\n    if (props.step === 0) {\n      debugWarn('ElSlider', 'step should not be 0.')\n      return []\n    }\n\n    const stopCount = (props.max - props.min) / props.step\n    const stepWidth = (100 * props.step) / (props.max - props.min)\n    const result = Array.from<number>({ length: stopCount - 1 }).map(\n      (_, index) => (index + 1) * stepWidth\n    )\n\n    if (props.range) {\n      return result.filter((step) => {\n        return (\n          step <\n            (100 * (minValue.value - props.min)) / (props.max - props.min) ||\n          step > (100 * (maxValue.value - props.min)) / (props.max - props.min)\n        )\n      })\n    } else {\n      return result.filter(\n        (step) =>\n          step >\n          (100 * (initData.firstValue - props.min)) / (props.max - props.min)\n      )\n    }\n  })\n\n  const getStopStyle = (position: number): CSSProperties => {\n    return props.vertical\n      ? { bottom: `${position}%` }\n      : { left: `${position}%` }\n  }\n\n  return {\n    stops,\n    getStopStyle,\n  }\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,KAAK;AACjE,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AACjD,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE;AAC1B,MAAM,SAAS,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;AACrD,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC;AAC3D,IAAI,MAAM,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACjE,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC;AACpG,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AACrC,QAAQ,OAAO,IAAI,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1J,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/G,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,CAAC,QAAQ,KAAK;AACrC,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAClF,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}
{"version": 3, "file": "marker.mjs", "sources": ["../../../../../../packages/components/slider/src/marker.ts"], "sourcesContent": ["import { computed, defineComponent, h } from 'vue'\nimport { buildProps, definePropType, isString } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\n\nexport const sliderMarkerProps = buildProps({\n  mark: {\n    type: definePropType<\n      | string\n      | {\n          style: CSSProperties\n          label: any\n        }\n    >([String, Object]),\n    default: undefined,\n  },\n} as const)\nexport type SliderMarkerProps = ExtractPropTypes<typeof sliderMarkerProps>\n\nexport default defineComponent({\n  name: 'ElSliderMarker',\n  props: sliderMarkerProps,\n  setup(props) {\n    const ns = useNamespace('slider')\n    const label = computed(() => {\n      return isString(props.mark) ? props.mark : props.mark!.label\n    })\n    const style = computed(() =>\n      isString(props.mark) ? undefined : props.mark!.style\n    )\n\n    return () =>\n      h(\n        'div',\n        {\n          class: ns.e('marks-text'),\n          style: style.value,\n        },\n        label.value\n      )\n  },\n})\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,CAAC,EAAE;AACH,mBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,gBAAgB;AACxB,EAAE,KAAK,EAAE,iBAAiB;AAC1B,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACtC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AACjC,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AAClE,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnF,IAAI,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE;AAC1B,MAAM,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;AAC/B,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;AACxB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACpB,GAAG;AACH,CAAC,CAAC;;;;"}
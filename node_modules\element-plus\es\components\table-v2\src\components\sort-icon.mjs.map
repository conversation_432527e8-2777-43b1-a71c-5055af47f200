{"version": 3, "file": "sort-icon.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/components/sort-icon.tsx"], "sourcesContent": ["import ElIcon from '@element-plus/components/icon'\nimport { SortDown, SortUp } from '@element-plus/icons-vue'\nimport { SortOrder } from '../constants'\n\nimport type { FunctionalComponent } from 'vue'\n\nexport type SortIconProps = {\n  sortOrder: SortOrder\n  class?: JSX.IntrinsicAttributes['class']\n}\n\nconst SortIcon: FunctionalComponent<SortIconProps> = (props) => {\n  const { sortOrder } = props\n\n  return (\n    <ElIcon size={14} class={props.class}>\n      {sortOrder === SortOrder.ASC ? <SortUp /> : <SortDown />}\n    </ElIcon>\n  )\n}\n\nexport default SortIcon\n"], "names": ["SortIcon", "sortOrder", "_createVNode"], "mappings": ";;;;;;AAWA,EAAMA,MAAAA;IACE,SAAA;AAAEC,GAAAA,GAAAA,KAAAA,CAAAA;AAAF,EAAA,OAANC,WAAA,CAAA,MAAA,EAAA;AAEA,IAAA,MAAA,EAAA,EAAA;AAAA,IAAA,OAAA,EAAA,KAAA,CAAA,KAAA;AAAA,GAAA,EAAA;AAAA,IAAA,OAAA,EAAA,MAAA,CAAA,SAAA,KAAA,SAAA,CAAA,GAAA,GAAAA,WAAA,CAAA,MAAA,EAAA,IAAA,EAAA,IAAA,CAAA,GAAAA,WAAA,CAAA,QAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,GAAA,CAAA,CAAA;AAAA,CAAA,CAAA;AAKD,iBARD,QAAA;;;;"}
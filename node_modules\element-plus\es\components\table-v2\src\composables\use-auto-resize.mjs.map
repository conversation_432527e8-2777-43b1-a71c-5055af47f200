{"version": 3, "file": "use-auto-resize.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-auto-resize.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted, ref, watch } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\n\nimport type { AutoResizerProps } from '../auto-resizer'\n\nconst useAutoResize = (props: AutoResizerProps) => {\n  const sizer = ref<HTMLElement>()\n  const width$ = ref(0)\n  const height$ = ref(0)\n\n  let resizerStopper: ReturnType<typeof useResizeObserver>['stop']\n  onMounted(() => {\n    resizerStopper = useResizeObserver(sizer, ([entry]) => {\n      const { width, height } = entry.contentRect\n      const { paddingLeft, paddingRight, paddingTop, paddingBottom } =\n        getComputedStyle(entry.target)\n\n      const left = Number.parseInt(paddingLeft) || 0\n      const right = Number.parseInt(paddingRight) || 0\n      const top = Number.parseInt(paddingTop) || 0\n      const bottom = Number.parseInt(paddingBottom) || 0\n\n      width$.value = width - left - right\n      height$.value = height - top - bottom\n    }).stop\n  })\n\n  onBeforeUnmount(() => {\n    resizerStopper?.()\n  })\n\n  watch([width$, height$], ([width, height]) => {\n    props.onResize?.({\n      width,\n      height,\n    })\n  })\n\n  return {\n    sizer,\n    width: width$,\n    height: height$,\n  }\n}\n\nexport { useAutoResize }\n"], "names": [], "mappings": ";;;AAEK,MAAC,aAAa,GAAG,CAAC,KAAK,KAAK;AACjC,EAAE,MAAM,KAAK,GAAG,GAAG,EAAE,CAAC;AACtB,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACxB,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,cAAc,GAAG,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK;AAC3D,MAAM,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;AAClD,MAAM,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACtG,MAAM,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACvD,MAAM,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACnD,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACzD,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;AAC1C,MAAM,OAAO,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;AAC5C,KAAK,CAAC,CAAC,IAAI,CAAC;AACZ,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,EAAE,CAAC;AACvD,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK;AAChD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE;AAC5D,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,MAAM,EAAE,OAAO;AACnB,GAAG,CAAC;AACJ;;;;"}
{"version": 3, "file": "use-table.mjs", "sources": ["../../../../../../packages/components/table-v2/src/use-table.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  ref,\n  shallowRef,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { isArray, isNumber } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  useColumns,\n  useData,\n  useRow,\n  useScrollbar,\n  useStyles,\n} from './composables'\n\nimport type { TableV2Props } from './table'\nimport type { TableGridInstance } from './table-grid'\n\nfunction useTable(props: TableV2Props) {\n  const mainTableRef = ref<TableGridInstance>()\n  const leftTableRef = ref<TableGridInstance>()\n  const rightTableRef = ref<TableGridInstance>()\n  const {\n    columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n\n    onColumnSorted,\n  } = useColumns(props, toRef(props, 'columns'), toRef(props, 'fixed'))\n\n  const {\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n    scrollPos,\n  } = useScrollbar(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n\n    onMaybeEndReached,\n  })\n\n  const ns = useNamespace('table-v2')\n  const instance = getCurrentInstance()!\n\n  // state\n  const isScrolling = shallowRef(false)\n\n  const {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHeightChange,\n    onRowHovered,\n    onRowsRendered,\n  } = useRow(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    tableInstance: instance,\n    ns,\n    isScrolling,\n  })\n\n  const { data, depthMap } = useData(props, {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    resetAfterIndex,\n  })\n\n  const rowsHeight = computed(() => {\n    const { estimatedRowHeight, rowHeight } = props\n    const _data = unref(data)\n    if (isNumber(estimatedRowHeight)) {\n      // calculate the actual height\n      return Object.values(unref(rowHeights)).reduce(\n        (acc, curr) => acc + curr,\n        0\n      )\n    }\n\n    return _data.length * rowHeight\n  })\n\n  const {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    headerWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight,\n  } = useStyles(props, {\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    rowsHeight,\n  })\n\n  // DOM/Component refs\n  const containerRef = ref()\n\n  const showEmpty = computed(() => {\n    const noData = unref(data).length === 0\n\n    return isArray(props.fixedData)\n      ? props.fixedData.length === 0 && noData\n      : noData\n  })\n\n  function getRowHeight(rowIndex: number) {\n    const { estimatedRowHeight, rowHeight, rowKey } = props\n\n    if (!estimatedRowHeight) return rowHeight\n\n    return (\n      unref(rowHeights)[unref(data)[rowIndex][rowKey]] || estimatedRowHeight\n    )\n  }\n\n  const isEndReached = ref(false)\n  function onMaybeEndReached() {\n    const { onEndReached } = props\n    if (!onEndReached) return\n\n    const { scrollTop } = unref(scrollPos)\n\n    const _totalHeight = unref(rowsHeight)\n    const clientHeight = unref(windowHeight)\n\n    const remainDistance =\n      _totalHeight - (scrollTop + clientHeight) + props.hScrollbarSize\n\n    if (\n      !isEndReached.value &&\n      unref(lastRenderedRowIndex) >= 0 &&\n      _totalHeight <= scrollTop + unref(mainTableHeight) - unref(headerHeight)\n    ) {\n      isEndReached.value = true\n      onEndReached(remainDistance)\n    } else {\n      isEndReached.value = false\n    }\n  }\n\n  // events\n\n  watch(\n    () => unref(rowsHeight),\n    () => (isEndReached.value = false)\n  )\n\n  watch(\n    () => props.expandedRowKeys,\n    (val) => (expandedRowKeys.value = val),\n    {\n      deep: true,\n    }\n  )\n\n  return {\n    // models\n    columns,\n    containerRef,\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    // states\n    isDynamic,\n    isResetting,\n    isScrolling,\n    hasFixedColumns,\n    // records\n    columnsStyles,\n    columnsTotalWidth,\n    data,\n    expandedRowKeys,\n    depthMap,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    mainColumns,\n    // metadata\n    bodyWidth,\n    emptyStyle,\n    rootStyle,\n    headerWidth,\n    footerHeight,\n    mainTableHeight,\n    fixedTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    // flags\n    showEmpty,\n\n    // methods\n    getRowHeight,\n\n    // event handlers\n    onColumnSorted,\n    onRowHovered,\n    onRowExpanded,\n    onRowsRendered,\n    onRowHeightChange,\n    // use scrollbars\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n  }\n}\n\nexport { useTable }\n\nexport type UseTableReturn = ReturnType<typeof useTable>\n"], "names": [], "mappings": ";;;;;;;;;;AAkBA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC;AAC9B,EAAE,MAAM;AACR,IAAI,OAAO;AACX,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AACxE,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE;AAC1B,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACxC,EAAE,MAAM;AACR,IAAI,eAAe;AACnB,IAAI,oBAAoB;AACxB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,aAAa,EAAE,QAAQ;AAC3B,IAAI,EAAE;AACN,IAAI,WAAW;AACf,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE;AAC5C,IAAI,eAAe;AACnB,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,MAAM,EAAE,kBAAkB,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;AACpD,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACtC,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AACnF,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE;AACvB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,UAAU;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAC5C,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC;AACtF,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,YAAY,CAAC,QAAQ,EAAE;AAClC,IAAI,MAAM,EAAE,kBAAkB,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AAC5D,IAAI,IAAI,CAAC,kBAAkB;AAC3B,MAAM,OAAO,SAAS,CAAC;AACvB,IAAI,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,kBAAkB,CAAC;AAClF,GAAG;AACH,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAClC,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC;AACnC,IAAI,IAAI,CAAC,YAAY;AACrB,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AAC3C,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAC3C,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7C,IAAI,MAAM,cAAc,GAAG,YAAY,IAAI,SAAS,GAAG,YAAY,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC;AAC5F,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,YAAY,IAAI,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE;AAC7I,MAAM,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AAChC,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,EAAE,MAAM,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACnE,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK,eAAe,CAAC,KAAK,GAAG,GAAG,EAAE;AAC3E,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,IAAI;AACR,IAAI,eAAe;AACnB,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ;;;;"}
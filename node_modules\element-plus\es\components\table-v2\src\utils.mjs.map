{"version": 3, "file": "utils.mjs", "sources": ["../../../../../../packages/components/table-v2/src/utils.ts"], "sourcesContent": ["import { h, isVNode } from 'vue'\nimport { addUnit, isArray, isFunction } from '@element-plus/utils'\n\nimport type { CSSProperties, Component, Slot } from 'vue'\n\nconst sumReducer = (sum: number, num: number) => sum + num\n\nexport const sum = (listLike: number | number[]) => {\n  return isArray(listLike) ? listLike.reduce(sumReducer, 0) : listLike\n}\n\nexport const tryCall = <T>(\n  fLike: T,\n  params: T extends (...args: infer K) => unknown ? K : any,\n  defaultRet = {}\n) => {\n  return isFunction(fLike) ? fLike(params) : fLike ?? defaultRet\n}\n\nexport const enforceUnit = (style: CSSProperties) => {\n  ;(['width', 'maxWidth', 'minWidth', 'height'] as const).forEach((key) => {\n    style[key] = addUnit(style[key])\n  })\n\n  return style\n}\n\nexport const componentToSlot = <T extends object>(\n  ComponentLike: JSX.Element | ((props: T) => Component<T>) | undefined\n) =>\n  isVNode(ComponentLike)\n    ? (props: T) => h(ComponentLike, props)\n    : (ComponentLike as Slot)\n"], "names": [], "mappings": ";;;;AAEA,MAAM,UAAU,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACjC,MAAC,GAAG,GAAG,CAAC,QAAQ,KAAK;AACjC,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC;AACvE,EAAE;AACU,MAAC,OAAO,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,GAAG,EAAE,KAAK;AAC3D,EAAE,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,UAAU,CAAC;AAChF,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,KAAK;AAEtC,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC/D,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,eAAe,GAAG,CAAC,aAAa,KAAK,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG;;;;"}
{"version": 3, "file": "style-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table/style-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  ref,\n  unref,\n  watch,\n  watchEffect,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { useFormSize } from '@element-plus/components/form'\n\nimport type { Table, TableProps } from './defaults'\nimport type { Store } from '../store'\nimport type TableLayout from '../table-layout'\nimport type { TableColumnCtx } from '../table-column/defaults'\n\nfunction useStyle<T>(\n  props: TableProps<T>,\n  layout: TableLayout<T>,\n  store: Store<T>,\n  table: Table<T>\n) {\n  const isHidden = ref(false)\n  const renderExpanded = ref(null)\n  const resizeProxyVisible = ref(false)\n  const setDragVisible = (visible: boolean) => {\n    resizeProxyVisible.value = visible\n  }\n  const resizeState = ref<{\n    width: null | number\n    height: null | number\n    headerHeight: null | number\n  }>({\n    width: null,\n    height: null,\n    headerHeight: null,\n  })\n  const isGroup = ref(false)\n  const scrollbarViewStyle = {\n    display: 'inline-block',\n    verticalAlign: 'middle',\n  }\n  const tableWidth = ref()\n  const tableScrollHeight = ref(0)\n  const bodyScrollHeight = ref(0)\n  const headerScrollHeight = ref(0)\n  const footerScrollHeight = ref(0)\n  const appendScrollHeight = ref(0)\n\n  watchEffect(() => {\n    layout.setHeight(props.height)\n  })\n  watchEffect(() => {\n    layout.setMaxHeight(props.maxHeight)\n  })\n  watch(\n    () => [props.currentRowKey, store.states.rowKey],\n    ([currentRowKey, rowKey]) => {\n      if (!unref(rowKey) || !unref(currentRowKey)) return\n      store.setCurrentRowKey(`${currentRowKey}`)\n    },\n    {\n      immediate: true,\n    }\n  )\n  watch(\n    () => props.data,\n    (data) => {\n      table.store.commit('setData', data)\n    },\n    {\n      immediate: true,\n      deep: true,\n    }\n  )\n  watchEffect(() => {\n    if (props.expandRowKeys) {\n      store.setExpandRowKeysAdapter(props.expandRowKeys)\n    }\n  })\n\n  const handleMouseLeave = () => {\n    table.store.commit('setHoverRow', null)\n    if (table.hoverState) table.hoverState = null\n  }\n\n  const handleHeaderFooterMousewheel = (event, data) => {\n    const { pixelX, pixelY } = data\n    if (Math.abs(pixelX) >= Math.abs(pixelY)) {\n      table.refs.bodyWrapper.scrollLeft += data.pixelX / 5\n    }\n  }\n\n  const shouldUpdateHeight = computed(() => {\n    return (\n      props.height ||\n      props.maxHeight ||\n      store.states.fixedColumns.value.length > 0 ||\n      store.states.rightFixedColumns.value.length > 0\n    )\n  })\n\n  const tableBodyStyles = computed(() => {\n    return {\n      width: layout.bodyWidth.value ? `${layout.bodyWidth.value}px` : '',\n    }\n  })\n\n  const doLayout = () => {\n    if (shouldUpdateHeight.value) {\n      layout.updateElsHeight()\n    }\n    layout.updateColumnsWidth()\n\n    // When the test case is running, the context environment simulated by jsdom may have been destroyed,\n    // and window.requestAnimationFrame does not exist at this time.\n    if (typeof window === 'undefined') return\n    requestAnimationFrame(syncPosition)\n  }\n  onMounted(async () => {\n    await nextTick()\n    store.updateColumns()\n    bindEvents()\n    requestAnimationFrame(doLayout)\n\n    const el: HTMLElement = table.vnode.el as HTMLElement\n    const tableHeader: HTMLElement = table.refs.headerWrapper\n    if (props.flexible && el && el.parentElement) {\n      // Automatic minimum size of flex-items\n      // Ensure that the main axis does not follow the width of the items\n      el.parentElement.style.minWidth = '0'\n    }\n\n    resizeState.value = {\n      width: (tableWidth.value = el.offsetWidth),\n      height: el.offsetHeight,\n      headerHeight:\n        props.showHeader && tableHeader ? tableHeader.offsetHeight : null,\n    }\n\n    // init filters\n    store.states.columns.value.forEach((column: TableColumnCtx<T>) => {\n      if (column.filteredValue && column.filteredValue.length) {\n        table.store.commit('filterChange', {\n          column,\n          values: column.filteredValue,\n          silent: true,\n        })\n      }\n    })\n    table.$ready = true\n  })\n  const setScrollClassByEl = (el: HTMLElement, className: string) => {\n    if (!el) return\n    const classList = Array.from(el.classList).filter(\n      (item) => !item.startsWith('is-scrolling-')\n    )\n    classList.push(layout.scrollX.value ? className : 'is-scrolling-none')\n    el.className = classList.join(' ')\n  }\n  const setScrollClass = (className: string) => {\n    const { tableWrapper } = table.refs\n    setScrollClassByEl(tableWrapper, className)\n  }\n  const hasScrollClass = (className: string) => {\n    const { tableWrapper } = table.refs\n    return !!(tableWrapper && tableWrapper.classList.contains(className))\n  }\n  const syncPosition = function () {\n    if (!table.refs.scrollBarRef) return\n    if (!layout.scrollX.value) {\n      const scrollingNoneClass = 'is-scrolling-none'\n      if (!hasScrollClass(scrollingNoneClass)) {\n        setScrollClass(scrollingNoneClass)\n      }\n      return\n    }\n    const scrollContainer = table.refs.scrollBarRef.wrapRef\n    if (!scrollContainer) return\n    const { scrollLeft, offsetWidth, scrollWidth } = scrollContainer\n    const { headerWrapper, footerWrapper } = table.refs\n    if (headerWrapper) headerWrapper.scrollLeft = scrollLeft\n    if (footerWrapper) footerWrapper.scrollLeft = scrollLeft\n    const maxScrollLeftPosition = scrollWidth - offsetWidth - 1\n    if (scrollLeft >= maxScrollLeftPosition) {\n      setScrollClass('is-scrolling-right')\n    } else if (scrollLeft === 0) {\n      setScrollClass('is-scrolling-left')\n    } else {\n      setScrollClass('is-scrolling-middle')\n    }\n  }\n\n  const bindEvents = () => {\n    if (!table.refs.scrollBarRef) return\n    if (table.refs.scrollBarRef.wrapRef) {\n      useEventListener(\n        table.refs.scrollBarRef.wrapRef,\n        'scroll',\n        syncPosition,\n        {\n          passive: true,\n        }\n      )\n    }\n    if (props.fit) {\n      useResizeObserver(table.vnode.el as HTMLElement, resizeListener)\n    } else {\n      useEventListener(window, 'resize', resizeListener)\n    }\n\n    useResizeObserver(table.refs.bodyWrapper, () => {\n      resizeListener()\n      table.refs?.scrollBarRef?.update()\n    })\n  }\n  const resizeListener = () => {\n    const el = table.vnode.el\n    if (!table.$ready || !el) return\n\n    let shouldUpdateLayout = false\n    const {\n      width: oldWidth,\n      height: oldHeight,\n      headerHeight: oldHeaderHeight,\n    } = resizeState.value\n\n    const width = (tableWidth.value = el.offsetWidth)\n    if (oldWidth !== width) {\n      shouldUpdateLayout = true\n    }\n\n    const height = el.offsetHeight\n    if ((props.height || shouldUpdateHeight.value) && oldHeight !== height) {\n      shouldUpdateLayout = true\n    }\n\n    const tableHeader: HTMLElement =\n      props.tableLayout === 'fixed'\n        ? table.refs.headerWrapper\n        : table.refs.tableHeaderRef?.$el\n    if (props.showHeader && tableHeader?.offsetHeight !== oldHeaderHeight) {\n      shouldUpdateLayout = true\n    }\n\n    tableScrollHeight.value = table.refs.tableWrapper?.scrollHeight || 0\n    headerScrollHeight.value = tableHeader?.scrollHeight || 0\n    footerScrollHeight.value = table.refs.footerWrapper?.offsetHeight || 0\n    appendScrollHeight.value = table.refs.appendWrapper?.offsetHeight || 0\n    bodyScrollHeight.value =\n      tableScrollHeight.value -\n      headerScrollHeight.value -\n      footerScrollHeight.value -\n      appendScrollHeight.value\n\n    if (shouldUpdateLayout) {\n      resizeState.value = {\n        width,\n        height,\n        headerHeight: (props.showHeader && tableHeader?.offsetHeight) || 0,\n      }\n      doLayout()\n    }\n  }\n  const tableSize = useFormSize()\n  const bodyWidth = computed(() => {\n    const { bodyWidth: bodyWidth_, scrollY, gutterWidth } = layout\n    return bodyWidth_.value\n      ? `${(bodyWidth_.value as number) - (scrollY.value ? gutterWidth : 0)}px`\n      : ''\n  })\n\n  const tableLayout = computed(() => {\n    if (props.maxHeight) return 'fixed'\n    return props.tableLayout\n  })\n\n  const emptyBlockStyle = computed(() => {\n    if (props.data && props.data.length) return null\n    let height = '100%'\n    if (props.height && bodyScrollHeight.value) {\n      height = `${bodyScrollHeight.value}px`\n    }\n    const width = tableWidth.value\n    return {\n      width: width ? `${width}px` : '',\n      height,\n    }\n  })\n\n  const scrollbarStyle = computed(() => {\n    if (props.height) {\n      return {\n        height: '100%',\n      }\n    }\n    if (props.maxHeight) {\n      if (!Number.isNaN(Number(props.maxHeight))) {\n        return {\n          maxHeight: `${\n            props.maxHeight -\n            headerScrollHeight.value -\n            footerScrollHeight.value\n          }px`,\n        }\n      } else {\n        return {\n          maxHeight: `calc(${props.maxHeight} - ${\n            headerScrollHeight.value + footerScrollHeight.value\n          }px)`,\n        }\n      }\n    }\n\n    return {}\n  })\n\n  /**\n   * fix layout\n   */\n  const handleFixedMousewheel = (event, data) => {\n    const bodyWrapper = table.refs.bodyWrapper\n    if (Math.abs(data.spinY) > 0) {\n      const currentScrollTop = bodyWrapper.scrollTop\n      if (data.pixelY < 0 && currentScrollTop !== 0) {\n        event.preventDefault()\n      }\n      if (\n        data.pixelY > 0 &&\n        bodyWrapper.scrollHeight - bodyWrapper.clientHeight > currentScrollTop\n      ) {\n        event.preventDefault()\n      }\n      bodyWrapper.scrollTop += Math.ceil(data.pixelY / 5)\n    } else {\n      bodyWrapper.scrollLeft += Math.ceil(data.pixelX / 5)\n    }\n  }\n\n  return {\n    isHidden,\n    renderExpanded,\n    setDragVisible,\n    isGroup,\n    handleMouseLeave,\n    handleHeaderFooterMousewheel,\n    tableSize,\n    emptyBlockStyle,\n    handleFixedMousewheel,\n    resizeProxyVisible,\n    bodyWidth,\n    resizeState,\n    doLayout,\n    tableBodyStyles,\n    tableLayout,\n    scrollbarViewStyle,\n    scrollbarStyle,\n  }\n}\n\nexport default useStyle\n"], "names": [], "mappings": ";;;;AAWA,SAAS,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE;AAC/C,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,kBAAkB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACxC,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK;AACtC,IAAI,kBAAkB,CAAC,KAAK,GAAG,OAAO,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC;AAC1B,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,YAAY,EAAE,IAAI;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,kBAAkB,GAAG;AAC7B,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,aAAa,EAAE,QAAQ;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,EAAE,MAAM,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,MAAM,kBAAkB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACnC,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACzC,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,KAAK;AACvF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;AAC/C,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC/C,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;AACpC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACxC,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,IAAI,KAAK,CAAC,aAAa,EAAE;AAC7B,MAAM,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACzD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,KAAK,CAAC,UAAU;AACxB,MAAM,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,4BAA4B,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACxD,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AACpC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC9C,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,OAAO,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5I,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE;AACxE,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,MAAM;AACzB,IAAI,IAAI,kBAAkB,CAAC,KAAK,EAAE;AAClC,MAAM,MAAM,CAAC,eAAe,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;AAChC,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW;AACrC,MAAM,OAAO;AACb,IAAI,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACxC,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,YAAY;AACxB,IAAI,MAAM,QAAQ,EAAE,CAAC;AACrB,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;AAC1B,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AAC9B,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;AACjD,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,aAAa,EAAE;AAClD,MAAM,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC;AAC5C,KAAK;AACL,IAAI,WAAW,CAAC,KAAK,GAAG;AACxB,MAAM,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW;AAC9C,MAAM,MAAM,EAAE,EAAE,CAAC,YAAY;AAC7B,MAAM,YAAY,EAAE,KAAK,CAAC,UAAU,IAAI,WAAW,GAAG,WAAW,CAAC,YAAY,GAAG,IAAI;AACrF,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnD,MAAM,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE;AAC/D,QAAQ,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE;AAC3C,UAAU,MAAM;AAChB,UAAU,MAAM,EAAE,MAAM,CAAC,aAAa;AACtC,UAAU,MAAM,EAAE,IAAI;AACtB,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACxB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,CAAC,EAAE,EAAE,SAAS,KAAK;AAChD,IAAI,IAAI,CAAC,EAAE;AACX,MAAM,OAAO;AACb,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;AACnG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,mBAAmB,CAAC,CAAC;AAC3E,IAAI,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,SAAS,KAAK;AACxC,IAAI,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;AACxC,IAAI,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,SAAS,KAAK;AACxC,IAAI,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;AACxC,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1E,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,WAAW;AAClC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY;AAChC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AAC/B,MAAM,MAAM,kBAAkB,GAAG,mBAAmB,CAAC;AACrD,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE;AAC/C,QAAQ,cAAc,CAAC,kBAAkB,CAAC,CAAC;AAC3C,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AAC5D,IAAI,IAAI,CAAC,eAAe;AACxB,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC;AACrE,IAAI,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;AACxD,IAAI,IAAI,aAAa;AACrB,MAAM,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;AAC5C,IAAI,IAAI,aAAa;AACrB,MAAM,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;AAC5C,IAAI,MAAM,qBAAqB,GAAG,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;AAChE,IAAI,IAAI,UAAU,IAAI,qBAAqB,EAAE;AAC7C,MAAM,cAAc,CAAC,oBAAoB,CAAC,CAAC;AAC3C,KAAK,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE;AACjC,MAAM,cAAc,CAAC,mBAAmB,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,cAAc,CAAC,qBAAqB,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY;AAChC,MAAM,OAAO;AACb,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACzC,MAAM,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE;AAChF,QAAQ,OAAO,EAAE,IAAI;AACrB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM;AACpD,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACjG,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACnC,IAAI,MAAM;AACV,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,YAAY,EAAE,eAAe;AACnC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAC1B,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC;AACpD,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC;AACnC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,kBAAkB,CAAC,KAAK,KAAK,SAAS,KAAK,MAAM,EAAE;AAC5E,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,KAAK,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AAC9I,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,YAAY,MAAM,eAAe,EAAE;AAC3G,MAAM,kBAAkB,GAAG,IAAI,CAAC;AAChC,KAAK;AACL,IAAI,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC;AACvG,IAAI,kBAAkB,CAAC,KAAK,GAAG,CAAC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,YAAY,KAAK,CAAC,CAAC;AAC9F,IAAI,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC;AACzG,IAAI,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,CAAC;AACzG,IAAI,gBAAgB,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC;AACtI,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,WAAW,CAAC,KAAK,GAAG;AAC1B,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,YAAY,EAAE,KAAK,CAAC,UAAU,KAAK,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC;AACxG,OAAO,CAAC;AACR,MAAM,QAAQ,EAAE,CAAC;AACjB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,WAAW,EAAE,CAAC;AAClC,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;AACnE,IAAI,OAAO,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,UAAU,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AAC/F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,KAAK,CAAC,SAAS;AACvB,MAAM,OAAO,OAAO,CAAC;AACrB,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM;AACvC,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC;AACxB,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAChD,MAAM,MAAM,GAAG,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;AACnC,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE;AACtC,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,MAAM;AACtB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;AACzB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;AAClD,QAAQ,OAAO;AACf,UAAU,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;AACjG,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,OAAO;AACf,UAAU,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,kBAAkB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC;AAC1G,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACjD,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;AAC/C,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAClC,MAAM,MAAM,gBAAgB,GAAG,WAAW,CAAC,SAAS,CAAC;AACrD,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,KAAK,CAAC,EAAE;AACrD,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,GAAG,gBAAgB,EAAE;AACrG,QAAQ,KAAK,CAAC,cAAc,EAAE,CAAC;AAC/B,OAAO;AACP,MAAM,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC1D,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,gBAAgB;AACpB,IAAI,4BAA4B;AAChC,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}
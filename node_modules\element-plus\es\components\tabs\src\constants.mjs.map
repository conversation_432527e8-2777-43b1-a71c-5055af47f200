{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/tabs/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, Injection<PERSON><PERSON>, Ref, Slots, UnwrapRef } from 'vue'\nimport type { TabsProps } from './tabs'\nimport type { TabPaneProps } from './tab-pane'\n\nexport type TabsPaneContext = UnwrapRef<{\n  uid: number\n  slots: Slots\n  props: TabPaneProps\n  paneName: ComputedRef<string | number | undefined>\n  active: ComputedRef<boolean>\n  index: Ref<string | undefined>\n  isClosable: ComputedRef<boolean>\n}>\n\nexport interface TabsRootContext {\n  props: TabsProps\n  currentName: Ref<string | number>\n  registerPane: (pane: TabsPaneContext) => void\n  sortPane: (pane: TabsPaneContext) => void\n  unregisterPane: (uid: number) => void\n}\n\nexport const tabsRootContextKey: InjectionKey<TabsRootContext> =\n  Symbol('tabsRootContextKey')\n"], "names": [], "mappings": "AAAY,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB;;;;"}
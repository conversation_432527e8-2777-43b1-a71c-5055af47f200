{"version": 3, "file": "tab-pane2.mjs", "sources": ["../../../../../../packages/components/tabs/src/tab-pane.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"shouldBeRender\"\n    v-show=\"active\"\n    :id=\"`pane-${paneName}`\"\n    :class=\"ns.b()\"\n    role=\"tabpanel\"\n    :aria-hidden=\"!active\"\n    :aria-labelledby=\"`tab-${paneName}`\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  reactive,\n  ref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { eagerComputed } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport { tabPaneProps } from './tab-pane'\n\nconst COMPONENT_NAME = 'ElTabPane'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(tabPaneProps)\n\nconst instance = getCurrentInstance()!\nconst slots = useSlots()\n\nconst tabsRoot = inject(tabsRootContextKey)\nif (!tabsRoot)\n  throwError(COMPONENT_NAME, 'usage: <el-tabs><el-tab-pane /></el-tabs/>')\n\nconst ns = useNamespace('tab-pane')\n\nconst index = ref<string>()\nconst isClosable = computed(() => props.closable || tabsRoot.props.closable)\nconst active = eagerComputed(\n  () => tabsRoot.currentName.value === (props.name ?? index.value)\n)\nconst loaded = ref(active.value)\nconst paneName = computed(() => props.name ?? index.value)\nconst shouldBeRender = eagerComputed(\n  () => !props.lazy || loaded.value || active.value\n)\n\nwatch(active, (val) => {\n  if (val) loaded.value = true\n})\n\nconst pane = reactive({\n  uid: instance.uid,\n  slots,\n  props,\n  paneName,\n  active,\n  index,\n  isClosable,\n})\n\ntabsRoot.registerPane(pane)\nonMounted(() => {\n  tabsRoot.sortPane(pane)\n})\n\nonUnmounted(() => {\n  tabsRoot.unregisterPane(pane.uid)\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;AAGA,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,QAAA,GAAW,OAAO,kBAAkB,CAAA,CAAA;AAC1C,IAAA,IAAI,CAAC,QAAA;AACH,MAAA,UAAA,CAAW,gBAAgB,4CAA4C,CAAA,CAAA;AAEzE,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAElC,IAAA,MAAM,QAAQ,GAAY,EAAA,CAAA;AAC1B,IAAA,MAAM,aAAa,QAAS,CAAA,MAAM,MAAM,QAAY,IAAA,QAAA,CAAS,MAAM,QAAQ,CAAA,CAAA;AAC3E,IAAA,MAAM,MAAS,GAAA,aAAA,CAAA,MAAA;AAAA,MACb,MAAM,CAAS;AAA2C,MAC5D,OAAA,QAAA,CAAA,WAAA,CAAA,KAAA,MAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACA,KAAM,CAAA,CAAA;AACN,IAAA,MAAM,YAAoB,CAAA,MAAA,CAAA,KAAA,CAAM,CAAM;AACtC,IAAA,MAAM,QAAiB,GAAA,QAAA,CAAA,MAAA;AAAA,MACrB,MAAM,CAAC;AAAqC,MAC9C,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAEA,KAAM,CAAA,CAAA;AACJ,IAAI,MAAA,cAAoB,GAAA,aAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,IAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IAC1B,KAAC,CAAA,MAAA,EAAA,CAAA,GAAA,KAAA;AAED,MAAA,IAAM;AAAgB,cACN,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAAA,IACA,MAAA,IAAA,GAAA,QAAA,CAAA;AAAA,MACA,GAAA,EAAA,QAAA,CAAA,GAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,QAAA;AAAA,MACD,MAAA;AAED,MAAA,KAAA;AACA,MAAA,UAAgB;AACd,KAAA,CAAA,CAAA;AAAsB,IACxB,QAAC,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA;AAED,IAAA,SAAA,CAAA,MAAkB;AAChB,MAAS,QAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAe;AAAQ,KACjC,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;"}
{"version": 3, "file": "text.mjs", "sources": ["../../../../../../packages/components/text/src/text.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const textProps = buildProps({\n  /**\n   * @description text type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger', ''],\n    default: '',\n  },\n  /**\n   * @description text size\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n    default: '',\n  },\n  /**\n   * @description render ellipsis\n   */\n  truncated: Boolean,\n  /**\n   * @description maximum lines\n   */\n  lineClamp: {\n    type: [String, Number],\n  },\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: String,\n    default: 'span',\n  },\n} as const)\n\nexport type TextProps = ExtractPropTypes<typeof textProps>\n"], "names": [], "mappings": ";;;AAEY,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,CAAC;AACnE,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}
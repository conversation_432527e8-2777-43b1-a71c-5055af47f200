{"version": 3, "file": "panel-time-range.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-range.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"actualVisible\"\n    :class=\"[nsTime.b('range-picker'), nsPicker.b('panel')]\"\n  >\n    <div :class=\"nsTime.be('range-picker', 'content')\">\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.startTime') }}\n        </div>\n        <div :class=\"startContainerKls\">\n          <time-spinner\n            ref=\"minSpinner\"\n            role=\"start\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"startTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMinChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMinSelectionRange\"\n          />\n        </div>\n      </div>\n      <div :class=\"nsTime.be('range-picker', 'cell')\">\n        <div :class=\"nsTime.be('range-picker', 'header')\">\n          {{ t('el.datepicker.endTime') }}\n        </div>\n        <div :class=\"endContainerKls\">\n          <time-spinner\n            ref=\"maxSpinner\"\n            role=\"end\"\n            :show-seconds=\"showSeconds\"\n            :am-pm-mode=\"amPmMode\"\n            :arrow-control=\"arrowControl\"\n            :spinner-date=\"endTime\"\n            :disabled-hours=\"disabledHours_\"\n            :disabled-minutes=\"disabledMinutes_\"\n            :disabled-seconds=\"disabledSeconds_\"\n            @change=\"handleMaxChange\"\n            @set-option=\"onSetOption\"\n            @select-range=\"setMaxSelectionRange\"\n          />\n        </div>\n      </div>\n    </div>\n    <div :class=\"nsTime.be('panel', 'footer')\">\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'cancel']\"\n        @click=\"handleCancel()\"\n      >\n        {{ t('el.datepicker.cancel') }}\n      </button>\n      <button\n        type=\"button\"\n        :class=\"[nsTime.be('panel', 'btn'), 'confirm']\"\n        :disabled=\"btnConfirmDisabled\"\n        @click=\"handleConfirm()\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, unref } from 'vue'\nimport dayjs from 'dayjs'\nimport { union } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { panelTimeRangeProps } from '../props/panel-time-range'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimeRangeProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\nconst makeSelectRange = (start: number, end: number) => {\n  const result: number[] = []\n  for (let i = start; i <= end; i++) {\n    result.push(i)\n  }\n  return result\n}\n\nconst { t, lang } = useLocale()\nconst nsTime = useNamespace('time')\nconst nsPicker = useNamespace('picker')\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\n\nconst startContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\nconst endContainerKls = computed(() => [\n  nsTime.be('range-picker', 'body'),\n  nsTime.be('panel', 'content'),\n  nsTime.is('arrow', arrowControl),\n  showSeconds.value ? 'has-seconds' : '',\n])\n\nconst startTime = computed(() => props.parsedValue![0])\nconst endTime = computed(() => props.parsedValue![1])\nconst oldValue = useOldValue(props)\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n\nconst handleConfirm = (visible = false) => {\n  emit('pick', [startTime.value, endTime.value], visible)\n}\n\nconst handleMinChange = (date: Dayjs) => {\n  handleChange(date.millisecond(0), endTime.value)\n}\nconst handleMaxChange = (date: Dayjs) => {\n  handleChange(startTime.value, date.millisecond(0))\n}\n\nconst isValidValue = (_date: Dayjs[]) => {\n  const parsedDate = _date.map((_) => dayjs(_).locale(lang.value))\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate[0].isSame(result[0]) && parsedDate[1].isSame(result[1])\n}\n\nconst handleChange = (start: Dayjs, end: Dayjs) => {\n  if (!props.visible) {\n    return\n  }\n  // todo getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', [start, end], true)\n}\nconst btnConfirmDisabled = computed(() => {\n  return startTime.value > endTime.value\n})\n\nconst selectionRange = ref([0, 2])\nconst setMinSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'min')\n  selectionRange.value = [start, end]\n}\n\nconst offset = computed(() => (showSeconds.value ? 11 : 8))\nconst setMaxSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end, 'max')\n  const _offset = unref(offset)\n  selectionRange.value = [start + _offset, end + _offset]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const list = showSeconds.value ? [0, 3, 6, 11, 14, 17] : [0, 3, 8, 11]\n  const mapping = ['hours', 'minutes'].concat(\n    showSeconds.value ? ['seconds'] : []\n  )\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  const half = list.length / 2\n  if (next < half) {\n    timePickerOptions['start_emitSelectRange'](mapping[next])\n  } else {\n    timePickerOptions['end_emitSelectRange'](mapping[next - half])\n  }\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    const role = selectionRange.value[0] < offset.value ? 'start' : 'end'\n    timePickerOptions[`${role}_scrollDown`](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst disabledHours_ = (role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledHours ? disabledHours(role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const nextDisable = isStart\n    ? makeSelectRange(compareHour + 1, 23)\n    : makeSelectRange(0, compareHour - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledMinutes_ = (hour: number, role: string, compare?: Dayjs) => {\n  const defaultDisable = disabledMinutes ? disabledMinutes(hour, role) : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  if (hour !== compareHour) {\n    return defaultDisable\n  }\n  const compareMinute = compareDate.minute()\n  const nextDisable = isStart\n    ? makeSelectRange(compareMinute + 1, 59)\n    : makeSelectRange(0, compareMinute - 1)\n  return union(defaultDisable, nextDisable)\n}\nconst disabledSeconds_ = (\n  hour: number,\n  minute: number,\n  role: string,\n  compare?: Dayjs\n) => {\n  const defaultDisable = disabledSeconds\n    ? disabledSeconds(hour, minute, role)\n    : []\n  const isStart = role === 'start'\n  const compareDate = compare || (isStart ? endTime.value : startTime.value)\n  const compareHour = compareDate.hour()\n  const compareMinute = compareDate.minute()\n  if (hour !== compareHour || minute !== compareMinute) {\n    return defaultDisable\n  }\n  const compareSecond = compareDate.second()\n  const nextDisable = isStart\n    ? makeSelectRange(compareSecond + 1, 59)\n    : makeSelectRange(0, compareSecond - 1)\n  return union(defaultDisable, nextDisable)\n}\n\nconst getRangeAvailableTime = ([start, end]: Array<Dayjs>) => {\n  return [\n    getAvailableTime(start, 'start', true, end),\n    getAvailableTime(end, 'end', false, start),\n  ] as const\n}\n\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(\n    disabledHours_,\n    disabledMinutes_,\n    disabledSeconds_\n  )\n\nconst {\n  timePickerOptions,\n\n  getAvailableTime,\n  onSetOption,\n} = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst parseUserInput = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => dayjs(d, props.format).locale(lang.value))\n  }\n  return dayjs(days, props.format).locale(lang.value)\n}\n\nconst formatToString = (days: Dayjs[] | Dayjs) => {\n  if (!days) return null\n  if (isArray(days)) {\n    return days.map((d) => d.format(props.format))\n  }\n  return days.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  if (isArray(defaultValue)) {\n    return defaultValue.map((d: Date) => dayjs(d).locale(lang.value))\n  }\n  const defaultDay = dayjs(defaultValue).locale(lang.value)\n  return [defaultDay, defaultDay.add(60, 'm')]\n}\n\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\n</script>\n"], "names": ["_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;;;;;;;;;;;AAyFA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAA,EAAe,GAAgB,KAAA;AACtD,MAAA,MAAM,SAAmB,EAAC,CAAA;AAC1B,MAAA,KAAA,IAAS,CAAI,GAAA,KAAA,EAAO,CAAK,IAAA,GAAA,EAAK,CAAK,EAAA,EAAA;AACjC,QAAA,MAAA,CAAO,KAAK,CAAC,CAAA,CAAA;AAAA,OACf;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAC9B,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,QAAA,GAAW,aAAa,QAAQ,CAAA,CAAA;AACtC,IAAM,MAAA,UAAA,GAAa,OAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,QACE,UAAW,CAAA,KAAA,CAAA;AAEf,IAAM,MAAA,iBAAA,GAAoB,SAAS,MAAM;AAAA,MACvC,MAAA,CAAO,EAAG,CAAA,cAAA,EAAgB,MAAM,CAAA;AAAA,MAChC,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,SAAS,CAAA;AAAA,MAC5B,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,YAAY,CAAA;AAAA,MAC/B,WAAA,CAAY,QAAQ,aAAgB,GAAA,EAAA;AAAA,KACrC,CAAA,CAAA;AACD,IAAM,MAAA,eAAA,GAAkB,SAAS,MAAM;AAAA,MACrC,MAAA,CAAO,EAAG,CAAA,cAAA,EAAgB,MAAM,CAAA;AAAA,MAChC,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,SAAS,CAAA;AAAA,MAC5B,MAAA,CAAO,EAAG,CAAA,OAAA,EAAS,YAAY,CAAA;AAAA,MAC/B,WAAA,CAAY,QAAQ,aAAgB,GAAA,EAAA;AAAA,KACrC,CAAA,CAAA;AAED,IAAA,MAAM,YAAY,QAAS,CAAA,MAAM,KAAM,CAAA,WAAA,CAAa,CAAC,CAAC,CAAA,CAAA;AACtD,IAAA,MAAM,UAAU,QAAS,CAAA,MAAM,KAAM,CAAA,WAAA,CAAa,CAAC,CAAC,CAAA,CAAA;AACpD,IAAM,MAAA,QAAA,GAAW,YAAY,KAAK,CAAA,CAAA;AAClC,IAAA,MAAM,eAAe,MAAM;AACzB,MAAK,IAAA,CAAA,MAAA,EAAQ,QAAS,CAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AAAA,KACpC,CAAA;AACA,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AACD,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,QAAS,CAAA,GAAG;AAC7B,QAAA,OAAU,GAAA,CAAA;AACV,MAAO,IAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AAAA,QACR,OAAA,GAAA,CAAA;AAED,MAAM,OAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AAAsD,IACxD,MAAA,aAAA,GAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,MAAA,EAAA,CAAA,SAAmB,CAAgB,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA;AACvC,KAAA,CAAA;AAA+C,IACjD,MAAA,eAAA,GAAA,CAAA,IAAA,KAAA;AACA,MAAM,YAAA,CAAA,IAAA,CAAA,WAAmC,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACvC,KAAA,CAAA;AAAiD,IACnD,MAAA,eAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,YAAA,CAAA,SAAmC,CAAA,KAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACvC,KAAM,CAAA;AACN,IAAM,MAAA;AACN,MAAA,MAAA,UAAkB,GAAC,KAAE,CAAA,GAAO,QAAQ,KAAM,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAc,CAAO,KAAA,CAAA,CAAA,CAAA;AAAS,MAC1E,MAAA,MAAA,GAAA,qBAAA,CAAA,UAAA,CAAA,CAAA;AAEA,MAAM,OAAA,UAAA,CAAA,CAAe,CAAC,CAAA,MAAc,CAAe,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,UAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACjD,KAAI,CAAA;AACF,IAAA,MAAA,YAAA,GAAA,CAAA,KAAA,EAAA,GAAA,KAAA;AAAA,MACF,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA;AAEA,QAAA,OAAa;AAAkB,OACjC;AACA,MAAM,IAAA,CAAA,MAAA,EAAA,CAAA,KAAA,EAAA,GAAA,CAAqB;AACzB,KAAO,CAAA;AAA0B,IACnC,MAAC,kBAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAA,OAAuB,SAAA,CAAA,KAAA,GAAI,OAAM,CAAA,KAAA,CAAA;AACjC,KAAM,CAAA,CAAA;AACJ,IAAK,MAAA,cAAA,GAAgB,GAAO,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAK;AACjC,IAAe,MAAA,oBAAS,GAAA,CAAA,KAAU,EAAA,GAAA,KAAA;AAAA,MACpC,IAAA,CAAA,cAAA,EAAA,KAAA,EAAA,GAAA,EAAA,KAAA,CAAA,CAAA;AAEA,MAAA,cAAwB,CAAA,KAAA,GAAA,CAAA,KAAmB,EAAA,GAAA,CAAA,CAAA;AAC3C,KAAM,CAAA;AACJ,IAAK,MAAA,MAAA,GAAA,QAAgB,CAAO,MAAA,WAAU,CAAA,KAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AACtC,IAAM,MAAA,oBAAsB,GAAA,CAAA,KAAA,EAAA,GAAA,KAAA;AAC5B,MAAA,IAAA,CAAA,cAAuB,EAAA,KAAS,EAAA,GAAA,EAAA,KAAA,CAAA,CAAA;AAAsB,MACxD,MAAA,OAAA,GAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAEA,MAAM,cAAA,CAAA,KAAA,GAAA,CAAA,KAAyC,GAAA,OAAA,EAAA,GAAA,GAAA,OAAA,CAAA,CAAA;AAC7C,KAAA,CAAA;AACA,IAAA,MAAA,oBAA0B,GAAA,CAAA,IAAA,KAAW;AAAA,MAAA,MACvB,IAAA,GAAA,WAAS,CAAA,KAAA,OAAc,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA;AAAA,MACrC,MAAA,OAAA,GAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,MAAA,MAAM,QAAQ,IAAK,CAAA,OAAA,CAAQ,cAAe,CAAA,KAAA,CAAM,CAAC,CAAC,CAAA,CAAA;AAClD,MAAA,MAAM,IAAQ,GAAA,CAAA,KAAA,GAAQ,IAAO,GAAA,IAAA,CAAK,UAAU,IAAK,CAAA,MAAA,CAAA;AACjD,MAAM,MAAA,IAAA,GAAO,KAAK,MAAS,GAAA,CAAA,CAAA;AAC3B,MAAA,IAAI,OAAO,IAAM,EAAA;AACf,QAAA,iBAAA,CAAkB,uBAAuB,CAAA,CAAE,OAAQ,CAAA,IAAI,CAAC,CAAA,CAAA;AAAA,OACnD,MAAA;AACL,QAAA,iBAAA,CAAkB,qBAAqB,CAAA,CAAE,OAAQ,CAAA,IAAA,GAAO,IAAI,CAAC,CAAA,CAAA;AAAA,OAC/D;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,CAAC,KAAyB,KAAA;AAC9C,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AAEnB,MAAA,MAAM,EAAE,IAAA,EAAM,KAAO,EAAA,EAAA,EAAI,MAAS,GAAA,UAAA,CAAA;AAElC,MAAA,IAAI,CAAC,IAAM,EAAA,KAAK,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAChC,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,IAAA,GAAO,CAAK,CAAA,GAAA,CAAA,CAAA;AAClC,QAAA,oBAAA,CAAqB,IAAI,CAAA,CAAA;AACzB,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,CAAC,EAAI,EAAA,IAAI,CAAE,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAC7B,QAAM,MAAA,IAAA,GAAO,IAAS,KAAA,EAAA,GAAK,CAAK,CAAA,GAAA,CAAA,CAAA;AAChC,QAAA,MAAM,OAAO,cAAe,CAAA,KAAA,CAAM,CAAC,CAAI,GAAA,MAAA,CAAO,QAAQ,OAAU,GAAA,KAAA,CAAA;AAChE,QAAA,iBAAA,CAAkB,CAAG,EAAA,IAAI,CAAa,WAAA,CAAA,CAAA,CAAE,IAAI,CAAA,CAAA;AAC5C,QAAA,KAAA,CAAM,cAAe,EAAA,CAAA;AACrB,QAAA,OAAA;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,IAAA,EAAc,OAAoB,KAAA;AACxD,MAAA,MAAM,cAAiB,GAAA,aAAA,GAAgB,aAAc,CAAA,IAAI,IAAI,EAAC,CAAA;AAC9D,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAY,OAAU,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,WAAc,GAAA,CAAA,EAAG,EAAE,CACnC,GAAA,eAAA,CAAgB,CAAG,EAAA,WAAA,GAAc,CAAC,CAAA,CAAA;AACtC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AACA,IAAA,MAAM,gBAAmB,GAAA,CAAC,IAAc,EAAA,IAAA,EAAc,OAAoB,KAAA;AACxE,MAAA,MAAM,iBAAiB,eAAkB,GAAA,eAAA,CAAgB,IAAM,EAAA,IAAI,IAAI,EAAC,CAAA;AACxE,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAY,OAAU,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAA,IAAI,SAAS,WAAa,EAAA;AACxB,QAAO,OAAA,cAAA,CAAA;AAAA,OACT;AACA,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,aAAgB,GAAA,CAAA,EAAG,EAAE,CACrC,GAAA,eAAA,CAAgB,CAAG,EAAA,aAAA,GAAgB,CAAC,CAAA,CAAA;AACxC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AACA,IAAA,MAAM,gBAAmB,GAAA,CACvB,IACA,EAAA,MAAA,EACA,MACA,OACG,KAAA;AACH,MAAA,MAAM,iBAAiB,eACnB,GAAA,eAAA,CAAgB,MAAM,MAAQ,EAAA,IAAI,IAClC,EAAC,CAAA;AACL,MAAA,MAAM,UAAU,IAAS,KAAA,OAAA,CAAA;AACzB,MAAA,MAAM,WAAc,GAAA,OAAA,KAAY,OAAU,GAAA,OAAA,CAAQ,QAAQ,SAAU,CAAA,KAAA,CAAA,CAAA;AACpE,MAAM,MAAA,WAAA,GAAc,YAAY,IAAK,EAAA,CAAA;AACrC,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAI,IAAA,IAAA,KAAS,WAAe,IAAA,MAAA,KAAW,aAAe,EAAA;AACpD,QAAO,OAAA,cAAA,CAAA;AAAA,OACT;AACA,MAAM,MAAA,aAAA,GAAgB,YAAY,MAAO,EAAA,CAAA;AACzC,MAAM,MAAA,WAAA,GAAc,OAChB,GAAA,eAAA,CAAgB,aAAgB,GAAA,CAAA,EAAG,EAAE,CACrC,GAAA,eAAA,CAAgB,CAAG,EAAA,aAAA,GAAgB,CAAC,CAAA,CAAA;AACxC,MAAO,OAAA,KAAA,CAAM,gBAAgB,WAAW,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,qBAAwB,GAAA,CAAC,CAAC,KAAA,EAAO,GAAG,CAAoB,KAAA;AAC5D,MAAO,OAAA;AAAA,QACL,gBAAiB,CAAA,KAAA,EAAO,OAAS,EAAA,IAAA,EAAM,GAAG,CAAA;AAAA,QAC1C,gBAAiB,CAAA,GAAA,EAAK,KAAO,EAAA,KAAA,EAAO,KAAK,CAAA;AAAA,OAC3C,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,EAAE,iBAAA,EAAmB,mBAAqB,EAAA,mBAAA,EAC9C,GAAA,4BAAA,CAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,CAAA,CAAA;AAAA,IACE,MAAA;AAAA,MACA,iBAAA;AAAA,MACA,gBAAA;AAAA,MACF,WAAA;AAEF,KAAM,GAAA,YAAA,CAAA;AAAA,MACJ,iBAAA;AAAA,MAEA,mBAAA;AAAA,MACA,mBAAA;AAAA;AACe,IACf,MAAA,cAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACA,IAAA,CAAA,IAAA;AAAA,QACA,OAAA,IAAA,CAAA;AAAA,MACD,IAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AAED,QAAM,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAA4C,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAChD,OAAI;AACJ,MAAI,OAAA,KAAQ,KAAO,EAAA,KAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACjB,KAAA,CAAA;AAAgE,IAClE,MAAA,cAAA,GAAA,CAAA,IAAA,KAAA;AACA,MAAA,IAAA,CAAA;AAAkD,QACpD,OAAA,IAAA,CAAA;AAEA,MAAM,IAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AACJ,QAAI,WAAc,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAClB,OAAI;AACF,MAAO,OAAA,IAAA,CAAA,MAAS,CAAC,KAAA,CAAM,MAAS,CAAA,CAAA;AAAa,KAC/C,CAAA;AACA,IAAO,MAAA,eAAY,GAAA,MAAY;AAAA,MACjC,IAAA,OAAA,CAAA,YAAA,CAAA,EAAA;AAEA,QAAA,oBAAwB,GAAM,CAAA,CAAA,CAAA,KAAA,KAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAC5B,OAAI;AACF,MAAO,MAAA,UAAA,GAAA,KAAiB,CAAA,YAAa,CAAA,CAAM,MAAG,CAAA,IAAY,CAAA,KAAA,CAAA,CAAA;AAAM,MAClE,OAAA,CAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,EAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,mBAAoB,EAAA,CAAA,kBAAuB,cAAA,CAAA,CAAA,CAAA;AAAA,IAC7C,IAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAEA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAkB,EAAA,YAAA,CAAA,CAAA,CAAA;AAC7C,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,oBAAkB,EAAA,aAAe,CAAA,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,iBAAgB,EAAA,eAAa,CAAA,CAAA,CAAA;AACxD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,uBAAsB,EAAA,qBAAc,CAAA,CAAA,CAAA;AAC/D,IAAA,OAA0B,CAAA,IAAA,EAAA,MAAA,KAAA;AAC1B,MAAA,OAA0B,IAAA,CAAA,aAAC,IAAyBA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
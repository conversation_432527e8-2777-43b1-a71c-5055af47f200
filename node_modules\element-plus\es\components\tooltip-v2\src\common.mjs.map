{"version": 3, "file": "common.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/common.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\n/**\n * TODO: make this under constants or tokens\n */\nexport const tooltipV2CommonProps = buildProps({\n  nowrap: Boolean,\n} as const)\n\nexport type TooltipV2CommonProps = ExtractPropTypes<typeof tooltipV2CommonProps>\n\nexport enum TooltipV2Sides {\n  top = 'top',\n  bottom = 'bottom',\n  left = 'left',\n  right = 'right',\n}\n\nexport const tooltipV2Sides = Object.values(TooltipV2Sides)\n\nexport const tooltipV2OppositeSide = {\n  [TooltipV2Sides.top]: TooltipV2Sides.bottom,\n  [TooltipV2Sides.bottom]: TooltipV2Sides.top,\n  [TooltipV2Sides.left]: TooltipV2Sides.right,\n  [TooltipV2Sides.right]: TooltipV2Sides.left,\n} as const\n\nexport const tooltipV2ArrowBorders = {\n  [TooltipV2Sides.top]: [TooltipV2Sides.left, TooltipV2Sides.top],\n  [TooltipV2Sides.bottom]: [TooltipV2Sides.bottom, TooltipV2Sides.right],\n  [TooltipV2Sides.left]: [TooltipV2Sides.bottom, TooltipV2Sides.left],\n  [TooltipV2Sides.right]: [TooltipV2Sides.top, TooltipV2Sides.right],\n} as const\n"], "names": [], "mappings": ";;AACY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,MAAM,EAAE,OAAO;AACjB,CAAC,EAAE;AACO,IAAC,cAAc,mBAAmB,CAAC,CAAC,eAAe,KAAK;AAClE,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACjC,EAAE,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACvC,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACnC,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AACrC,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC,EAAE,cAAc,IAAI,EAAE,EAAE;AACb,MAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;AAChD,MAAC,qBAAqB,GAAG;AACrC,EAAE,CAAC,KAAK,aAAa,QAAQ;AAC7B,EAAE,CAAC,QAAQ,gBAAgB,KAAK;AAChC,EAAE,CAAC,MAAM,cAAc,OAAO;AAC9B,EAAE,CAAC,OAAO,eAAe,MAAM;AAC/B,EAAE;AACU,MAAC,qBAAqB,GAAG;AACrC,EAAE,CAAC,KAAK,aAAa,CAAC,MAAM,aAAa,KAAK,WAAW;AACzD,EAAE,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,eAAe,OAAO,aAAa;AACvE,EAAE,CAAC,MAAM,cAAc,CAAC,QAAQ,eAAe,MAAM,YAAY;AACjE,EAAE,CAAC,OAAO,eAAe,CAAC,KAAK,YAAY,OAAO,aAAa;AAC/D;;;;"}
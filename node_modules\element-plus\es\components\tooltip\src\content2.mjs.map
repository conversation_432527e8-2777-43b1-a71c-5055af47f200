{"version": 3, "file": "content2.mjs", "sources": ["../../../../../../packages/components/tooltip/src/content.vue"], "sourcesContent": ["<template>\n  <el-teleport :disabled=\"!teleported\" :to=\"appendTo\">\n    <transition\n      :name=\"transitionClass\"\n      @after-leave=\"onTransitionLeave\"\n      @before-enter=\"onBeforeEnter\"\n      @after-enter=\"onAfterShow\"\n      @before-leave=\"onBeforeLeave\"\n    >\n      <el-popper-content\n        v-if=\"shouldRender\"\n        v-show=\"shouldShow\"\n        :id=\"id\"\n        ref=\"contentRef\"\n        v-bind=\"$attrs\"\n        :aria-label=\"ariaLabel\"\n        :aria-hidden=\"ariaHidden\"\n        :boundaries-padding=\"boundariesPadding\"\n        :fallback-placements=\"fallbackPlacements\"\n        :gpu-acceleration=\"gpuAcceleration\"\n        :offset=\"offset\"\n        :placement=\"placement\"\n        :popper-options=\"popperOptions\"\n        :strategy=\"strategy\"\n        :effect=\"effect\"\n        :enterable=\"enterable\"\n        :pure=\"pure\"\n        :popper-class=\"popperClass\"\n        :popper-style=\"[popperStyle, contentStyle]\"\n        :reference-el=\"referenceEl\"\n        :trigger-target-el=\"triggerTargetEl\"\n        :visible=\"shouldShow\"\n        :z-index=\"zIndex\"\n        @mouseenter=\"onContentEnter\"\n        @mouseleave=\"onContentLeave\"\n        @blur=\"onBlur\"\n        @close=\"onClose\"\n      >\n        <slot />\n      </el-popper-content>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, unref, watch } from 'vue'\nimport { computedEager, onClickOutside } from '@vueuse/core'\nimport { useNamespace, usePopperContainerId } from '@element-plus/hooks'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { ElPopperContent } from '@element-plus/components/popper'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { tryFocus } from '@element-plus/components/focus-trap'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { useTooltipContentProps } from './content'\nimport type { PopperContentInstance } from '@element-plus/components/popper'\n\ndefineOptions({\n  name: 'ElTooltipContent',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(useTooltipContentProps)\n\nconst { selector } = usePopperContainerId()\nconst ns = useNamespace('tooltip')\n\nconst contentRef = ref<PopperContentInstance>()\nconst popperContentRef = computedEager(() => contentRef.value?.popperContentRef)\nlet stopHandle: ReturnType<typeof onClickOutside>\nconst {\n  controlled,\n  id,\n  open,\n  trigger,\n  onClose,\n  onOpen,\n  onShow,\n  onHide,\n  onBeforeShow,\n  onBeforeHide,\n} = inject(TOOLTIP_INJECTION_KEY, undefined)!\nconst transitionClass = computed(() => {\n  return props.transition || `${ns.namespace.value}-fade-in-linear`\n})\nconst persistentRef = computed(() => {\n  // For testing, we would always want the content to be rendered\n  // to the DOM, so we need to return true here.\n  if (process.env.NODE_ENV === 'test') {\n    return true\n  }\n  return props.persistent\n})\n\nonBeforeUnmount(() => {\n  stopHandle?.()\n})\n\nconst shouldRender = computed(() => {\n  return unref(persistentRef) ? true : unref(open)\n})\n\nconst shouldShow = computed(() => {\n  return props.disabled ? false : unref(open)\n})\n\nconst appendTo = computed(() => {\n  return props.appendTo || selector.value\n})\n\nconst contentStyle = computed(() => (props.style ?? {}) as any)\n\nconst ariaHidden = ref(true)\n\nconst onTransitionLeave = () => {\n  onHide()\n  isFocusInsideContent() && tryFocus(document.body)\n  ariaHidden.value = true\n}\n\nconst stopWhenControlled = () => {\n  if (unref(controlled)) return true\n}\n\nconst onContentEnter = composeEventHandlers(stopWhenControlled, () => {\n  if (props.enterable && unref(trigger) === 'hover') {\n    onOpen()\n  }\n})\n\nconst onContentLeave = composeEventHandlers(stopWhenControlled, () => {\n  if (unref(trigger) === 'hover') {\n    onClose()\n  }\n})\n\nconst onBeforeEnter = () => {\n  contentRef.value?.updatePopper?.()\n  onBeforeShow?.()\n}\n\nconst onBeforeLeave = () => {\n  onBeforeHide?.()\n}\n\nconst onAfterShow = () => {\n  onShow()\n  stopHandle = onClickOutside(popperContentRef, () => {\n    if (unref(controlled)) return\n    const $trigger = unref(trigger)\n    if ($trigger !== 'hover') {\n      onClose()\n    }\n  })\n}\n\nconst onBlur = () => {\n  if (!props.virtualTriggering) {\n    onClose()\n  }\n}\n\nconst isFocusInsideContent = (event?: FocusEvent) => {\n  const popperContent: HTMLElement | undefined =\n    contentRef.value?.popperContentRef\n  const activeElement = (event?.relatedTarget as Node) || document.activeElement\n\n  return popperContent?.contains(activeElement)\n}\n\nwatch(\n  () => unref(open),\n  (val) => {\n    if (!val) {\n      stopHandle?.()\n    } else {\n      ariaHidden.value = false\n    }\n  },\n  {\n    flush: 'post',\n  }\n)\n\nwatch(\n  () => props.content,\n  () => {\n    contentRef.value?.updatePopper?.()\n  }\n)\n\ndefineExpose({\n  /**\n   * @description el-popper-content component instance\n   */\n  contentRef,\n  /**\n   * @description validate current focus event is trigger inside el-popper-content\n   */\n  isFocusInsideContent,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref"], "mappings": ";;;;;;;;;;;;;mCAwDc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,oBAAqB,EAAA,CAAA;AAC1C,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AAEjC,IAAA,MAAM,aAAa,GAA2B,EAAA,CAAA;AAC9C,IAAA,MAAM,gBAAmB,GAAA,aAAA,CAAc,MAAM;AAC7C,MAAI,IAAA,EAAA,CAAA;AACJ,MAAM,OAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AAAA,KACJ,CAAA,CAAA;AAAA,IACA,IAAA,UAAA,CAAA;AAAA,IACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,EAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACF,MAAW;AACX,MAAM,YAAA;AACJ,MAAA,YAAa;AAAmC,KACjD,GAAA,MAAA,CAAA,qBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACD,IAAM,MAAA,eAAA,WAA+B,CAAA,MAAA;AAGnC,MAAI,OAAA,KAAQ,CAAI,UAAA,IAAA,CAAA,EAAqB,EAAA,CAAA,SAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AACnC,KAAO,CAAA,CAAA;AAAA,IACT,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,IAAA,OAAa,CAAA,GAAA,CAAA,QAAA,KAAA,MAAA,EAAA;AAAA,QACd,OAAA,IAAA,CAAA;AAED,OAAA;AACE,MAAa,OAAA,KAAA,CAAA,UAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAED,IAAM,eAAA,CAAA;AACJ,MAAA,UAAa,IAAA,IAAA,GAAA,KAAa,CAAI,GAAA,aAAa;AAAI,KAChD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,WAA4B,CAAA,MAAA;AAChC,MAAA,OAAO,KAAM,CAAA,aAAmB,CAAA,GAAA,IAAA,GAAA,KAAU,CAAA,IAAA,CAAA,CAAA;AAAA,KAC3C,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,WAA0B,CAAA,MAAA;AAC9B,MAAO,OAAA,KAAA,CAAM,gBAAqB,GAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAA,MAAM,mBAAwB,CAAA,MAAA;AAE9B,MAAM,OAAA,KAAA,CAAA,QAAiB,IAAI,QAAA,CAAA,KAAA,CAAA;AAE3B,KAAA,CAAA,CAAA;AACE,IAAO,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACP,MAAqB,IAAA,EAAA,CAAA;AACrB,MAAA,OAAA,CAAA,EAAA,GAAmB,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAAA,KACrB,CAAA,CAAA;AAEA,IAAA,MAAM,qBAAqB,CAAM,CAAA;AAC/B,IAAI,MAAA,iBAAgB,GAAU,MAAA;AAAA,MAChC,MAAA,EAAA,CAAA;AAEA,MAAM,oBAAA,EAAsC,IAAA,QAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAC1C,MAAA,UAAU,CAAA,KAAA,GAAA,IAAa,CAAM;AAC3B,KAAO,CAAA;AAAA,IACT,MAAA,kBAAA,GAAA,MAAA;AAAA,MACD,IAAA,KAAA,CAAA,UAAA,CAAA;AAED,QAAM,OAAA,IAAA,CAAA;AACJ,KAAI,CAAA;AACF,IAAQ,MAAA,cAAA,GAAA,oBAAA,CAAA,kBAAA,EAAA,MAAA;AAAA,MACV,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,OAAA,EAAA;AAAA,QACD,MAAA,EAAA,CAAA;AAED,OAAA;AACE,KAAA,CAAA,CAAA;AACA,IAAe,MAAA,cAAA,GAAA,oBAAA,CAAA,kBAAA,EAAA,MAAA;AAAA,MACjB,IAAA,KAAA,CAAA,OAAA,CAAA,KAAA,OAAA,EAAA;AAEA,QAAA;AACE,OAAe;AAAA,KACjB,CAAA,CAAA;AAEA,IAAA,MAAM,gBAAoB,MAAA;AACxB,MAAO,IAAA,EAAA,EAAA,EAAA,CAAA;AACP,MAAa,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAe,kBAAkB,GAAM,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAClD,MAAI,YAAM,WAAa,KAAA,CAAA,GAAA,YAAA,EAAA,CAAA;AACvB,KAAM,CAAA;AACN,IAAA,MAAA,gBAA0B,MAAA;AACxB,MAAQ,YAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,YAAA,EAAA,CAAA;AAAA,KACV,CAAA;AAAA,IAAA,MACD,WAAA,GAAA,MAAA;AAAA,MACH,MAAA,EAAA,CAAA;AAEA,MAAA,aAAe,cAAM,CAAA,gBAAA,EAAA,MAAA;AACnB,QAAI,SAAO,CAAmB,UAAA,CAAA;AAC5B,UAAQ,OAAA;AAAA,QACV,MAAA,QAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACF,IAAA,QAAA,KAAA,OAAA,EAAA;AAEA,UAAM,OAAA,EAAA,CAAA;AACJ,SAAM;AAEN,OAAM,CAAA,CAAA;AAEN,KAAO,CAAA;AAAqC,IAC9C,MAAA,MAAA,GAAA,MAAA;AAEA,MAAA,IAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,QACE,UAAY;AAAI,OACf;AACC,KAAA,CAAA;AACE,IAAa,MAAA,oBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MAAA,IACR,EAAA,CAAA;AACL,MAAA,MAAA,aAAmB,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAA,CAAA;AAAA,MACrB,MAAA,aAAA,GAAA,CAAA,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,CAAA,aAAA,KAAA,QAAA,CAAA,aAAA,CAAA;AAAA,MACF,OAAA,aAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IAAA,KACS,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACT,IAAA,CAAA,GAAA,EAAA;AAAA,QACF,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAEA,OAAA,MAAA;AAAA,kBACc,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,OACN;AACJ,KAAA,EAAA;AAAiC,MACnC,KAAA,EAAA,MAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAa,KAAA,CAAA,MAAA,KAAA,CAAA,OAAA,EAAA,MAAA;AAAA,MAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAIX,MAAA,CAAA;AAAA,MAAA,UAAA;AAAA,MAAA,oBAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAIA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,UAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
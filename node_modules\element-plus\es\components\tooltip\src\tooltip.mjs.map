{"version": 3, "file": "tooltip.mjs", "sources": ["../../../../../../packages/components/tooltip/src/tooltip.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { createModelToggleComposable } from '@element-plus/hooks'\nimport { popperArrowProps, popperProps } from '@element-plus/components/popper'\nimport { useTooltipContentProps } from './content'\nimport { useTooltipTriggerProps } from './trigger'\nimport type Tooltip from './tooltip.vue'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const {\n  useModelToggleProps: useTooltipModelToggleProps,\n  useModelToggleEmits: useTooltipModelToggleEmits,\n  useModelToggle: useTooltipModelToggle,\n} = createModelToggleComposable('visible' as const)\n\nexport const useTooltipProps = buildProps({\n  ...popperProps,\n  ...useTooltipModelToggleProps,\n  ...useTooltipContentProps,\n  ...useTooltipTriggerProps,\n  ...popperArrowProps,\n  /**\n   * @description whether the tooltip content has an arrow\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport const tooltipEmits = [\n  ...useTooltipModelToggleEmits,\n  'before-show',\n  'before-hide',\n  'show',\n  'hide',\n  'open',\n  'close',\n]\n\nexport type ElTooltipProps = ExtractPropTypes<typeof useTooltipProps>\n\nexport type TooltipInstance = InstanceType<typeof Tooltip> & unknown\n"], "names": [], "mappings": ";;;;;;;AAKY,MAAC;AACb,EAAE,mBAAmB,EAAE,0BAA0B;AACjD,EAAE,mBAAmB,EAAE,0BAA0B;AACjD,EAAE,cAAc,EAAE,qBAAqB;AACvC,CAAC,GAAG,2BAA2B,CAAC,SAAS,EAAE;AAC/B,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,GAAG,WAAW;AAChB,EAAE,GAAG,0BAA0B;AAC/B,EAAE,GAAG,sBAAsB;AAC3B,EAAE,GAAG,sBAAsB;AAC3B,EAAE,GAAG,gBAAgB;AACrB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,GAAG,0BAA0B;AAC/B,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,OAAO;AACT;;;;"}
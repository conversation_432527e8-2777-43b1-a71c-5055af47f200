{"version": 3, "file": "visual-hidden.mjs", "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { StyleValue } from 'vue'\n\nexport const visualHiddenProps = buildProps({\n  style: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: () => ({}),\n  },\n} as const)\n"], "names": [], "mappings": ";;AACY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,CAAC;;;;"}
{"version": 3, "file": "watermark.mjs", "sources": ["../../../../../../packages/components/watermark/src/watermark.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Watermark from './watermark.vue'\n\nexport interface WatermarkFontType {\n  color?: string\n  fontSize?: number | string\n  fontWeight?: 'normal' | 'light' | 'weight' | number\n  fontStyle?: 'none' | 'normal' | 'italic' | 'oblique'\n  fontFamily?: string\n  textAlign?: 'start' | 'end' | 'left' | 'right' | 'center'\n  textBaseline?:\n    | 'top'\n    | 'hanging'\n    | 'middle'\n    | 'alphabetic'\n    | 'ideographic'\n    | 'bottom'\n}\n\nexport const watermarkProps = buildProps({\n  /**\n   * @description The z-index of the appended watermark element\n   */\n  zIndex: {\n    type: Number,\n    default: 9,\n  },\n  /**\n   * @description The rotation angle of the watermark\n   */\n  rotate: {\n    type: Number,\n    default: -22,\n  },\n  /**\n   * @description The width of the watermark\n   */\n  width: Number,\n  /**\n   * @description The height of the watermark\n   */\n  height: Number,\n  /**\n   * @description Image source, it is recommended to export 2x or 3x image, high priority (support base64 format)\n   */\n  image: String,\n  /**\n   * @description Watermark text content\n   */\n  content: {\n    type: definePropType<string | string[]>([String, Array]),\n    default: 'Element Plus',\n  },\n  /**\n   * @description Text style\n   */\n  font: {\n    type: definePropType<WatermarkFontType>(Object),\n  },\n  /**\n   * @description The spacing between watermarks\n   */\n  gap: {\n    type: definePropType<[number, number]>(Array),\n    default: () => [100, 100],\n  },\n  /**\n   * @description The offset of the watermark from the upper left corner of the container. The default is gap/2\n   */\n  offset: {\n    type: definePropType<[number, number]>(Array),\n  },\n} as const)\n\nexport type WatermarkProps = ExtractPropTypes<typeof watermarkProps>\nexport type WatermarkInstance = InstanceType<typeof Watermark> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC,EAAE;AAChB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,CAAC;;;;"}
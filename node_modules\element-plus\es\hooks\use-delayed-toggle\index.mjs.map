{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-delayed-toggle/index.ts"], "sourcesContent": ["import { unref } from 'vue'\nimport { buildProps, isNumber } from '@element-plus/utils'\nimport { useTimeout } from '../use-timeout'\n\nimport type { ExtractPropTypes, ToRefs } from 'vue'\n\nexport const useDelayedToggleProps = buildProps({\n  /**\n   * @description delay of appearance, in millisecond\n   */\n  showAfter: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description delay of disappear, in millisecond\n   */\n  hideAfter: {\n    type: Number,\n    default: 200,\n  },\n  /**\n   * @description disappear automatically, in millisecond\n   */\n  autoClose: {\n    type: Number,\n    default: 0,\n  },\n} as const)\n\nexport type UseDelayedToggleProps = {\n  open: (event?: Event) => void\n  close: (event?: Event) => void\n} & ToRefs<ExtractPropTypes<typeof useDelayedToggleProps>>\n\nexport const useDelayedToggle = ({\n  showAfter,\n  hideAfter,\n  autoClose,\n  open,\n  close,\n}: UseDelayedToggleProps) => {\n  const { registerTimeout } = useTimeout()\n  const {\n    registerTimeout: registerTimeoutForAutoClose,\n    cancelTimeout: cancelTimeoutForAutoClose,\n  } = useTimeout()\n\n  const onOpen = (event?: Event) => {\n    registerTimeout(() => {\n      open(event)\n\n      const _autoClose = unref(autoClose)\n      if (isNumber(_autoClose) && _autoClose > 0) {\n        registerTimeoutForAutoClose(() => {\n          close(event)\n        }, _autoClose)\n      }\n    }, unref(showAfter))\n  }\n\n  const onClose = (event?: Event) => {\n    cancelTimeoutForAutoClose()\n\n    registerTimeout(() => {\n      close(event)\n    }, unref(hideAfter))\n  }\n\n  return {\n    onOpen,\n    onClose,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,KAAK;AACP,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,UAAU,EAAE,CAAC;AAC3C,EAAE,MAAM;AACR,IAAI,eAAe,EAAE,2BAA2B;AAChD,IAAI,aAAa,EAAE,yBAAyB;AAC5C,GAAG,GAAG,UAAU,EAAE,CAAC;AACnB,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC5B,IAAI,eAAe,CAAC,MAAM;AAC1B,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AAC1C,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE;AAClD,QAAQ,2BAA2B,CAAC,MAAM;AAC1C,UAAU,KAAK,CAAC,KAAK,CAAC,CAAC;AACvB,SAAS,EAAE,UAAU,CAAC,CAAC;AACvB,OAAO;AACP,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC7B,IAAI,yBAAyB,EAAE,CAAC;AAChC,IAAI,eAAe,CAAC,MAAM;AAC1B,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC;AACnB,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,MAAM;AACV,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}
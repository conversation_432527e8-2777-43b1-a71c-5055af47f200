{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-prop/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance } from 'vue'\nimport type { ComputedRef } from 'vue'\n\nexport const useProp = <T>(name: string): ComputedRef<T | undefined> => {\n  const vm = getCurrentInstance()\n  return computed(() => (vm?.proxy?.$props as any)?.[name])\n}\n"], "names": [], "mappings": ";;AACY,MAAC,OAAO,GAAG,CAAC,IAAI,KAAK;AACjC,EAAE,MAAM,EAAE,GAAG,kBAAkB,EAAE,CAAC;AAClC,EAAE,OAAO,QAAQ,CAAC,MAAM;AACxB,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AACjH,GAAG,CAAC,CAAC;AACL;;;;"}
import installer from './defaults';
export * from 'element-plus/es/components';
export * from 'element-plus/es/constants';
export * from 'element-plus/es/directives';
export * from 'element-plus/es/hooks';
export * from './make-installer';
export declare const install: (app: import("vue").App, options?: import("element-plus/es/components").ConfigProviderContext) => void;
export declare const version: string;
export default installer;
export { default as dayjs } from 'dayjs';

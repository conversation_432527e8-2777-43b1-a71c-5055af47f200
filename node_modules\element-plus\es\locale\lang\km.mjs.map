{"version": 3, "file": "km.mjs", "sources": ["../../../../../packages/locale/lang/km.ts"], "sourcesContent": ["export default {\n  name: 'km',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'យល់ព្រម',\n      clear: 'លុប',\n    },\n    datepicker: {\n      now: 'ឥឡូវ​នេះ',\n      today: 'ថ្ងៃនេះ',\n      cancel: 'បោះបង់',\n      clear: 'លុប',\n      confirm: 'យល់ព្រម',\n      selectDate: 'ជ្រើសរើសថ្ងៃ',\n      selectTime: 'ជ្រើសរើសម៉ោង',\n      startDate: 'ថ្ងៃចាប់ផ្តើម',\n      startTime: 'ម៉ោងចាប់ផ្តើម',\n      endDate: 'ថ្ងៃបញ្ចប់',\n      endTime: 'ម៉ោងបញ្ចប់',\n      prevYear: 'ឆ្នាំមុន',\n      nextYear: 'ឆ្នាំក្រោយ',\n      prevMonth: 'ខែមុន',\n      nextMonth: 'ខែក្រោយ',\n      year: 'ឆ្នាំ',\n      month1: 'មករា',\n      month2: 'កុម្ភៈ',\n      month3: 'មីនា',\n      month4: 'មេសា',\n      month5: 'ឧសភា',\n      month6: 'មិថុនា',\n      month7: 'កក្កដា',\n      month8: 'សីហា',\n      month9: 'កញ្ញា',\n      month10: 'តុលា',\n      month11: 'វិច្ឆិកា',\n      month12: 'ធ្នូ',\n      // week: 'សប្តាហ៍',\n      weeks: {\n        sun: 'អាទិត្យ',\n        mon: 'ចន្ទ',\n        tue: 'អង្គារ',\n        wed: 'ពុធ',\n        thu: 'ព្រហ',\n        fri: 'សុក្រ',\n        sat: 'សៅរ៍',\n      },\n      months: {\n        jan: 'មករា',\n        feb: 'កុម្ភៈ',\n        mar: 'មីនា',\n        apr: 'មេសា',\n        may: 'ឧសភា',\n        jun: 'មិថុនា',\n        jul: 'កក្កដា',\n        aug: 'សីហា',\n        sep: 'កញ្ញា',\n        oct: 'តុលា',\n        nov: 'វិច្ឆិកា',\n        dec: 'ធ្នូ',\n      },\n    },\n    select: {\n      loading: 'កំពុងផ្ទុក',\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      noData: 'គ្មានទិន្នន័យ',\n      placeholder: 'ជ្រើសរើស',\n    },\n    mention: {\n      loading: 'កំពុងផ្ទុក',\n    },\n    cascader: {\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      loading: 'កំពុងផ្ទុក',\n      placeholder: 'ជ្រើសរើស',\n      noData: 'គ្មានទិន្នន័យ',\n    },\n    pagination: {\n      goto: 'ទៅកាន់',\n      pagesize: '/ទំព័រ',\n      total: 'សរុប {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'សារ',\n      confirm: 'យល់ព្រម',\n      cancel: 'បោះបង់',\n      error: 'ការបញ្ចូលមិនត្រូវបានអនុញ្ញាត',\n    },\n    upload: {\n      deleteTip: 'ចុចលុបដើម្បីដកចេញ',\n      delete: 'លុប',\n      preview: 'មើល',\n      continue: 'បន្ត',\n    },\n    table: {\n      emptyText: 'គ្មានទិន្នន័យ',\n      confirmFilter: 'យល់ព្រម',\n      resetFilter: 'កំណត់ឡើងវិញ',\n      clearFilter: 'ទាំងអស់',\n      sumText: 'បូក',\n    },\n    tree: {\n      emptyText: 'គ្មានទិន្នន័យ',\n    },\n    transfer: {\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      noData: 'គ្មានទិន្នន័យ',\n      titles: ['បញ្ជី ១', 'បញ្ជី ២'],\n      filterPlaceholder: 'បញ្ចូលពាក្យ',\n      noCheckedFormat: '{total} ធាតុ',\n      hasCheckedFormat: '{checked}/{total} បានជ្រើសយក',\n    },\n    image: {\n      error: 'មិនបានជោគជ័យ',\n    },\n    pageHeader: {\n      title: 'ត្រលប់ក្រោយ',\n    },\n    popconfirm: {\n      confirmButtonText: 'យល់ព្រម',\n      cancelButtonText: 'មិនព្រម',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,kDAAkD;AAC7D,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,UAAU,EAAE,0EAA0E;AAC5F,MAAM,UAAU,EAAE,0EAA0E;AAC5F,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,SAAS,EAAE,4CAA4C;AAC7D,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,4CAA4C;AACzD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,OAAO,EAAE,kGAAkG;AACjH,MAAM,MAAM,EAAE,gFAAgF;AAC9F,MAAM,WAAW,EAAE,kDAAkD;AACrE,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,8DAA8D;AAC7E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kGAAkG;AACjH,MAAM,OAAO,EAAE,8DAA8D;AAC7E,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,MAAM,EAAE,gFAAgF;AAC9F,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,QAAQ,EAAE,iCAAiC;AACjD,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,0KAA0K;AACvL,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wGAAwG;AACzH,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gFAAgF;AACjG,MAAM,aAAa,EAAE,4CAA4C;AACjE,MAAM,WAAW,EAAE,oEAAoE;AACvF,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gFAAgF;AACjG,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kGAAkG;AACjH,MAAM,MAAM,EAAE,gFAAgF;AAC9F,MAAM,MAAM,EAAE,CAAC,uCAAuC,EAAE,uCAAuC,CAAC;AAChG,MAAM,iBAAiB,EAAE,oEAAoE;AAC7F,MAAM,eAAe,EAAE,kCAAkC;AACzD,MAAM,gBAAgB,EAAE,gFAAgF;AACxG,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,0EAA0E;AACvF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oEAAoE;AACjF,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,4CAA4C;AACrE,MAAM,gBAAgB,EAAE,4CAA4C;AACpE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}
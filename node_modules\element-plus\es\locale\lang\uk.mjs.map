{"version": 3, "file": "uk.mjs", "sources": ["../../../../../packages/locale/lang/uk.ts"], "sourcesContent": ["export default {\n  name: 'uk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Очистити',\n    },\n    datepicker: {\n      now: 'Зараз',\n      today: 'Сьогодні',\n      cancel: 'Відміна',\n      clear: 'Очистити',\n      confirm: 'OK',\n      selectDate: 'Вибрати дату',\n      selectTime: 'Вибрати час',\n      startDate: 'Дата початку',\n      startTime: 'Час початку',\n      endDate: 'Дата завершення',\n      endTime: 'Час завершення',\n      prevYear: 'Попередній Рік',\n      nextYear: 'Наступний Рік',\n      prevMonth: 'Попередній Місяць',\n      nextMonth: 'Наступний Місяць',\n      year: '',\n      month1: 'Січень',\n      month2: 'Лютий',\n      month3: 'Березень',\n      month4: 'Квітень',\n      month5: 'Травень',\n      month6: 'Червень',\n      month7: 'Липень',\n      month8: 'Серпень',\n      month9: 'Вересень',\n      month10: 'Жовтень',\n      month11: 'Листопад',\n      month12: 'Грудень',\n      week: 'тиждень',\n      weeks: {\n        sun: 'Нд',\n        mon: 'Пн',\n        tue: 'Вт',\n        wed: 'Ср',\n        thu: 'Чт',\n        fri: 'Пт',\n        sat: 'Сб',\n      },\n      months: {\n        jan: 'Січ',\n        feb: 'Лют',\n        mar: 'Бер',\n        apr: 'Кві',\n        may: 'Тра',\n        jun: 'Чер',\n        jul: 'Лип',\n        aug: 'Сер',\n        sep: 'Вер',\n        oct: 'Жов',\n        nov: 'Лис',\n        dec: 'Гру',\n      },\n    },\n    select: {\n      loading: 'Завантаження',\n      noMatch: 'Співпадінь не знайдено',\n      noData: 'Немає даних',\n      placeholder: 'Обрати',\n    },\n    mention: {\n      loading: 'Завантаження',\n    },\n    cascader: {\n      noMatch: 'Співпадінь не знайдено',\n      loading: 'Завантаження',\n      placeholder: 'Обрати',\n      noData: 'Немає даних',\n    },\n    pagination: {\n      goto: 'Перейти',\n      pagesize: 'на сторінці',\n      total: 'Всього {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Повідомлення',\n      confirm: 'OK',\n      cancel: 'Відміна',\n      error: 'Неприпустимий ввід даних',\n    },\n    upload: {\n      deleteTip: 'натисніть кнопку щоб видалити',\n      delete: 'Видалити',\n      preview: 'Перегляд',\n      continue: 'Продовжити',\n    },\n    table: {\n      emptyText: 'Немає даних',\n      confirmFilter: 'Підтвердити',\n      resetFilter: 'Скинути',\n      clearFilter: 'Все',\n      sumText: 'Сума',\n    },\n    tour: {\n      next: 'Далі',\n      previous: 'Назад',\n      finish: 'Завершити',\n    },\n    tree: {\n      emptyText: 'Немає даних',\n    },\n    transfer: {\n      noMatch: 'Співпадінь не знайдено',\n      noData: 'Обрати',\n      titles: ['Список 1', 'Список 2'],\n      filterPlaceholder: 'Введіть ключове слово',\n      noCheckedFormat: '{total} пунктів',\n      hasCheckedFormat: '{checked}/{total} вибрано',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,kDAAkD;AAC/D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,gCAAgC;AAC3C,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,UAAU,EAAE,+DAA+D;AACjF,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,QAAQ,EAAE,iFAAiF;AACjG,MAAM,QAAQ,EAAE,2EAA2E;AAC3F,MAAM,SAAS,EAAE,mGAAmG;AACpH,MAAM,SAAS,EAAE,6FAA6F;AAC9G,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,0EAA0E;AACzF,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,MAAM,EAAE,+DAA+D;AAC7E,MAAM,WAAW,EAAE,sCAAsC;AACzD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,0EAA0E;AACzF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,OAAO,EAAE,0EAA0E;AACzF,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,MAAM,EAAE,+DAA+D;AAC7E,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,QAAQ,EAAE,+DAA+D;AAC/E,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,0EAA0E;AACvF,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,KAAK,EAAE,wIAAwI;AACrJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iKAAiK;AAClL,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,aAAa,EAAE,oEAAoE;AACzF,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,0BAA0B;AACzC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,QAAQ,EAAE,gCAAgC;AAChD,MAAM,MAAM,EAAE,wDAAwD;AACtE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,+DAA+D;AAChF,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4HAA4H;AAC3I,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;AAClG,MAAM,iBAAiB,EAAE,sHAAsH;AAC/I,MAAM,eAAe,EAAE,oDAAoD;AAC3E,MAAM,gBAAgB,EAAE,8DAA8D;AACtF,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}
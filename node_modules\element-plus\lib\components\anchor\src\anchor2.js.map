{"version": 3, "file": "anchor2.js", "sources": ["../../../../../../packages/components/anchor/src/anchor.vue"], "sourcesContent": ["<template>\n  <div ref=\"anchorRef\" :class=\"cls\">\n    <div\n      v-if=\"marker\"\n      ref=\"markerRef\"\n      :class=\"ns.e('marker')\"\n      :style=\"markerStyle\"\n    />\n    <div :class=\"ns.e('list')\">\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, provide, ref, watch } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  animateScrollTo,\n  getElement,\n  getOffsetTopDistance,\n  getScrollElement,\n  getScrollTop,\n  isUndefined,\n  isWindow,\n  throttleByRaf,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { anchorEmits, anchorProps } from './anchor'\nimport { anchorKey } from './constants'\n\nimport type { AnchorLinkState } from './constants'\n\ndefineOptions({\n  name: 'ElAnchor',\n})\n\nconst props = defineProps(anchorProps)\nconst emit = defineEmits(anchorEmits)\n\nconst currentAnchor = ref('')\nconst anchorRef = ref<HTMLElement | null>(null)\nconst markerRef = ref<HTMLElement | null>(null)\nconst containerEl = ref<HTMLElement | Window>()\n\nconst links: Record<string, HTMLElement> = {}\nlet isScrolling = false\nlet currentScrollTop = 0\n\nconst ns = useNamespace('anchor')\n\nconst cls = computed(() => [\n  ns.b(),\n  props.type === 'underline' ? ns.m('underline') : '',\n  ns.m(props.direction),\n])\n\nconst addLink = (state: AnchorLinkState) => {\n  links[state.href] = state.el\n}\n\nconst removeLink = (href: string) => {\n  delete links[href]\n}\n\nconst setCurrentAnchor = (href: string) => {\n  const activeHref = currentAnchor.value\n  if (activeHref !== href) {\n    currentAnchor.value = href\n    emit(CHANGE_EVENT, href)\n  }\n}\n\nlet clearAnimate: (() => void) | null = null\n\nconst scrollToAnchor = (href: string) => {\n  if (!containerEl.value) return\n  const target = getElement(href)\n  if (!target) return\n  if (clearAnimate) clearAnimate()\n  isScrolling = true\n  const scrollEle = getScrollElement(target, containerEl.value)\n  const distance = getOffsetTopDistance(target, scrollEle)\n  const max = scrollEle.scrollHeight - scrollEle.clientHeight\n  const to = Math.min(distance - props.offset, max)\n  clearAnimate = animateScrollTo(\n    containerEl.value,\n    currentScrollTop,\n    to,\n    props.duration,\n    () => {\n      // make sure it is executed after throttleByRaf's handleScroll\n      setTimeout(() => {\n        isScrolling = false\n      }, 20)\n    }\n  )\n}\n\nconst scrollTo = (href?: string) => {\n  if (href) {\n    setCurrentAnchor(href)\n    scrollToAnchor(href)\n  }\n}\n\nconst handleClick = (e: MouseEvent, href?: string) => {\n  emit('click', e, href)\n  scrollTo(href)\n}\n\nconst handleScroll = throttleByRaf(() => {\n  if (containerEl.value) {\n    currentScrollTop = getScrollTop(containerEl.value)\n  }\n  const currentHref = getCurrentHref()\n  if (isScrolling || isUndefined(currentHref)) return\n  setCurrentAnchor(currentHref)\n})\n\nconst getCurrentHref = () => {\n  if (!containerEl.value) return\n  const scrollTop = getScrollTop(containerEl.value)\n  const anchorTopList: { top: number; href: string }[] = []\n\n  for (const href of Object.keys(links)) {\n    const target = getElement(href)\n    if (!target) continue\n    const scrollEle = getScrollElement(target, containerEl.value)\n    const distance = getOffsetTopDistance(target, scrollEle)\n    anchorTopList.push({\n      top: distance - props.offset - props.bound,\n      href,\n    })\n  }\n  anchorTopList.sort((prev, next) => prev.top - next.top)\n  for (let i = 0; i < anchorTopList.length; i++) {\n    const item = anchorTopList[i]\n    const next = anchorTopList[i + 1]\n\n    if (i === 0 && scrollTop === 0) {\n      return props.selectScrollTop ? item.href : ''\n    }\n    if (item.top <= scrollTop && (!next || next.top > scrollTop)) {\n      return item.href\n    }\n  }\n}\n\nconst getContainer = () => {\n  const el = getElement(props.container)\n  if (!el || isWindow(el)) {\n    containerEl.value = window\n  } else {\n    containerEl.value = el\n  }\n}\n\nuseEventListener(containerEl, 'scroll', handleScroll)\n\nconst markerStyle = computed(() => {\n  if (!anchorRef.value || !markerRef.value || !currentAnchor.value) return {}\n  const currentLinkEl = links[currentAnchor.value]\n  if (!currentLinkEl) return {}\n  const anchorRect = anchorRef.value.getBoundingClientRect()\n  const markerRect = markerRef.value.getBoundingClientRect()\n  const linkRect = currentLinkEl.getBoundingClientRect()\n\n  if (props.direction === 'horizontal') {\n    const left = linkRect.left - anchorRect.left\n    return {\n      left: `${left}px`,\n      width: `${linkRect.width}px`,\n      opacity: 1,\n    }\n  } else {\n    const top =\n      linkRect.top - anchorRect.top + (linkRect.height - markerRect.height) / 2\n    return {\n      top: `${top}px`,\n      opacity: 1,\n    }\n  }\n})\n\nonMounted(() => {\n  getContainer()\n  const hash = decodeURIComponent(window.location.hash)\n  const target = getElement(hash)\n  if (target) {\n    scrollTo(hash)\n  } else {\n    handleScroll()\n  }\n})\n\nwatch(\n  () => props.container,\n  () => {\n    getContainer()\n  }\n)\n\nprovide(anchorKey, {\n  ns,\n  direction: props.direction,\n  currentAnchor,\n  addLink,\n  removeLink,\n  handleClick,\n})\n\ndefineExpose({\n  scrollTo,\n})\n</script>\n"], "names": ["ref", "useNamespace", "computed", "CHANGE_EVENT", "getElement", "getScrollElement", "getOffsetTopDistance", "animateScrollTo", "throttleByRaf", "getScrollTop", "isUndefined", "isWindow", "useEventListener", "onMounted", "watch", "provide", "anchorKey"], "mappings": ";;;;;;;;;;;;;;;;;uCAkCc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,aAAA,GAAgBA,QAAI,EAAE,CAAA,CAAA;AAC5B,IAAM,MAAA,SAAA,GAAYA,QAAwB,IAAI,CAAA,CAAA;AAC9C,IAAM,MAAA,SAAA,GAAYA,QAAwB,IAAI,CAAA,CAAA;AAC9C,IAAA,MAAM,cAAcA,OAA0B,EAAA,CAAA;AAE9C,IAAA,MAAM,QAAqC,EAAC,CAAA;AAC5C,IAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAClB,IAAA,IAAI,gBAAmB,GAAA,CAAA,CAAA;AAEvB,IAAM,MAAA,EAAA,GAAKC,mBAAa,QAAQ,CAAA,CAAA;AAEhC,IAAM,MAAA,GAAA,GAAMC,aAAS,MAAM;AAAA,MACzB,GAAG,CAAE,EAAA;AAAA,MACL,MAAM,IAAS,KAAA,WAAA,GAAc,EAAG,CAAA,CAAA,CAAE,WAAW,CAAI,GAAA,EAAA;AAAA,MACjD,EAAA,CAAG,CAAE,CAAA,KAAA,CAAM,SAAS,CAAA;AAAA,KACrB,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,CAAC,KAA2B,KAAA;AAC1C,MAAM,KAAA,CAAA,KAAA,CAAM,IAAI,CAAA,GAAI,KAAM,CAAA,EAAA,CAAA;AAAA,KAC5B,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,CAAC,IAAiB,KAAA;AACnC,MAAA,OAAO,MAAM,IAAI,CAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,IAAiB,KAAA;AACzC,MAAA,MAAM,aAAa,aAAc,CAAA,KAAA,CAAA;AACjC,MAAA,IAAI,eAAe,IAAM,EAAA;AACvB,QAAA,aAAA,CAAc,KAAQ,GAAA,IAAA,CAAA;AACtB,QAAA,IAAA,CAAKC,oBAAc,IAAI,CAAA,CAAA;AAAA,OACzB;AAAA,KACF,CAAA;AAEA,IAAA,IAAI,YAAoC,GAAA,IAAA,CAAA;AAExC,IAAM,MAAA,cAAA,GAAiB,CAAC,IAAiB,KAAA;AACvC,MAAI,IAAA,CAAC,YAAY,KAAO;AACxB,QAAM,OAAA;AACN,MAAA,MAAa,MAAA,GAAAC,kBAAA,CAAA,IAAA,CAAA,CAAA;AACb,MAAA,IAAI;AACJ,QAAc,OAAA;AACd,MAAA,IAAA,YAAkB;AAClB,QAAM,YAAA,EAAA,CAAW;AACjB,MAAM,WAAA,GAAgB,IAAA,CAAA;AACtB,MAAA,MAAM,SAAU,GAAAC,uBAAe,CAAA,mBAAiB,CAAA,KAAA,CAAA,CAAA;AAChD,MAAe,MAAA,QAAA,GAAAC,6BAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,MAAA,MACD,GAAA,GAAA,SAAA,CAAA,YAAA,GAAA,SAAA,CAAA,YAAA,CAAA;AAAA,MACZ,MAAA,EAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA,MAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACA,YAAA,GAAAC,sBAAA,CAAA,WAAA,CAAA,KAAA,EAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,CAAA,QAAA,EAAA,MAAA;AAAA,QACA,UAAM,CAAA,MAAA;AAAA,UACA,WAAA,GAAA,KAAA,CAAA;AAEJ,SAAA,EAAA,EAAA,CAAA,CAAA;AACE,OAAc,CAAA,CAAA;AAAA,KAAA,CAAA;AACX,IACP,MAAA,QAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACF,IAAA,IAAA,EAAA;AAAA,QACF,gBAAA,CAAA,IAAA,CAAA,CAAA;AAEA,QAAM,cAAY,CAAkB,IAAA,CAAA,CAAA;AAClC,OAAA;AACE,KAAA,CAAA;AACA,IAAA,MAAA,WAAA,GAAmB,CAAA,CAAA,EAAA,IAAA,KAAA;AAAA,MACrB,IAAA,CAAA,OAAA,EAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AAAA,MACF,QAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAK,MAAA,eAAgBC,2BAAA,CAAA,MAAA;AACrB,MAAA,IAAA,WAAa,CAAA,KAAA,EAAA;AAAA,QACf,gBAAA,GAAAC,mBAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAEA,OAAM;AACJ,MAAA,iBAAuB,GAAA,cAAA,EAAA,CAAA;AACrB,MAAmB,IAAA,WAAA,IAAAC,iBAAA,CAAA;AAA8B,QACnD,OAAA;AACA,MAAA,4BAAmC,CAAA,CAAA;AACnC,KAAI,CAAA,CAAA;AACJ,IAAA,MAAA,cAA4B,GAAA,MAAA;AAAA,MAC7B,IAAA,CAAA,WAAA,CAAA,KAAA;AAED,QAAA;AACE,MAAI,kBAAoBD,mBAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACxB,MAAM,MAAA,aAAyB,GAAA,EAAA,CAAA;AAC/B,MAAA,KAAA,oBAAwD,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AAExD,QAAA,MAAA,MAAmB,GAAAL,kBAAY,CAAA,IAAA,CAAA,CAAA;AAC7B,QAAM,IAAA,CAAA,MAAA;AACN,UAAA,SAAa;AACb,QAAA,MAAM,SAAY,GAAAC,uBAAA,CAAiB,MAAQ,EAAA,WAAA,CAAY,KAAK,CAAA,CAAA;AAC5D,QAAM,MAAA,QAAA,GAAWC,6BAAqB,CAAA,MAAA,EAAQ,SAAS,CAAA,CAAA;AACvD,QAAA,aAAA,CAAc,IAAK,CAAA;AAAA,UACjB,GAAK,EAAA,QAAA,GAAW,KAAM,CAAA,MAAA,GAAS,KAAM,CAAA,KAAA;AAAA,UACrC,IAAA;AAAA,SACD,CAAA,CAAA;AAAA,OACH;AACA,MAAA,aAAA,CAAc,KAAK,CAAC,IAAA,EAAM,SAAS,IAAK,CAAA,GAAA,GAAM,KAAK,GAAG,CAAA,CAAA;AACtD,MAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,aAAA,CAAc,QAAQ,CAAK,EAAA,EAAA;AAC7C,QAAM,MAAA,IAAA,GAAO,cAAc,CAAC,CAAA,CAAA;AAC5B,QAAM,MAAA,IAAA,GAAO,aAAc,CAAA,CAAA,GAAI,CAAC,CAAA,CAAA;AAEhC,QAAI,IAAA,CAAA,KAAM,CAAK,IAAA,SAAA,KAAc,CAAG,EAAA;AAC9B,UAAO,OAAA,KAAA,CAAM,eAAkB,GAAA,IAAA,CAAK,IAAO,GAAA,EAAA,CAAA;AAAA,SAC7C;AACA,QAAA,IAAI,KAAK,GAAO,IAAA,SAAA,KAAc,CAAC,IAAQ,IAAA,IAAA,CAAK,MAAM,SAAY,CAAA,EAAA;AAC5D,UAAA,OAAO,IAAK,CAAA,IAAA,CAAA;AAAA,SACd;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAM,MAAA,EAAA,GAAKF,kBAAW,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACrC,MAAA,IAAI,CAAC,EAAA,IAAMO,cAAS,CAAA,EAAE,CAAG,EAAA;AACvB,QAAA,WAAA,CAAY,KAAQ,GAAA,MAAA,CAAA;AAAA,OACf,MAAA;AACL,QAAA,WAAA,CAAY,KAAQ,GAAA,EAAA,CAAA;AAAA,OACtB;AAAA,KACF,CAAA;AAEA,IAAiBC,qBAAA,CAAA,WAAA,EAAa,UAAU,YAAY,CAAA,CAAA;AAEpD,IAAM,MAAA,WAAA,GAAcV,aAAS,MAAM;AACjC,MAAI,IAAA,CAAC,SAAU,CAAA,KAAA,IAAS,CAAC,SAAA,CAAU,SAAS,CAAC,aAAA,CAAc,KAAO;AAClE,QAAM,OAAA,EAAA,CAAA;AACN,MAAI,MAAgB,aAAA,GAAA,KAAO,CAAC,aAAA,CAAA,KAAA,CAAA,CAAA;AAC5B,MAAM,IAAA,CAAA,aAAA;AACN,QAAM,OAAA,EAAA,CAAA;AACN,MAAM,MAAA,UAAA,kBAA+C,CAAA,qBAAA,EAAA,CAAA;AAErD,MAAI,MAAA,sBAAkC,CAAA,KAAA,CAAA,qBAAA,EAAA,CAAA;AACpC,MAAM,MAAA,QAAA,GAAgB,aAAA,CAAO,qBAAW,EAAA,CAAA;AACxC,MAAO,IAAA,KAAA,CAAA,SAAA,KAAA,YAAA,EAAA;AAAA,QACL,MAAA,OAAa,QAAA,CAAA,IAAA,GAAA,UAAA,CAAA,IAAA,CAAA;AAAA,QACb,OAAA;AAAwB,UACxB,IAAS,EAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA;AAAA,UACX,KAAA,EAAA,CAAA,EAAA,QAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,UACK,OAAA,EAAA,CAAA;AACL,SAAM,CAAA;AAEN,OAAO,MAAA;AAAA,QACL,MAAA,MAAW,QAAA,CAAA,GAAA,GAAA,UAAA,CAAA,GAAA,GAAA,CAAA,QAAA,CAAA,MAAA,GAAA,UAAA,CAAA,MAAA,IAAA,CAAA,CAAA;AAAA,QAAA,OACF;AAAA,UACX,GAAA,EAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA;AAAA,UACF,OAAA,EAAA,CAAA;AAAA,SACD,CAAA;AAED,OAAA;AACE,KAAa,CAAA,CAAA;AACb,IAAAW,aAAa,CAAA,MAAA;AACb,MAAM,YAAA,EAAA,CAAS;AACf,MAAA,MAAY,IAAA,GAAA,kBAAA,CAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACV,MAAA,MAAA,MAAa,GAAAT,kBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACf,IAAO,MAAA,EAAA;AACL,QAAa,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,OACf,MAAA;AAAA,QACD,YAAA,EAAA,CAAA;AAED,OAAA;AAAA,KAAA,CACE;AAAY,IAAAU,SACN,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;AACJ,MAAa,YAAA,EAAA,CAAA;AAAA,KACf,CAAA,CAAA;AAAA,IACFC,WAAA,CAAAC,mBAAA,EAAA;AAEA,MAAA,EAAA;AAAmB,MACjB,SAAA,EAAA,KAAA,CAAA,SAAA;AAAA,MACA,aAAiB;AAAA,MACjB,OAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAC,CAAA;AAED,MAAa,QAAA;AAAA,KACX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;"}
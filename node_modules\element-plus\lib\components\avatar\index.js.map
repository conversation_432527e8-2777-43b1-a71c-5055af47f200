{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/avatar/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Avatar from './src/avatar.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAvatar: SFCWithInstall<typeof Avatar> = withInstall(Avatar)\nexport default ElAvatar\n\nexport * from './src/avatar'\nexport type { AvatarInstance } from './src/instance'\n"], "names": ["withInstall", "Avatar"], "mappings": ";;;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,mBAAM;;;;;;;"}
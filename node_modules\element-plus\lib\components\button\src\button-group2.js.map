{"version": 3, "file": "button-group2.js", "sources": ["../../../../../../packages/components/button/src/button-group.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b('group')\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { provide, reactive, toRef } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { buttonGroupProps } from './button-group'\nimport { buttonGroupContextKey } from './constants'\n\ndefineOptions({\n  name: 'ElButtonGroup',\n})\nconst props = defineProps(buttonGroupProps)\nprovide(\n  buttonGroupContextKey,\n  reactive({\n    size: toRef(props, 'size'),\n    type: toRef(props, 'type'),\n  })\n)\nconst ns = useNamespace('button')\n</script>\n"], "names": ["provide", "buttonGroupContextKey", "reactive", "toRef", "useNamespace", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;uCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAAA,WAAA,CAAAC,+BAAA,EAAAC,YAAA,CAAA;AAAA,MACE,IAAA,EAAAC,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AAAA,MACA,IAAS,EAAAA,SAAA,CAAA,KAAA,EAAA,MAAA,CAAA;AAAA,KACP,CAAA,CAAA,CAAA;AAAyB,IACzB,MAAA,EAAA,GAAYC,kBAAO,CAAM,QAAA,CAAA,CAAA;AAAA,IAAA,OAC1B,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACH,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AACA,QAAM,KAAA,EAAKC,kBAAqB,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;"}
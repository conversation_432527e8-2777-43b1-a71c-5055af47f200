{"version": 3, "file": "config.js", "sources": ["../../../../../../packages/components/cascader-panel/src/config.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { NOOP, buildProps, definePropType } from '@element-plus/utils'\nimport type {\n  CascaderConfig,\n  CascaderOption,\n  CascaderProps,\n  CascaderValue,\n} from './node'\n\nexport const CommonProps = buildProps({\n  /**\n   * @description specify which key of node object is used as the node's value\n   */\n  modelValue: {\n    type: definePropType<CascaderValue>([Number, String, Array]),\n  },\n  /**\n   * @description data of the options, the key of `value` and `label` can be customize by `CascaderProps`.\n   */\n  options: {\n    type: definePropType<CascaderOption[]>(Array),\n    default: () => [] as CascaderOption[],\n  },\n  /**\n   * @description configuration options, see the following `CascaderProps` table.\n   */\n  props: {\n    type: definePropType<CascaderProps>(Object),\n    default: () => ({} as CascaderProps),\n  },\n} as const)\n\nexport const DefaultProps: CascaderConfig = {\n  /**\n   * @description trigger mode of expanding options\n   */\n  expandTrigger: 'click',\n  /**\n   * @description whether multiple selection is enabled\n   */\n  multiple: false,\n  /**\n   * @description whether checked state of a node not affects its parent and child nodes\n   */\n  checkStrictly: false, // whether all nodes can be selected\n  /**\n   * @description when checked nodes change, whether to emit an array of node's path, if false, only emit the value of node.\n   */\n  emitPath: true, // wether to emit an array of all levels value in which node is located\n  /**\n   * @description whether to dynamic load child nodes, use with `lazyload` attribute\n   */\n  lazy: false,\n  /**\n   * @description method for loading child nodes data, only works when `lazy` is true\n   */\n  lazyLoad: NOOP,\n  /**\n   * @description specify which key of node object is used as the node's value\n   */\n  value: 'value',\n  /**\n   * @description specify which key of node object is used as the node's label\n   */\n  label: 'label',\n  /**\n   * @description specify which key of node object is used as the node's children\n   */\n  children: 'children',\n  /**\n   * @description specify which key of node object is used as the node's leaf\n   */\n  leaf: 'leaf',\n  /**\n   * @description specify which key of node object is used as the node's disabled\n   */\n  disabled: 'disabled',\n  /**\n   * @description hover threshold of expanding options\n   */\n  hoverThreshold: 500,\n}\n\nexport const useCascaderConfig = (props: { props: CascaderProps }) => {\n  return computed(() => ({\n    ...DefaultProps,\n    ...props.props,\n  }))\n}\n"], "names": ["buildProps", "definePropType", "NOOP", "computed"], "mappings": ";;;;;;;;AAEY,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE,KAAK;AACjB,EAAE,aAAa,EAAE,KAAK;AACtB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,QAAQ,EAAEC,WAAI;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,cAAc,EAAE,GAAG;AACrB,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,KAAK,KAAK;AAC5C,EAAE,OAAOC,YAAQ,CAAC,OAAO;AACzB,IAAI,GAAG,YAAY;AACnB,IAAI,GAAG,KAAK,CAAC,KAAK;AAClB,GAAG,CAAC,CAAC,CAAC;AACN;;;;;;"}
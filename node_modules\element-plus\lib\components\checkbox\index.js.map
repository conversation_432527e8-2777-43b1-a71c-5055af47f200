{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/checkbox/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Checkbox from './src/checkbox.vue'\nimport CheckboxButton from './src/checkbox-button.vue'\nimport CheckboxGroup from './src/checkbox-group.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCheckbox: SFCWithInstall<typeof Checkbox> & {\n  CheckboxButton: typeof CheckboxButton\n  CheckboxGroup: typeof CheckboxGroup\n} = withInstall(Checkbox, {\n  CheckboxButton,\n  CheckboxGroup,\n})\nexport default ElCheckbox\n\nexport const ElCheckboxButton: SFCWithInstall<typeof CheckboxButton> =\n  withNoopInstall(CheckboxButton)\nexport const ElCheckboxGroup: SFCWithInstall<typeof CheckboxGroup> =\n  withNoopInstall(CheckboxGroup)\n\nexport * from './src/checkbox-group'\nexport * from './src/checkbox'\nexport * from './src/constants'\n"], "names": ["withInstall", "Checkbox", "CheckboxButton", "CheckboxGroup", "withNoopInstall"], "mappings": ";;;;;;;;;;;;AAIY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ,EAAE;AAChD,kBAAEC,yBAAc;AAChB,iBAAEC,0BAAa;AACf,CAAC,EAAE;AAES,MAAC,gBAAgB,GAAGC,uBAAe,CAACF,yBAAc,EAAE;AACpD,MAAC,eAAe,GAAGE,uBAAe,CAACD,0BAAa;;;;;;;;;;;;"}
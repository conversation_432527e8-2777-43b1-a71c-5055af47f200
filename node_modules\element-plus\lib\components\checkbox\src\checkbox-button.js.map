{"version": 3, "file": "checkbox-button.js", "sources": ["../../../../../../packages/components/checkbox/src/checkbox-button.vue"], "sourcesContent": ["<template>\n  <label :class=\"labelKls\">\n    <input\n      v-if=\"trueValue || falseValue || trueLabel || falseLabel\"\n      v-model=\"model\"\n      :class=\"ns.be('button', 'original')\"\n      type=\"checkbox\"\n      :name=\"name\"\n      :tabindex=\"tabindex\"\n      :disabled=\"isDisabled\"\n      :true-value=\"trueValue ?? trueLabel ?? true\"\n      :false-value=\"falseValue ?? falseLabel ?? false\"\n      @change=\"handleChange\"\n      @focus=\"isFocused = true\"\n      @blur=\"isFocused = false\"\n      @click.stop\n    />\n    <input\n      v-else\n      v-model=\"model\"\n      :class=\"ns.be('button', 'original')\"\n      type=\"checkbox\"\n      :name=\"name\"\n      :tabindex=\"tabindex\"\n      :disabled=\"isDisabled\"\n      :value=\"actualValue\"\n      @change=\"handleChange\"\n      @focus=\"isFocused = true\"\n      @blur=\"isFocused = false\"\n      @click.stop\n    />\n\n    <span\n      v-if=\"$slots.default || label\"\n      :class=\"ns.be('button', 'inner')\"\n      :style=\"isChecked ? activeStyle : undefined\"\n    >\n      <slot>{{ label }}</slot>\n    </span>\n  </label>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, useSlots } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { checkboxGroupContextKey } from './constants'\nimport { useCheckbox } from './composables'\nimport { checkboxEmits, checkboxProps } from './checkbox'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElCheckboxButton',\n})\n\nconst props = defineProps(checkboxProps)\ndefineEmits(checkboxEmits)\nconst slots = useSlots()\n\nconst {\n  isFocused,\n  isChecked,\n  isDisabled,\n  checkboxButtonSize,\n  model,\n  actualValue,\n  handleChange,\n} = useCheckbox(props, slots)\nconst checkboxGroup = inject(checkboxGroupContextKey, undefined)\nconst ns = useNamespace('checkbox')\n\nconst activeStyle = computed<CSSProperties>(() => {\n  const fillValue = checkboxGroup?.fill?.value ?? ''\n  return {\n    backgroundColor: fillValue,\n    borderColor: fillValue,\n    color: checkboxGroup?.textColor?.value ?? '',\n    boxShadow: fillValue ? `-1px 0 0 0 ${fillValue}` : undefined,\n  }\n})\n\nconst labelKls = computed(() => {\n  return [\n    ns.b('button'),\n    ns.bm('button', checkboxButtonSize.value),\n    ns.is('disabled', isDisabled.value),\n    ns.is('checked', isChecked.value),\n    ns.is('focus', isFocused.value),\n  ]\n})\n</script>\n"], "names": ["useSlots", "useCheckbox", "inject", "checkboxGroupContextKey", "useNamespace", "computed"], "mappings": ";;;;;;;;;;;uCAmDc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA;AAAA,MACJ,SAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,kBAAA;AAAA,MACA,KAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,KACF,GAAIC,uBAAY,CAAA,KAAA,EAAO,KAAK,CAAA,CAAA;AAC5B,IAAM,MAAA,aAAA,GAAgBC,UAAO,CAAAC,iCAAA,EAAyB,KAAS,CAAA,CAAA,CAAA;AAC/D,IAAM,MAAA,EAAA,GAAKC,mBAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,WAAA,GAAcC,aAAwB,MAAM;AAChD,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAY,CAAe;AACjC,MAAO,MAAA,SAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,aAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAAA,MAAA,OACY;AAAA,QACjB,eAAa,EAAA,SAAA;AAAA,QACb,WAAsB,EAAA,SAAA;AAAoB,QAC1C,KAAW,EAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,aAA0B,IAAA,IAAA,GAAA,KAAc,CAAA,GAAA,aAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AAAA,QACrD,SAAA,EAAA,SAAA,GAAA,CAAA,WAAA,EAAA,SAAA,CAAA,CAAA,GAAA,KAAA,CAAA;AAAA,OACD,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,QAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACL,OAAK;AAAQ,QACb,EAAG,CAAA,CAAA,CAAA,QAAa,CAAA;AAAwB,QACxC,EAAG,CAAA,EAAA,CAAG,QAAY,EAAA,kBAAgB,CAAA,KAAA,CAAA;AAAA,QAClC,EAAG,CAAA,EAAA,CAAG,UAAW,EAAA,UAAe,CAAA,KAAA,CAAA;AAAA,QAChC,EAAG,CAAA,EAAA,CAAG,SAAS,EAAA,SAAe,CAAA,KAAA,CAAA;AAAA,QAChC,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,KAAA,CAAA;AAAA,OACD,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
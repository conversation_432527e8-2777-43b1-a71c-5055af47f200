{"version": 3, "file": "collapse.js", "sources": ["../../../../../../packages/components/collapse/src/collapse.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { collapseEmits, collapseProps } from './collapse'\nimport { useCollapse, useCollapseDOM } from './use-collapse'\n\ndefineOptions({\n  name: 'ElCollapse',\n})\nconst props = defineProps(collapseProps)\nconst emit = defineEmits(collapseEmits)\n\nconst { activeNames, setActiveNames } = useCollapse(props, emit)\n\nconst { rootKls } = useCollapseDOM()\n\ndefineExpose({\n  /** @description active names */\n  activeNames,\n  /** @description set active names */\n  setActiveNames,\n})\n</script>\n"], "names": ["useCollapse", "useCollapseDOM", "_openBlock", "_createElementBlock"], "mappings": ";;;;;;;;;uCAUc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,EAAE,WAAa,EAAA,cAAA,EAAmB,GAAAA,uBAAA,CAAY,OAAO,IAAI,CAAA,CAAA;AAE/D,IAAM,MAAA,EAAE,OAAQ,EAAA,GAAIC,0BAAe,EAAA,CAAA;AAEnC,IAAa,MAAA,CAAA;AAAA,MAAA,WAAA;AAAA,MAEX,cAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;"}
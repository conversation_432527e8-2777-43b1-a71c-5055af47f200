{"version": 3, "file": "panel-year-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-year-range.vue"], "sourcesContent": ["<template>\n  <div :class=\"panelKls\">\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div :class=\"leftPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"leftPanelKls.arrowLeftBtn\"\n              @click=\"leftPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"leftPanelKls.arrowRightBtn\"\n              @click=\"leftNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"rightPanelKls.content\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"rightPanelKls.arrowLeftBtn\"\n              @click=\"rightPrevYear\"\n            >\n              <slot name=\"prev-year\">\n                <el-icon><d-arrow-left /></el-icon>\n              </slot>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"rightPanelKls.arrowRightBtn\"\n              @click=\"rightNextYear\"\n            >\n              <slot name=\"next-year\">\n                <el-icon><d-arrow-right /></el-icon>\n              </slot>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <year-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, useSlots, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport {\n  panelYearRangeEmits,\n  panelYearRangeProps,\n} from '../props/panel-year-range'\nimport { useShortcut } from '../composables/use-shortcut'\nimport { useYearRangeHeader } from '../composables/use-year-range-header'\nimport { correctlyParseUserInput, isValidRange } from '../utils'\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants'\nimport YearTable from './basic-year-table.vue'\n\nimport type { Dayjs } from 'dayjs'\nimport type { RangeState } from '../props/shared'\n\ndefineOptions({\n  name: 'DatePickerYearRange',\n})\n\nconst props = defineProps(panelYearRangeProps)\nconst emit = defineEmits(panelYearRangeEmits)\n\nconst { lang } = useLocale()\nconst leftDate = ref(dayjs().locale(lang.value))\nconst rightDate = ref(leftDate.value.add(10, 'year'))\nconst { pickerNs: ppNs } = inject(ROOT_PICKER_INJECTION_KEY)!\nconst drpNs = useNamespace('date-range-picker')\nconst isDefaultFormat = inject('isDefaultFormat') as any\n\nconst hasShortcuts = computed(() => !!shortcuts.length)\n\nconst panelKls = computed(() => [\n  ppNs.b(),\n  drpNs.b(),\n  {\n    'has-sidebar': Boolean(useSlots().sidebar) || hasShortcuts.value,\n  },\n])\n\nconst leftPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-left'],\n    arrowLeftBtn: [ppNs.e('icon-btn'), 'd-arrow-left'],\n    arrowRightBtn: [\n      ppNs.e('icon-btn'),\n      { [ppNs.is('disabled')]: !enableYearArrow.value },\n      'd-arrow-right',\n    ],\n  }\n})\n\nconst rightPanelKls = computed(() => {\n  return {\n    content: [ppNs.e('content'), drpNs.e('content'), 'is-right'],\n    arrowLeftBtn: [\n      ppNs.e('icon-btn'),\n      { 'is-disabled': !enableYearArrow.value },\n      'd-arrow-left',\n    ],\n    arrowRightBtn: [ppNs.e('icon-btn'), 'd-arrow-right'],\n  }\n})\n\nconst handleShortcutClick = useShortcut(lang)\n\nconst {\n  leftPrevYear,\n  rightNextYear,\n  leftNextYear,\n  rightPrevYear,\n  leftLabel,\n  rightLabel,\n  leftYear,\n  rightYear,\n} = useYearRangeHeader({\n  unlinkPanels: toRef(props, 'unlinkPanels'),\n  leftDate,\n  rightDate,\n})\n\nconst enableYearArrow = computed(() => {\n  return props.unlinkPanels && rightYear.value > leftYear.value + 1\n})\n\nconst minDate = ref<Dayjs>()\nconst maxDate = ref<Dayjs>()\n\nconst rangeState = ref<RangeState>({\n  endDate: null,\n  selecting: false,\n})\n\nconst handleChangeRange = (val: RangeState) => {\n  rangeState.value = val\n}\n\ntype RangePickValue = {\n  minDate: Dayjs\n  maxDate: Dayjs\n}\nconst handleRangePick = (val: RangePickValue, close = true) => {\n  const minDate_ = val.minDate\n  const maxDate_ = val.maxDate\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [minDate_.toDate(), maxDate_ && maxDate_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close) return\n  handleConfirm()\n}\n\nconst handleConfirm = (visible = false) => {\n  if (isValidRange([minDate.value, maxDate.value])) {\n    emit('pick', [minDate.value, maxDate.value], visible)\n  }\n}\n\nconst onSelect = (selecting: boolean) => {\n  rangeState.value.selecting = selecting\n  if (!selecting) {\n    rangeState.value.endDate = null\n  }\n}\n\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst { shortcuts, disabledDate } = pickerBase.props\nconst format = toRef(pickerBase.props, 'format')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst unit = 'year'\n\nconst getDefaultValue = () => {\n  let start: Dayjs\n  if (isArray(defaultValue.value)) {\n    const left = dayjs(defaultValue.value[0])\n    let right = dayjs(defaultValue.value[1])\n    if (!props.unlinkPanels) {\n      right = left.add(10, unit)\n    }\n    return [left, right]\n  } else if (defaultValue.value) {\n    start = dayjs(defaultValue.value)\n  } else {\n    start = dayjs()\n  }\n  start = start.locale(lang.value)\n  return [start, start.add(10, unit)]\n}\n\nwatch(\n  () => defaultValue.value,\n  (val) => {\n    if (val) {\n      const defaultArr = getDefaultValue()\n      leftDate.value = defaultArr[0]\n      rightDate.value = defaultArr[1]\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.parsedValue,\n  (newVal) => {\n    if (newVal && newVal.length === 2) {\n      minDate.value = newVal[0]\n      maxDate.value = newVal[1]\n      leftDate.value = minDate.value\n      if (props.unlinkPanels && maxDate.value) {\n        const minDateYear = minDate.value.year()\n        const maxDateYear = maxDate.value.year()\n        rightDate.value =\n          minDateYear === maxDateYear\n            ? maxDate.value.add(10, 'year')\n            : maxDate.value\n      } else {\n        rightDate.value = leftDate.value.add(10, 'year')\n      }\n    } else {\n      const defaultArr = getDefaultValue()\n      minDate.value = undefined\n      maxDate.value = undefined\n      leftDate.value = defaultArr[0]\n      rightDate.value = defaultArr[1]\n    }\n  },\n  { immediate: true }\n)\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return correctlyParseUserInput(\n    value,\n    format.value,\n    lang.value,\n    isDefaultFormat\n  )\n}\n\nconst formatToString = (value: Dayjs[] | Dayjs) => {\n  return isArray(value)\n    ? value.map((day) => day.format(format.value))\n    : value.format(format.value)\n}\n\nconst isValidValue = (date: [Dayjs, Dayjs]) => {\n  return (\n    isValidRange(date) &&\n    (disabledDate\n      ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate())\n      : true)\n  )\n}\n\nconst handleClear = () => {\n  const defaultArr = getDefaultValue()\n  leftDate.value = defaultArr[0]\n  rightDate.value = defaultArr[1]\n  maxDate.value = undefined\n  minDate.value = undefined\n  emit('pick', null)\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["useLocale", "ref", "dayjs", "inject", "ROOT_PICKER_INJECTION_KEY", "useNamespace", "computed", "useSlots", "useShortcut", "useYearRangeHeader", "toRef", "isValidRange", "isArray", "watch", "correctlyParseUserInput", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_unref", "_renderSlot", "_openBlock"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;uCAiHc,CAAA;AAAA,EACZ,IAAM,EAAA,qBAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAE,IAAK,EAAA,GAAIA,eAAU,EAAA,CAAA;AAC3B,IAAA,MAAM,WAAWC,OAAI,CAAAC,yBAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AAC/C,IAAA,MAAM,YAAYD,OAAI,CAAA,QAAA,CAAS,MAAM,GAAI,CAAA,EAAA,EAAI,MAAM,CAAC,CAAA,CAAA;AACpD,IAAA,MAAM,EAAE,QAAA,EAAU,IAAK,EAAA,GAAIE,WAAOC,mCAAyB,CAAA,CAAA;AAC3D,IAAM,MAAA,KAAA,GAAQC,qBAAa,mBAAmB,CAAA,CAAA;AAC9C,IAAM,MAAA,eAAA,GAAkBF,WAAO,iBAAiB,CAAA,CAAA;AAEhD,IAAA,MAAM,eAAeG,YAAS,CAAA,MAAM,CAAC,CAAC,UAAU,MAAM,CAAA,CAAA;AAEtD,IAAM,MAAA,QAAA,GAAWA,aAAS,MAAM;AAAA,MAC9B,KAAK,CAAE,EAAA;AAAA,MACP,MAAM,CAAE,EAAA;AAAA,MACR;AAAA,QACE,eAAe,OAAQ,CAAAC,YAAA,EAAW,CAAA,OAAO,KAAK,YAAa,CAAA,KAAA;AAAA,OAC7D;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeD,aAAS,MAAM;AAClC,MAAO,OAAA;AAAA,QACL,OAAA,EAAS,CAAC,IAAA,CAAK,CAAE,CAAA,SAAS,GAAG,KAAM,CAAA,CAAA,CAAE,SAAS,CAAA,EAAG,SAAS,CAAA;AAAA,QAC1D,cAAc,CAAC,IAAA,CAAK,CAAE,CAAA,UAAU,GAAG,cAAc,CAAA;AAAA,QACjD,aAAe,EAAA;AAAA,UACb,IAAA,CAAK,EAAE,UAAU,CAAA;AAAA,UACjB,EAAE,CAAC,IAAK,CAAA,EAAA,CAAG,UAAU,CAAC,GAAG,CAAC,eAAA,CAAgB,KAAM,EAAA;AAAA,UAChD,eAAA;AAAA,SACF;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgBA,aAAS,MAAM;AACnC,MAAO,OAAA;AAAA,QACL,OAAA,EAAS,CAAC,IAAA,CAAK,CAAE,CAAA,SAAS,GAAG,KAAM,CAAA,CAAA,CAAE,SAAS,CAAA,EAAG,UAAU,CAAA;AAAA,QAC3D,YAAc,EAAA;AAAA,UACZ,IAAA,CAAK,EAAE,UAAU,CAAA;AAAA,UACjB,EAAE,aAAA,EAAe,CAAC,eAAA,CAAgB,KAAM,EAAA;AAAA,UACxC,cAAA;AAAA,SACF;AAAA,QACA,eAAe,CAAC,IAAA,CAAK,CAAE,CAAA,UAAU,GAAG,eAAe,CAAA;AAAA,OACrD,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,mBAAA,GAAsBE,wBAAY,IAAI,CAAA,CAAA;AAE5C,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,QACEC,qCAAmB,CAAA;AAAA,MACrB,YAAA,EAAcC,SAAM,CAAA,KAAA,EAAO,cAAc,CAAA;AAAA,MACzC,QAAA;AAAA,MACA,SAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBJ,aAAS,MAAM;AACrC,MAAA,OAAO,KAAM,CAAA,YAAA,IAAgB,SAAU,CAAA,KAAA,GAAQ,SAAS,KAAQ,GAAA,CAAA,CAAA;AAAA,KACjE,CAAA,CAAA;AAED,IAAA,MAAM,UAAUL,OAAW,EAAA,CAAA;AAC3B,IAAA,MAAM,UAAUA,OAAW,EAAA,CAAA;AAE3B,IAAA,MAAM,aAAaA,OAAgB,CAAA;AAAA,MACjC,OAAS,EAAA,IAAA;AAAA,MACT,SAAW,EAAA,KAAA;AAAA,KACZ,CAAA,CAAA;AAED,IAAM,MAAA,iBAAA,GAAoB,CAAC,GAAoB,KAAA;AAC7C,MAAA,UAAA,CAAW,KAAQ,GAAA,GAAA,CAAA;AAAA,KACrB,CAAA;AAMA,IAAA,MAAM,eAAkB,GAAA,CAAC,GAAqB,EAAA,KAAA,GAAQ,IAAS,KAAA;AAC7D,MAAA,MAAM,WAAW,GAAI,CAAA,OAAA,CAAA;AACrB,MAAA,MAAM,WAAW,GAAI,CAAA,OAAA,CAAA;AACrB,MAAA,IAAI,OAAQ,CAAA,KAAA,KAAU,QAAY,IAAA,OAAA,CAAQ,UAAU,QAAU,EAAA;AAC5D,QAAA,OAAA;AAAA,OACF;AACA,MAAK,IAAA,CAAA,iBAAA,EAAmB,CAAC,QAAS,CAAA,MAAA,IAAU,QAAY,IAAA,QAAA,CAAS,MAAO,EAAC,CAAC,CAAA,CAAA;AAC1E,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAChB,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAEhB,MAAA,IAAI,CAAC,KAAO;AACZ,QAAc,OAAA;AAAA,MAChB,aAAA,EAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,aAAa,GAAC,CAAA,eAAuB,KAAA;AACvC,MAAA,IAAAU,kBAAc,CAAA,CAAA,aAAe,EAAQ,OAAA,CAAA,MAAQ,CAAO,EAAA;AAAA,QACtD,IAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,KAAA,EAAA,OAAA,CAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,qBAA6B,KAAA;AAC7B,MAAA,UAAgB,CAAA,KAAA,CAAA,SAAA,GAAA,SAAA,CAAA;AACd,MAAA,IAAA,CAAA;AAA2B,QAC7B,UAAA,CAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACN,IAAA,MAAM,UAAE,GAAWR,UAAa,CAAA,gBAAe,CAAA,CAAA;AAC/C,IAAA,MAAM,EAAS,SAAA,EAAA,YAAiB,EAAA,GAAA,UAAe,CAAA,KAAA,CAAA;AAC/C,IAAA,MAAM,MAAe,GAAAO,SAAA,CAAA,UAAiB,CAAA,KAAA,EAAA,QAAqB,CAAA,CAAA;AAG3D,IAAA,MAAM,wBAAwB,CAAA,UAAA,CAAA,KAAA,EAAA,cAAA,CAAA,CAAA;AAC5B,IAAI,MAAA,eAAA,GAAA,MAAA;AACJ,MAAI,IAAA,KAAA,CAAA;AACF,MAAA,IAAAE,cAAa,CAAA,YAAmB,CAAA,KAAA,CAAA,EAAA;AAChC,QAAA,MAAY,IAAA,GAAAV,yBAAM,CAAa,YAAA,CAAA,KAAM,CAAC,CAAC,CAAA,CAAA,CAAA;AACvC,QAAI,IAAA,QAAqBA,yBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACvB,QAAQ,IAAA,CAAA,KAAA,CAAA,YAAa,EAAI;AAAA,UAC3B,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA;AACA,SAAO;AAAY,QACrB,OAAA,CAAA;AACE,OAAQ,MAAA,IAAA,kBAAmB,EAAK;AAAA,QAC3B,KAAA,GAAAA,yBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAAc,QAChB,KAAA,GAAAA,yBAAA,EAAA,CAAA;AACA,OAAQ;AACR,MAAA,KAAA,GAAQ,KAAO,CAAA,MAAA,CAAM,IAAI,CAAA,OAAQ;AAAC,MACpC,OAAA,CAAA,KAAA,EAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AAAA,IAAAW,gBACqB,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACnB,IAAS,GAAA,EAAA;AACP,QAAA,MAAS,UAAA,GAAA,eAAA,EAAA,CAAA;AACP,QAAA,QAAM,mBAA6B,CAAA,CAAA,CAAA,CAAA;AACnC,QAAS,SAAA,CAAA,KAAA,GAAA,YAAmB,CAAC,CAAA;AAC7B,OAAU;AAAoB,KAChC,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IACFA,SAAA,CAAA,MAAA,KAAA,CAAA,WAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACA,cAAkB,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AAAA,QACpB,OAAA,CAAA,KAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AAEA,QAAA,OAAA,CAAA,KAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AAAA,gBACc,CAAA,KAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AAAA,QACA,IAAA,KAAA,CAAA,YAAA,IAAA,OAAA,CAAA,KAAA,EAAA;AACV,UAAI,MAAA,WAAiB,GAAA,OAAA,CAAA,KAAc,CAAA,IAAA,EAAA,CAAA;AACjC,UAAQ,MAAA,qBAAgB,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AACxB,UAAQ,SAAA,CAAA,KAAA,cAAgB,KAAA,WAAA,GAAA,OAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACxB,SAAA,MAAA;AACA,UAAI,SAAA,CAAM,KAAgB,GAAA,QAAA,CAAA,KAAA,CAAA,GAAe,CAAA,EAAA,EAAA,MAAA,CAAA,CAAA;AACvC,SAAM;AACN,OAAM,MAAA;AACN,QAAU,MAAA,UAAA,GAAA;AAGI,QAAA,OACT,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AACL,QAAA,OAAA,CAAA,KAAA,GAAkB,KAAA,CAAA,CAAA;AAA6B,QACjD,QAAA,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QACF,SAAO,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AACL,OAAA;AACA,KAAA,EAAA,EAAA,SAAQ,EAAQ,IAAA,EAAA,CAAA,CAAA;AAChB,IAAA,MAAA,cAAgB,GAAA,CAAA,KAAA,KAAA;AAChB,MAAS,OAAAC,mCAAoB,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,CAAA,KAAA,EAAA,eAAA,CAAA,CAAA;AAC7B,KAAU,CAAA;AAAoB,IAChC,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,OAAAF,cAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAA,GAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAkB,IACpB,MAAA,YAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAM,OAAAD,kBAAA,CAAA,IAA6C,CAAA,KAAA,YAAA,GAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AACjD,KAAO,CAAA;AAAA,IACL,MAAA,WAAA,GAAA,MAAA;AAAA,MAAA,MACO,UAAA,GAAA,eAAA,EAAA,CAAA;AAAA,MAAA,QACF,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACL,SAAA,CAAA,KAAA,GAAA,UAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,MACF,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAEA,MAAM,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AACJ,KAAA,CAAA;AAE6B,IAC/B,IAAA,CAAA,mBAAA,EAAA,CAAA,cAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AAEA,IAAM,IAAA,CAAA,mBAAyC,EAAA,CAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAC7C,IAAA,IAAA,CAAA,mBACmB,EAAA,CAAA,gCAEZ,CAAA,CAAa;AACd,IAER,IAAA,CAAA,mBAAA,EAAA,CAAA,aAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAEA,IAAA,oBAAoB,KAAM;AACxB,MAAA,sBAAmB,EAAgBI,sBAAA,CAAA,KAAA,EAAA;AACnC,QAAS,KAAA,EAAAC,4BAAoB,CAAA,QAAA,CAAA,CAAA;AAC7B,OAAU,EAAA;AACV,QAAAC,sBAAgB,CAAA,KAAA,EAAA;AAChB,UAAA,KAAgB,EAAAD,kBAAA,CAAAE,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AAChB,SAAA,EAAK;AAAY,UACnBC,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAEA,YAA0B,KAAA,EAAAH,kBAAiB,CAAAE,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAY,CAAC,CAAA;AACxD,WAA0B,CAAA;AAC1B,UAA0BA,SAAA,CAAA,YAAA,CAAA,IAAmBE,aAAA,EAAA,EAAAL,sBAAe,CAAA,KAAA,EAAA;AAC5D,YAA0B,GAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/image-viewer/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport ImageViewer from './src/image-viewer.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElImageViewer: SFCWithInstall<typeof ImageViewer> =\n  withInstall(ImageViewer)\nexport default ElImageViewer\n\nexport * from './src/image-viewer'\n"], "names": ["withInstall", "ImageViewer"], "mappings": ";;;;;;;;AAEY,MAAC,aAAa,GAAGA,mBAAW,CAACC,wBAAW;;;;;;;"}
{"version": 3, "file": "link2.js", "sources": ["../../../../../../packages/components/link/src/link.vue"], "sourcesContent": ["<template>\n  <a\n    :class=\"linkKls\"\n    :href=\"disabled || !href ? undefined : href\"\n    :target=\"disabled || !href ? undefined : target\"\n    @click=\"handleClick\"\n  >\n    <el-icon v-if=\"icon\"><component :is=\"icon\" /></el-icon>\n    <span v-if=\"$slots.default\" :class=\"ns.e('inner')\">\n      <slot />\n    </span>\n\n    <slot v-if=\"$slots.icon\" name=\"icon\" />\n  </a>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useDeprecated, useNamespace } from '@element-plus/hooks'\nimport { isBoolean } from '@element-plus/utils'\nimport { linkEmits, linkProps } from './link'\n\ndefineOptions({\n  name: 'ElLink',\n})\nconst props = defineProps(linkProps)\nconst emit = defineEmits(linkEmits)\n\nuseDeprecated(\n  {\n    scope: 'el-link',\n    from: 'The underline option (boolean)',\n    replacement: \"'always' | 'hover' | 'never'\",\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/link.html#underline',\n  },\n  computed(() => isBoolean(props.underline))\n)\n\nconst ns = useNamespace('link')\n\nconst linkKls = computed(() => [\n  ns.b(),\n  ns.m(props.type),\n  ns.is('disabled', props.disabled),\n  ns.is('underline', underline.value === 'always'),\n  ns.is('hover-underline', underline.value === 'hover' && !props.disabled),\n])\n\n// Boolean compatibility\nconst underline = computed(() => {\n  if (isBoolean(props.underline)) {\n    return props.underline ? 'hover' : 'never'\n  } else return props.underline\n})\n\nfunction handleClick(event: MouseEvent) {\n  if (!props.disabled) emit('click', event)\n}\n</script>\n"], "names": ["useDeprecated", "computed", "isBoolean", "useNamespace"], "mappings": ";;;;;;;;;;;;uCAuBc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAAA,mBAAA,CAAA;AAAA,MACE,KAAA,EAAA,SAAA;AAAA,MAAA,IACS,EAAA,gCAAA;AAAA,MAAA,WACD,EAAA,8BAAA;AAAA,MAAA,OACO,EAAA,OAAA;AAAA,MAAA,GACJ,EAAA,8DAAA;AAAA,KAAA,EAAAC,YACJ,CAAA,MAAAC,eAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACP,MAAA,EAAA,GAAAC,oBAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,MACS,OAAA,GAAAF,YAAgB,CAAA,MAAA;AAAgB,MAC3C,EAAA,CAAA,CAAA,EAAA;AAEA,MAAM,EAAA,CAAA,CAAA,CAAA;AAEN,MAAM,EAAA,CAAA,EAAA,CAAA,iBAAmB,CAAM,QAAA,CAAA;AAAA,MAC7B,GAAG,EAAE,CAAA,WAAA,EAAA,SAAA,CAAA,KAAA,KAAA,QAAA,CAAA;AAAA,MACL,EAAA,CAAG,EAAE,CAAA,iBAAU,EAAA,SAAA,CAAA,KAAA,KAAA,OAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AAAA,KAAA,CACf,CAAG;AAA6B,IAAA,MAC7B,SAAgB,GAAAA,YAAA,CAAA;AAA4B,MAC/C,mBAAyB,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA;AAA8C,QACxE,OAAA,KAAA,CAAA,SAAA,GAAA,OAAA,GAAA,OAAA,CAAA;AAGD,OAAM;AACJ,QAAI,OAAA,KAAU,CAAM,SAAA,CAAA;AAClB,KAAO,CAAA,CAAA;AAA4B,IACrC,oBAAoB,CAAA,KAAA,EAAA;AAAA,MACrB,IAAA,CAAA,KAAA,CAAA,QAAA;AAED,QAAA,IAAA,CAAS,cAA+B,CAAA,CAAA;AACtC,KAAA;AAAwC,IAC1C,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}
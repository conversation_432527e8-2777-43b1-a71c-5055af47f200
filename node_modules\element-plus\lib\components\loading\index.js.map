{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/loading/index.ts"], "sourcesContent": ["import { Loading } from './src/service'\nimport { vLoading } from './src/directive'\n\nimport type { App } from 'vue'\n\n// installer and everything in all\nexport const ElLoading = {\n  install(app: App) {\n    app.directive('loading', vLoading)\n    app.config.globalProperties.$loading = Loading\n  },\n  directive: vLoading,\n  service: Loading,\n}\n\nexport default ElLoading\nexport { vLoading, vLoading as ElLoadingDirective, Loading as ElLoadingService }\n\nexport * from './src/types'\n"], "names": ["vLoading", "Loading"], "mappings": ";;;;;;;AAEY,MAAC,SAAS,GAAG;AACzB,EAAE,OAAO,CAAC,GAAG,EAAE;AACf,IAAI,GAAG,CAAC,SAAS,CAAC,SAAS,EAAEA,kBAAQ,CAAC,CAAC;AACvC,IAAI,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,GAAGC,eAAO,CAAC;AACnD,GAAG;AACH,EAAE,SAAS,EAAED,kBAAQ;AACrB,EAAE,OAAO,EAAEC,eAAO;AAClB;;;;;;;;"}
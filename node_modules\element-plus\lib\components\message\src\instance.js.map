{"version": 3, "file": "instance.js", "sources": ["../../../../../../packages/components/message/src/instance.ts"], "sourcesContent": ["import { shallowReactive } from 'vue'\nimport type { ComponentInternalInstance, VNode } from 'vue'\nimport type { Mutable } from '@element-plus/utils'\nimport type { MessageHandler, MessageProps } from './message'\n\nexport type MessageContext = {\n  id: string\n  vnode: VNode\n  handler: MessageHandler\n  vm: ComponentInternalInstance\n  props: Mutable<MessageProps>\n}\n\nexport const instances: MessageContext[] = shallowReactive([])\n\nexport const getInstance = (id: string) => {\n  const idx = instances.findIndex((instance) => instance.id === id)\n  const current = instances[idx]\n  let prev: MessageContext | undefined\n  if (idx > 0) {\n    prev = instances[idx - 1]\n  }\n  return { current, prev }\n}\n\nexport const getLastOffset = (id: string): number => {\n  const { prev } = getInstance(id)\n  if (!prev) return 0\n  return prev.vm.exposed!.bottom.value\n}\n\nexport const getOffsetOrSpace = (id: string, offset: number) => {\n  const idx = instances.findIndex((instance) => instance.id === id)\n  return idx > 0 ? 16 : offset\n}\n"], "names": ["shallowReactive"], "mappings": ";;;;;;AACY,MAAC,SAAS,GAAGA,mBAAe,CAAC,EAAE,EAAE;AACjC,MAAC,WAAW,GAAG,CAAC,EAAE,KAAK;AACnC,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACjC,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,EAAE;AACU,MAAC,aAAa,GAAG,CAAC,EAAE,KAAK;AACrC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,IAAI,CAAC,IAAI;AACX,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACtC,EAAE;AACU,MAAC,gBAAgB,GAAG,CAAC,EAAE,EAAE,MAAM,KAAK;AAChD,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AAC/B;;;;;;;"}
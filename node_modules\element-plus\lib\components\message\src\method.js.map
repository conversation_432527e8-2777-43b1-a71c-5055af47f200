{"version": 3, "file": "method.js", "sources": ["../../../../../../packages/components/message/src/method.ts"], "sourcesContent": ["import { createVNode, isVNode, render } from 'vue'\nimport {\n  debugWarn,\n  isBoolean,\n  isClient,\n  isElement,\n  isFunction,\n  isNumber,\n  isString,\n} from '@element-plus/utils'\nimport { messageConfig } from '@element-plus/components/config-provider'\nimport MessageConstructor from './message.vue'\nimport { messageDefaults, messageTypes } from './message'\nimport { instances } from './instance'\n\nimport type { MessageContext } from './instance'\nimport type { AppContext } from 'vue'\nimport type {\n  Message,\n  MessageFn,\n  MessageHandler,\n  MessageOptions,\n  MessageParams,\n  MessageParamsNormalized,\n  messageType,\n} from './message'\n\nlet seed = 1\n\n// TODO: Since Notify.ts is basically the same like this file. So we could do some encapsulation against them to reduce code duplication.\n\nconst normalizeOptions = (params?: MessageParams) => {\n  const options: MessageOptions =\n    !params || isString(params) || isVNode(params) || isFunction(params)\n      ? { message: params }\n      : params\n\n  const normalized = {\n    ...messageDefaults,\n    ...options,\n  }\n\n  if (!normalized.appendTo) {\n    normalized.appendTo = document.body\n  } else if (isString(normalized.appendTo)) {\n    let appendTo = document.querySelector<HTMLElement>(normalized.appendTo)\n\n    // should fallback to default value with a warning\n    if (!isElement(appendTo)) {\n      debugWarn(\n        'ElMessage',\n        'the appendTo option is not an HTMLElement. Falling back to document.body.'\n      )\n      appendTo = document.body\n    }\n\n    normalized.appendTo = appendTo\n  }\n\n  // When grouping is configured globally,\n  // if grouping is manually set when calling message individually and it is not equal to the default value,\n  // the global configuration cannot override the current setting. default => false\n  if (isBoolean(messageConfig.grouping) && !normalized.grouping) {\n    normalized.grouping = messageConfig.grouping\n  }\n  if (isNumber(messageConfig.duration) && normalized.duration === 3000) {\n    normalized.duration = messageConfig.duration\n  }\n  if (isNumber(messageConfig.offset) && normalized.offset === 16) {\n    normalized.offset = messageConfig.offset\n  }\n  if (isBoolean(messageConfig.showClose) && !normalized.showClose) {\n    normalized.showClose = messageConfig.showClose\n  }\n\n  return normalized as MessageParamsNormalized\n}\n\nconst closeMessage = (instance: MessageContext) => {\n  const idx = instances.indexOf(instance)\n  if (idx === -1) return\n\n  instances.splice(idx, 1)\n  const { handler } = instance\n  handler.close()\n}\n\nconst createMessage = (\n  { appendTo, ...options }: MessageParamsNormalized,\n  context?: AppContext | null\n): MessageContext => {\n  const id = `message_${seed++}`\n  const userOnClose = options.onClose\n\n  const container = document.createElement('div')\n\n  const props = {\n    ...options,\n    // now the zIndex will be used inside the message.vue component instead of here.\n    // zIndex: nextIndex() + options.zIndex\n    id,\n    onClose: () => {\n      userOnClose?.()\n      closeMessage(instance)\n    },\n\n    // clean message element preventing mem leak\n    onDestroy: () => {\n      // since the element is destroy, then the VNode should be collected by GC as well\n      // we do not want cause any mem leak because we have returned vm as a reference to users\n      // so that we manually set it to false.\n      render(null, container)\n    },\n  }\n  const vnode = createVNode(\n    MessageConstructor,\n    props,\n    isFunction(props.message) || isVNode(props.message)\n      ? {\n          default: isFunction(props.message)\n            ? props.message\n            : () => props.message,\n        }\n      : null\n  )\n  vnode.appContext = context || message._context\n\n  render(vnode, container)\n  // instances will remove this item when close function gets called. So we do not need to worry about it.\n  appendTo.appendChild(container.firstElementChild!)\n\n  const vm = vnode.component!\n\n  const handler: MessageHandler = {\n    // instead of calling the onClose function directly, setting this value so that we can have the full lifecycle\n    // for out component, so that all closing steps will not be skipped.\n    close: () => {\n      vm.exposed!.close()\n    },\n  }\n\n  const instance: MessageContext = {\n    id,\n    vnode,\n    vm,\n    handler,\n    props: (vnode.component as any).props,\n  }\n\n  return instance\n}\n\nconst message: MessageFn &\n  Partial<Message> & { _context: AppContext | null } = (\n  options = {},\n  context\n) => {\n  if (!isClient) return { close: () => undefined }\n\n  const normalized = normalizeOptions(options)\n\n  if (normalized.grouping && instances.length) {\n    const instance = instances.find(\n      ({ vnode: vm }) => vm.props?.message === normalized.message\n    )\n    if (instance) {\n      instance.props.repeatNum += 1\n      instance.props.type = normalized.type\n      return instance.handler\n    }\n  }\n\n  if (isNumber(messageConfig.max) && instances.length >= messageConfig.max) {\n    return { close: () => undefined }\n  }\n\n  const instance = createMessage(normalized, context)\n\n  instances.push(instance)\n  return instance.handler\n}\n\nmessageTypes.forEach((type) => {\n  message[type] = (options = {}, appContext) => {\n    const normalized = normalizeOptions(options)\n    return message({ ...normalized, type }, appContext)\n  }\n})\n\nexport function closeAll(type?: messageType): void {\n  // Create a copy of instances to avoid modification during iteration\n  const instancesToClose = [...instances]\n\n  for (const instance of instancesToClose) {\n    if (!type || type === instance.props.type) {\n      instance.handler.close()\n    }\n  }\n}\n\nmessage.closeAll = closeAll\nmessage._context = null\n\nexport default message as Message\n"], "names": ["isString", "isVNode", "isFunction", "messageDefaults", "isElement", "debugWarn", "isBoolean", "messageConfig", "isNumber", "instance", "instances", "render", "createVNode", "MessageConstructor", "isClient", "messageTypes"], "mappings": ";;;;;;;;;;;;;;AAcA,IAAI,IAAI,GAAG,CAAC,CAAC;AACb,MAAM,gBAAgB,GAAG,CAAC,MAAM,KAAK;AACrC,EAAE,MAAM,OAAO,GAAG,CAAC,MAAM,IAAIA,eAAQ,CAAC,MAAM,CAAC,IAAIC,WAAO,CAAC,MAAM,CAAC,IAAIC,iBAAU,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AACtH,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,GAAGC,yBAAe;AACtB,IAAI,GAAG,OAAO;AACd,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC5B,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AACxC,GAAG,MAAM,IAAIH,eAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC5C,IAAI,IAAI,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/D,IAAI,IAAI,CAACI,eAAS,CAAC,QAAQ,CAAC,EAAE;AAC9B,MAAMC,eAAS,CAAC,WAAW,EAAE,2EAA2E,CAAC,CAAC;AAC1G,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC/B,KAAK;AACL,IAAI,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnC,GAAG;AACH,EAAE,IAAIC,eAAS,CAACC,4BAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AACjE,IAAI,UAAU,CAAC,QAAQ,GAAGA,4BAAa,CAAC,QAAQ,CAAC;AACjD,GAAG;AACH,EAAE,IAAIC,cAAQ,CAACD,4BAAa,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,KAAK,GAAG,EAAE;AACvE,IAAI,UAAU,CAAC,QAAQ,GAAGA,4BAAa,CAAC,QAAQ,CAAC;AACjD,GAAG;AACH,EAAE,IAAIC,cAAQ,CAACD,4BAAa,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;AAClE,IAAI,UAAU,CAAC,MAAM,GAAGA,4BAAa,CAAC,MAAM,CAAC;AAC7C,GAAG;AACH,EAAE,IAAID,eAAS,CAACC,4BAAa,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACnE,IAAI,UAAU,CAAC,SAAS,GAAGA,4BAAa,CAAC,SAAS,CAAC;AACnD,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,CAACE,UAAQ,KAAK;AACnC,EAAE,MAAM,GAAG,GAAGC,kBAAS,CAAC,OAAO,CAACD,UAAQ,CAAC,CAAC;AAC1C,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC;AAChB,IAAI,OAAO;AACX,EAAEC,kBAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,EAAE,OAAO,EAAE,GAAGD,UAAQ,CAAC;AAC/B,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,EAAE,OAAO,KAAK;AAC7D,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjC,EAAE,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;AACtC,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,GAAG,OAAO;AACd,IAAI,EAAE;AACN,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,EAAE,CAAC;AACnD,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,SAAS,EAAE,MAAM;AACrB,MAAME,UAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAGC,eAAW,CAACC,oBAAkB,EAAE,KAAK,EAAEX,iBAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAID,WAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG;AAC7G,IAAI,OAAO,EAAEC,iBAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO;AAC5E,GAAG,GAAG,IAAI,CAAC,CAAC;AACZ,EAAE,KAAK,CAAC,UAAU,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC;AACjD,EAAES,UAAM,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC3B,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC;AAC7B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,KAAK,EAAE,MAAM;AACjB,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACzB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,EAAE;AACN,IAAI,KAAK;AACT,IAAI,EAAE;AACN,IAAI,OAAO;AACX,IAAI,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;AAChC,GAAG,CAAC;AACJ,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,OAAO,KAAK;AAC3C,EAAE,IAAI,CAACG,aAAQ;AACf,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AACnC,EAAE,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC/C,EAAE,IAAI,UAAU,CAAC,QAAQ,IAAIJ,kBAAS,CAAC,MAAM,EAAE;AAC/C,IAAI,MAAM,SAAS,GAAGA,kBAAS,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK;AACxD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,MAAM,UAAU,CAAC,OAAO,CAAC;AACpF,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;AACrC,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;AAC7C,MAAM,OAAO,SAAS,CAAC,OAAO,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,IAAIF,cAAQ,CAACD,4BAAa,CAAC,GAAG,CAAC,IAAIG,kBAAS,CAAC,MAAM,IAAIH,4BAAa,CAAC,GAAG,EAAE;AAC5E,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AACnC,GAAG;AACH,EAAE,MAAME,UAAQ,GAAG,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACtD,EAAEC,kBAAS,CAAC,IAAI,CAACD,UAAQ,CAAC,CAAC;AAC3B,EAAE,OAAOA,UAAQ,CAAC,OAAO,CAAC;AAC1B,EAAE;AACFM,sBAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC/B,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,UAAU,KAAK;AAChD,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACjD,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,UAAU,EAAE,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;AACxD,GAAG,CAAC;AACJ,CAAC,CAAC,CAAC;AACI,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,gBAAgB,GAAG,CAAC,GAAGL,kBAAS,CAAC,CAAC;AAC1C,EAAE,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE;AAC3C,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;AAC/C,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,CAAC;AACD,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5B,OAAO,CAAC,QAAQ,GAAG,IAAI;;;;;"}
{"version": 3, "file": "jumper.js", "sources": ["../../../../../../../packages/components/pagination/src/components/jumper.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type Jumper from './jumper.vue'\n\nexport const paginationJumperProps = buildProps({\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n} as const)\n\nexport type PaginationJumperProps = ExtractPropTypes<\n  typeof paginationJumperProps\n>\n\nexport type PaginationJumperInstance = InstanceType<typeof Jumper> & unknown\n"], "names": ["buildProps", "componentSizes"], "mappings": ";;;;;;;AAEY,MAAC,qBAAqB,GAAGA,kBAAU,CAAC;AAChD,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAEC,mBAAc;AAC1B,GAAG;AACH,CAAC;;;;"}
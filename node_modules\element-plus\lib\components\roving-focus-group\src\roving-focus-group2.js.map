{"version": 3, "file": "roving-focus-group2.js", "sources": ["../../../../../../packages/components/roving-focus-group/src/roving-focus-group.vue"], "sourcesContent": ["<template>\n  <el-focus-group-collection>\n    <el-roving-focus-group-impl v-bind=\"$attrs\">\n      <slot />\n    </el-roving-focus-group-impl>\n  </el-focus-group-collection>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue'\nimport ElRovingFocusGroupImpl from './roving-focus-group-impl.vue'\nimport { ElCollection as ElFocusGroupCollection } from './roving-focus-group'\n\nexport default defineComponent({\n  name: 'ElRovingFocusGroup',\n  components: {\n    ElFocusGroupCollection,\n    ElRovingFocusGroupImpl,\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElFocusGroupCollection", "ElRovingFocusGroupImpl", "_createBlock", "_withCtx", "_createVNode", "_normalizeProps", "_guardReactiveProps", "_renderSlot"], "mappings": ";;;;;;;;;AAaA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,oBAAA;AAAA,EACN,UAAY,EAAA;AAAA,4BACVC,6BAAA;AAAA,4BACAC,+BAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;0BAlBCC,eAI4B,CAAA,oCAAA,EAAA,IAAA,EAAA;AAAA,IAAA,OAAA,EAAAC,WAAA,CAH1B,MAE6B;AAAA,MAF7BC,eAAA,CAAA,qCAAA,EAAAC,kBAAA,CAAAC,sBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA;AAAA,QAE6B,OAAA,EAAAH,WAAA,CAAA,MAAA;AAAA,UAAAI,cAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAFO,CAAM;AAAA,SAAA,CAAA;AAAA,QAAA,CAAA,EAAA,CAAA;AAChC,OAAA,EAAA,EAAA,CAAR;AAAQ,KAAA,CAAA;;;;;;;;"}
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const statisticProps = runtime.buildProps({
  decimalSeparator: {
    type: String,
    default: "."
  },
  groupSeparator: {
    type: String,
    default: ","
  },
  precision: {
    type: Number,
    default: 0
  },
  formatter: Function,
  value: {
    type: runtime.definePropType([Number, Object]),
    default: 0
  },
  prefix: String,
  suffix: String,
  title: String,
  valueStyle: {
    type: runtime.definePropType([String, Object, Array])
  }
});

exports.statisticProps = statisticProps;
//# sourceMappingURL=statistic.js.map

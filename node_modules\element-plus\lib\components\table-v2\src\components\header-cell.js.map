{"version": 3, "file": "header-cell.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/header-cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport type { FunctionalComponent } from 'vue'\nimport type { TableV2HeaderCell } from '../header-cell'\n\nconst HeaderCell: FunctionalComponent<TableV2HeaderCell> = (props, { slots }) =>\n  renderSlot(slots, 'default', props, () => [\n    <div class={props.class} title={props.column?.title}>\n      {props.column?.title}\n    </div>,\n  ])\n\nHeaderCell.displayName = 'ElTableV2HeaderCell'\nHeaderCell.inheritAttrs = false\n\nexport default HeaderCell\n"], "names": ["slots", "renderSlot", "props", "column", "title", "<PERSON><PERSON><PERSON><PERSON>", "inheritAttrs"], "mappings": ";;;;;;;AAIA,EAAA,KAAwD;AAAaA,CAAAA,KAAAA,cAAAA,CAAAA,KAAAA,EAAAA,SAAAA,EAAAA,KAAAA,EAAAA,MAAAA;AAAF,EAAR,IACzDC;EAA0C,OAC5BC,CAAAA,eAD4B,CAAA,KAAA,EAAA;IACRA,OAAAA,EAAAA,KAAMC,CAAAA,KAAQC;AADN,IAErCF,OAAMC,EAAAA,CAAAA,EAAN,GAAcC,YAHrB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA;;AAOAC,CAAU,CAAA,CAAA;AACVA,UAAU,CAACC,WAAX,GAAA,qBAAA,CAAA;AAEA,UAAA,CAAA,YAAA,GAAA,KAAA,CAAA;;;;;"}
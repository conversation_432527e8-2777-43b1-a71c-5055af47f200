{"version": 3, "file": "table-v2.js", "sources": ["../../../../../../packages/components/table-v2/src/table-v2.tsx"], "sourcesContent": ["// @ts-nocheck\nimport { defineComponent, provide, unref } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useTable } from './use-table'\nimport { TableV2InjectionKey } from './tokens'\nimport { tableV2Props } from './table'\n// renderers\nimport MainTable from './renderers/main-table'\nimport LeftTable from './renderers/left-table'\nimport RightTable from './renderers/right-table'\nimport Row from './renderers/row'\nimport Cell from './renderers/cell'\nimport Header from './renderers/header'\nimport HeaderCell from './renderers/header-cell'\nimport Footer from './renderers/footer'\nimport Empty from './renderers/empty'\nimport Overlay from './renderers/overlay'\n\nimport type { TableGridRowSlotParams } from './table-grid'\nimport type { ScrollStrategy } from './composables/use-scrollbar'\nimport type {\n  TableV2HeaderRendererParams,\n  TableV2HeaderRowCellRendererParams,\n  TableV2RowCellRenderParam,\n} from './components'\n\nconst COMPONENT_NAME = 'ElTableV2'\n\nconst TableV2 = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2Props,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n\n    const {\n      columnsStyles,\n      fixedColumnsOnLeft,\n      fixedColumnsOnRight,\n      mainColumns,\n      mainTableHeight,\n      fixedTableHeight,\n      leftTableWidth,\n      rightTableWidth,\n      data,\n      depthMap,\n      expandedRowKeys,\n      hasFixedColumns,\n      mainTableRef,\n      leftTableRef,\n      rightTableRef,\n      isDynamic,\n      isResetting,\n      isScrolling,\n\n      bodyWidth,\n      emptyStyle,\n      rootStyle,\n      headerWidth,\n      footerHeight,\n\n      showEmpty,\n\n      // exposes\n      scrollTo,\n      scrollToLeft,\n      scrollToTop,\n      scrollToRow,\n\n      getRowHeight,\n      onColumnSorted,\n      onRowHeightChange,\n      onRowHovered,\n      onRowExpanded,\n      onRowsRendered,\n      onScroll,\n      onVerticalScroll,\n    } = useTable(props)\n\n    expose({\n      /**\n       * @description scroll to a given position\n       * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n       */\n      scrollTo,\n      /**\n       * @description scroll to a given position horizontally\n       * @params scrollLeft {Number} where to scroll to.\n       */\n      scrollToLeft,\n      /**\n       * @description scroll to a given position vertically\n       * @params scrollTop { Number } where to scroll to.\n       */\n      scrollToTop,\n      /**\n       * @description scroll to a given row\n       * @params row {Number} which row to scroll to\n       * @params @optional strategy {ScrollStrategy} use what strategy to scroll to\n       */\n      scrollToRow,\n    })\n\n    provide(TableV2InjectionKey, {\n      ns,\n      isResetting,\n      isScrolling,\n    })\n\n    return () => {\n      const {\n        cache,\n        cellProps,\n        estimatedRowHeight,\n        expandColumnKey,\n        fixedData,\n        headerHeight,\n        headerClass,\n        headerProps,\n        headerCellProps,\n        sortBy,\n        sortState,\n        rowHeight,\n        rowClass,\n        rowEventHandlers,\n        rowKey,\n        rowProps,\n        scrollbarAlwaysOn,\n        indentSize,\n        iconSize,\n        useIsScrolling,\n        vScrollbarSize,\n        width,\n      } = props\n\n      const _data = unref(data)\n\n      const mainTableProps = {\n        cache,\n        class: ns.e('main'),\n        columns: unref(mainColumns),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        bodyWidth: unref(bodyWidth) + vScrollbarSize,\n        headerHeight,\n        headerWidth: unref(headerWidth),\n        height: unref(mainTableHeight),\n        mainTableRef,\n        rowKey,\n        rowHeight,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width,\n        getRowHeight,\n        onRowsRendered,\n        onScroll,\n      }\n\n      const leftColumnsWidth = unref(leftTableWidth)\n      const _fixedTableHeight = unref(fixedTableHeight)\n\n      const leftTableProps = {\n        cache,\n        class: ns.e('left'),\n        columns: unref(fixedColumnsOnLeft),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        leftTableRef,\n        rowHeight,\n        bodyWidth: leftColumnsWidth,\n        headerWidth: leftColumnsWidth,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        useIsScrolling,\n        width: leftColumnsWidth,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n\n      const rightColumnsWidth = unref(rightTableWidth)\n      const rightColumnsWidthWithScrollbar = rightColumnsWidth + vScrollbarSize\n\n      const rightTableProps = {\n        cache,\n        class: ns.e('right'),\n        columns: unref(fixedColumnsOnRight),\n        data: _data,\n        fixedData,\n        estimatedRowHeight,\n        rightTableRef,\n        rowHeight,\n        bodyWidth: rightColumnsWidthWithScrollbar,\n        headerWidth: rightColumnsWidthWithScrollbar,\n        headerHeight,\n        height: _fixedTableHeight,\n        rowKey,\n        scrollbarAlwaysOn,\n        scrollbarStartGap: 2,\n        scrollbarEndGap: vScrollbarSize,\n        width: rightColumnsWidthWithScrollbar,\n        style: `--${unref(\n          ns.namespace\n        )}-table-scrollbar-size: ${vScrollbarSize}px`,\n        useIsScrolling,\n        getRowHeight,\n        onScroll: onVerticalScroll,\n      }\n      const _columnsStyles = unref(columnsStyles)\n\n      const tableRowProps = {\n        ns,\n        depthMap: unref(depthMap),\n        columnsStyles: _columnsStyles,\n        expandColumnKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        estimatedRowHeight,\n        hasFixedColumns: unref(hasFixedColumns),\n        rowProps,\n        rowClass,\n        rowKey,\n        rowEventHandlers,\n        onRowHovered,\n        onRowExpanded,\n        onRowHeightChange,\n      }\n\n      const tableCellProps = {\n        cellProps,\n        expandColumnKey,\n        indentSize,\n        iconSize,\n        rowKey,\n        expandedRowKeys: unref(expandedRowKeys),\n        ns,\n      }\n\n      const tableHeaderProps = {\n        ns,\n        headerClass,\n        headerProps,\n        columnsStyles: _columnsStyles,\n      }\n\n      const tableHeaderCellProps = {\n        ns,\n\n        sortBy,\n        sortState,\n        headerCellProps,\n        onColumnSorted,\n      }\n\n      const tableSlots = {\n        row: (props: TableGridRowSlotParams) => (\n          <Row {...props} {...tableRowProps}>\n            {{\n              row: slots.row,\n              cell: (props: TableV2RowCellRenderParam) =>\n                slots.cell ? (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  >\n                    {slots.cell(props)}\n                  </Cell>\n                ) : (\n                  <Cell\n                    {...props}\n                    {...tableCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  />\n                ),\n            }}\n          </Row>\n        ),\n        header: (props: TableV2HeaderRendererParams) => (\n          <Header {...props} {...tableHeaderProps}>\n            {{\n              header: slots.header,\n              cell: (props: TableV2HeaderRowCellRendererParams) =>\n                slots['header-cell'] ? (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  >\n                    {slots['header-cell'](props)}\n                  </HeaderCell>\n                ) : (\n                  <HeaderCell\n                    {...props}\n                    {...tableHeaderCellProps}\n                    style={_columnsStyles[props.column.key]}\n                  />\n                ),\n            }}\n          </Header>\n        ),\n      }\n\n      const rootKls = [\n        props.class,\n        ns.b(),\n        ns.e('root'),\n        {\n          [ns.is('dynamic')]: unref(isDynamic),\n        },\n      ]\n\n      const footerProps = {\n        class: ns.e('footer'),\n        style: unref(footerHeight),\n      }\n\n      return (\n        <div class={rootKls} style={unref(rootStyle)}>\n          <MainTable {...mainTableProps}>{tableSlots}</MainTable>\n          <LeftTable {...leftTableProps}>{tableSlots}</LeftTable>\n          <RightTable {...rightTableProps}>{tableSlots}</RightTable>\n          {slots.footer && (\n            <Footer {...footerProps}>{{ default: slots.footer }}</Footer>\n          )}\n          {unref(showEmpty) && (\n            <Empty class={ns.e('empty')} style={unref(emptyStyle)}>\n              {{ default: slots.empty }}\n            </Empty>\n          )}\n          {slots.overlay && (\n            <Overlay class={ns.e('overlay')}>\n              {{ default: slots.overlay }}\n            </Overlay>\n          )}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2\n\nexport type TableV2Instance = InstanceType<typeof TableV2> & {\n  /**\n   * @description scroll to a given position\n   * @params params {{ scrollLeft?: number, scrollTop?: number }} where to scroll to.\n   */\n  scrollTo: (param: { scrollLeft?: number; scrollTop?: number }) => void\n  /**\n   * @description scroll to a given position horizontally\n   * @params scrollLeft {Number} where to scroll to.\n   */\n  scrollToLeft: (scrollLeft: number) => void\n  /**\n   * @description scroll to a given position vertically\n   * @params scrollTop { Number } where to scroll to.\n   */\n  scrollToTop: (scrollTop: number) => void\n  /**\n   * @description scroll to a given row\n   * @params row {Number} which row to scroll to\n   * @params strategy {ScrollStrategy} use what strategy to scroll to\n   */\n  scrollToRow(row: number, strategy?: ScrollStrategy): void\n}\n"], "names": ["Empty", "Overlay", "_isVNode", "COMPONENT_NAME", "TableV2", "name", "props", "expose", "columnsStyles", "fixedColumnsOnLeft", "fixedColumnsOnRight", "mainColumns", "mainTableHeight", "fixedTableHeight", "leftTableWidth", "rightTableWidth", "data", "depthMap", "expandedRowKeys", "hasFixedColumns", "mainTableRef", "leftTableRef", "rightTableRef", "isDynamic", "isResetting", "isScrolling", "bodyWidth", "emptyStyle", "rootStyle", "headerWidth", "footerHeight", "showEmpty", "scrollTo", "scrollToLeft", "scrollToTop", "scrollToRow", "getRowHeight", "onColumnSorted", "useTable", "onRowExpanded", "onRowsRendered", "onScroll", "onVerticalScroll", "TableV2InjectionKey", "unref", "cache", "cellProps", "estimatedRowHeight", "expandColumnKey", "fixedData", "headerHeight", "headerClass", "headerProps", "headerCellProps", "sortBy", "sortState", "rowHeight", "rowClass", "rowEventHandlers", "<PERSON><PERSON><PERSON>", "rowProps", "scrollbarAlwaysOn", "indentSize", "iconSize", "useIsScrolling", "vScrollbarSize", "width", "class", "columns", "height", "scrollbarStartGap", "scrollbarEndGap", "_fixedTableHeight", "_data", "style", "ns", "_columnsStyles", "_createVNode", "Cell", "_mergeProps", "onRowHeightChange", "Header", "<PERSON><PERSON><PERSON><PERSON>", "row", "cell", "LeftTable", "slots", "Footer", "header"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAeA,SAAOA,SAAW,EAAlB;AACA,EAAOC,OAAAA,aAAP,UAAA,IAAA,MAAA,CAAA,SAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,KAAA,iBAAA,IAAA,CAAAC,WAAA,CAAA,CAAA,CAAA,CAAA;;;;;;AAUA,EAAMC,KAAAA,CAAAA,KAAAA,EAAAA;AAEN,IAAMC,KAAAA;AACJC,IAAAA,MAD8B;AAE9BC,GAAAA,EAAAA;;IACK;MAAQ,aAAA;AAASC,MAAAA,kBAAAA;AAAT,MAAmB,mBAAA;AAC9B,MAAA,WAAuB;MAEjB,eAAA;MACJC,gBADI;MAEJC,cAFI;MAGJC,eAHI;MAIJC,IAJI;MAKJC,QALI;MAMJC,eANI;MAOJC,eAPI;MAQJC,YARI;MASJC,YATI;MAUJC,aAVI;MAWJC,SAXI;MAYJC,WAZI;MAaJC,WAbI;MAcJC,SAdI;MAeJC,UAfI;MAgBJC,SAhBI;MAiBJC,WAjBI;MAkBJC,YAlBI;MAoBJC,SApBI;MAqBJC,QArBI;MAsBJC,YAtBI;MAuBJC,WAvBI;MAwBJC,WAxBI;MA0BJC,YA1BI;AA4BJ,MAAA,cAAA;MACAC,iBA7BI;MA8BJC,YA9BI;MA+BJC,aA/BI;MAgCJC,cAhCI;MAkCJC,QAlCI;MAmCJC,gBAnCI;QAAAC,iBAAA,CAAA,KAAA,CAAA,CAAA;UAAA,CAAA;MAsCJC,QAtCI;MAuCJC,YAvCI;MAwCJC,WAxCI;AAyCJC,MAAAA,WAAAA;KACEJ,CAAAA,CAAAA;AAEJ/B,IAAAA,WAAO,CAAAoC,0BAAA,EAAA;AACL,MAAA,EAAA;AACN,MAAA,WAAA;AACA,MAAA,WAAA;AACA,KAAA,CAAA,CAAA;WAJW,MAAA;;AAML,QAAA,KAAA;AACN,QAAA,SAAA;AACA,QAAA,kBAAA;AACA,QAAA,eAAA;QATW,SAAA;;AAWL,QAAA,WAAA;AACN,QAAA,WAAA;AACA,QAAA,eAAA;AACA,QAAA,MAAA;QAdW,SAAA;;AAgBL,QAAA,QAAA;AACN,QAAA,gBAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACA,QAAA,iBAAA;AACMR,QAAAA,UAAAA;AArBK,QAAP,QAAA;QAwBO;QAAsB,cAAA;QAAA,KAAA;AAG3BV,OAAAA,GAAAA,KAAAA,CAAAA;AAH2B,MAA7B,MAAA,KAAA,GAAAmB,SAAA,CAAA,IAAA,CAAA,CAAA;AAMA,MAAA,MAAa,cAAA,GAAA;QACL,KAAA;QACJC,KADI,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;QAEJC,OAFI,EAAAF,SAAA,CAAA,WAAA,CAAA;QAGJG,IAHI,EAAA,KAAA;QAIJC,SAJI;QAKJC,kBALI;QAMJC,SANI,EAAAN,SAAA,CAAA,SAAA,CAAA,GAAA,cAAA;QAOJO,YAPI;QAQJC,WARI,EAAAR,SAAA,CAAA,WAAA,CAAA;QASJS,MATI,EAAAT,SAAA,CAAA,eAAA,CAAA;QAUJU,YAVI;QAWJC,MAXI;QAYJC,SAZI;QAaJC,iBAbI;QAcJC,iBAdI,EAAA,CAAA;QAeJC,eAfI,EAAA,cAAA;QAgBJC,cAhBI;QAiBJC,KAjBI;QAkBJC,YAlBI;QAmBJC,cAnBI;QAoBJC,QApBI;QAqBJC;AACAC,MAAAA,MAAAA,gBAAAA,GAAAA,SAAAA,CAAAA,cAAAA,CAAAA,CAAAA;AAtBI,MAAA,MAAN,iBAAA,GAAAtB,SAAA,CAAA,gBAAA,CAAA,CAAA;;AAyBA,QAAA,KAAW;;AAEX,QAAA,qCAAuB,CAAA;QACrBC,IADqB,EAAA,KAAA;AAErBsB,QAAAA,SAAS;AACTC,QAAAA,kBAAezD;AACfK,QAAAA,YAJqB;QAKrBiC,SALqB;QAMrBF,SANqB,EAAA,gBAAA;AAOrBrB,QAAAA,WAAWkB,EAAK;QAChBM,YARqB;AASrBrB,QAAAA,MAAAA,EAAAA,iBAAmBA;AACnBwC,QAAAA,MAAM;QACNjD,iBAXqB;QAYrBuC,iBAZqB,EAAA,CAAA;QAarBH,eAbqB,EAAA,cAAA;QAcrBK,cAdqB;AAerBS,QAAAA,KAAAA,EAAAA,gBAfqB;AAgBrBC,QAAAA,YAAAA;QACAP,QAjBqB,EAAA,gBAAA;QAkBrBE;YAlBqB,iBAAA,GAAAtB,SAAA,CAAA,eAAA,CAAA,CAAA;YAAA,8BAAA,GAAA,iBAAA,GAAA,cAAA,CAAA;AAqBrBH,MAAAA,MAAAA,eAAAA,GAAAA;QArBF,KAAA;AAwBA,QAAA,KAAsB,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;;AACtB,QAAA,IAAM+B,EAAiB,KAAA;;AAEvB,QAAA,kBAAoB;QAClB3B,aADqB;AAErBsB,QAAAA,SAAS;AACTC,QAAAA,SAASxB,EAAAA,8BAHY;AAIrB5B,QAAAA,WAJqB,EAAA,8BAAA;QAKrBiC,YALqB;QAMrBF,MANqB,EAAA,iBAAA;QAOrB1B,MAPqB;QAQrBmC,iBARqB;AASrB9B,QAAAA,iBATqB,EAAA,CAAA;AAUrBG,QAAAA,eAVqB,EAAA,cAAA;QAWrBqB,KAXqB,EAAA,8BAAA;AAYrBmB,QAAAA,KAAAA,GAAQG,EAZa,EAAA5B,SAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,uBAAA,EAAA,cAAA,CAAA,EAAA,CAAA;QAarBe,cAbqB;QAcrBE,YAdqB;AAerBS,QAAAA,QAAAA,EAAAA,gBAfqB;AAgBrBC,OAAAA,CAAAA;YAhBqB,cAAA,GAAA3B,SAAA,CAAA,aAAA,CAAA,CAAA;AAkBrBsB,MAAAA,MAAAA,aAlBqB,GAAA;QAmBrB9B,EAnBqB;AAoBrBK,QAAAA,QAAQ,EAAEC,SAAAA,CAAAA,QAAAA,CAAAA;QApBZ,aAAA,EAAA,cAAA;AAuBA,QAAA,eAAuB;AACvB,QAAA,eAAoC,EAAAE,SAAA,CAAA,eAAoB,CAAA;AAExD,QAAA;QACEC,eADsB,EAAAD,SAAA,CAAA,eAAA,CAAA;AAEtBuB,QAAAA,QAAS;AACTC,QAAAA,QAASxB;AACT5B,QAAAA,MAAMyD;QACNxB,gBALsB;QAMtBF,YANsB;QAOtBzB,aAPsB;QAQtBkC,iBARsB;AAStB9B,OAAAA,CAAAA;AACAG,MAAAA,MAAAA,cAVsB,GAAA;QAWtBqB,SAXsB;AAYtBmB,QAAAA,eAZsB;QAatBV,UAbsB;QActBE,QAdsB;AAetBS,QAAAA,MAAAA;AACAC,QAAAA,eAAe,EAAEN,SAhBK,CAAA,eAAA,CAAA;AAiBtBC,QAAAA,EAAAA;QACAQ;YAlBsB,gBAAA,GAAA;QAsBtBtC,EAtBsB;AAuBtBK,QAAAA,WAAUC;QAvBZ,WAAA;;AAyBA,OAAA,CAAA;;AAEA,QAAA,EAAA;QACEiC,MADoB;AAEpB1D,QAAAA,SAAU2B;AACVpC,QAAAA,eAAeoE;QACf5B,cAJoB;AAKpB9B,OAAAA,CAAAA;YALoB,UAAA,GAAA;AAOpBC,QAAAA,GAAAA,EAAAA,CAAAA,MAAAA,KAAiByB,eAAMzB,CAAAA,cAAAA,EAAAA,cAPH,CAAA,MAAA,EAAA,aAAA,CAAA,EAAA;UAAA,GAAA,EAAA,KAAA,CAAA,GAAA;UAAA,IAAA,EAAA,CAAA,MAAA,KAAA;YAAA,IAAA,KAAA,CAAA;YAAA,OAAA,KAAA,CAAA,IAAA,GAAA0D,eAAA,CAAAC,eAAA,EAAAC,cAAA,CAAA,MAAA,EAAA,cAAA,EAAA;cAAA,OAAA,EAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,GAAA,KAAA,GAAA;AAcpBC,cAAAA,OAAAA,EAAAA,MAAAA,CAAAA,KAAAA,CAAAA;aAdF,CAAA,GAAAH,eAAA,CAAAC,eAAA,EAAAC,cAAA,CAAA,MAAA,EAAA,cAAA,EAAA;AAiBA,qCAAuB,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,IAAA,CAAA,CAAA;WAAA;SAAA,CAAA;QAIrBhB,MAJqB,EAAA,CAAA,MAAA,KAAAc,eAAA,CAAAI,iBAAA,EAAAF,cAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;UAAA,MAAA,EAAA,KAAA,CAAA,MAAA;AAMrB7D,UAAAA,IAAAA,EAAAA,CAAAA,MAAe,KAAE0B;AACjB+B,YAAAA,IAAAA,MAAAA,CAAAA;YAPF,OAAA,KAAA,CAAA,aAAA,CAAA,GAAAE,eAAA,CAAAK,qBAAA,EAAAH,cAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;AAUA,qCAAyB,CAAA,MAAA,CAAA,MAAA,CAAA,GAAA,CAAA;aAAA,CAAA,EAAA,OAAA,CAAA,MAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,MAAA,GAAA;cAAA,OAAA,EAAA,MAAA,CAAA,MAAA,CAAA;aAAA,CAAA,GAAAF,eAAA,CAAAK,qBAAA,EAAAH,cAAA,CAAA,MAAA,EAAA,oBAAA,EAAA;AAIvBvE,cAAAA,OAAa,EAAEoE,cAAAA,CAAAA,MAAAA,CAAAA,MAAAA,CAAAA,GAAAA,CAAAA;aAJjB,CAAA,EAAA,IAAA,CAAA,CAAA;AAOA,WAAA;SAA6B,CAAA;QAG3BtB;YAH2B,OAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;QAK3BD,CAL2B,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,GAAAT,SAAA,CAAA,SAAA,CAAA;AAM3BP,OAAAA,CAAAA,CAAAA;MAN2B,MAA7B,WAAA,GAAA;AASA,QAAA,oBAAmB,CAAA;AACjB8C,QAAAA,KAAM7E,EAAAA,SACKA,CAAAA,YAAAA,CAAAA;;AAGL8E,MAAAA,OAAAA,eAAM,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,OAAA;;AAAA,OAAA,EAAA,CAAAP,oCAGIvE,EAAAA,cAAAA,EAAAA,OAAAA,CAAAA,UAFR,IAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAIWsE;AAJX,OAAA,CAAA,EAAAC,eAAA,CAAAQ,oBAMKC,EAAK,cAAL,EANL,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAAA,CAAA,UAAA,CAAA;yBAUQhF,CAAAA,qBAAAA,EAAAA,eAAAA,EAAAA,OAVR,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA;AAAA,QAAA,OAAA,EAAA,MAYWsE;eAbP,CAAA,MAAA,IAAAC,eAAA,CAAAU,iBAAA,EAAA,WAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,MAAA;QAJP,EADY3C,SAAA,CAAA,SAAA,CAAA,IAAAiC,eAAA,CAAA7E,gBAAA,EAAA;AAwBjBwF,QAAAA,OAASlF,EAAAA,EAAAA,CAAAA,CAAD,CACMA,OAAAA,CAAAA;eAEF,EAAAsC,SAAO,CAAA,UAHX,CAAA;AAIFwC,OAAAA,EAAAA;AAAM,QAAA,OAAA,EAAA,KAAA,CAAA,KAAA;;AAAA,QAAA,OAAA,EAAA,EACJE,YAAM,CAAA;AAAN,OAAA,EAAA;AAAA,QAAA,OAAA,EAAA,KAAA,CAAA,OAAA;AAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;AAAA,GAAA;;AADI,gBAAA,OAAA;;;;"}
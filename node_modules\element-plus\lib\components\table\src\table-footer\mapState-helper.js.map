{"version": 3, "file": "mapState-helper.js", "sources": ["../../../../../../../packages/components/table/src/table-footer/mapState-helper.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nfunction useMapState() {\n  const table = inject(TABLE_INJECTION_KEY)\n  const store = table?.store\n  const leftFixedLeafCount = computed(() => {\n    return store.states.fixedLeafColumnsLength.value\n  })\n  const rightFixedLeafCount = computed(() => {\n    return store.states.rightFixedColumns.value.length\n  })\n  const columnsCount = computed(() => {\n    return store.states.columns.value.length\n  })\n  const leftFixedCount = computed(() => {\n    return store.states.fixedColumns.value.length\n  })\n  const rightFixedCount = computed(() => {\n    return store.states.rightFixedColumns.value.length\n  })\n\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: store.states.columns,\n  }\n}\n\nexport default useMapState\n"], "names": ["inject", "TABLE_INJECTION_KEY", "computed"], "mappings": ";;;;;;;AAEA,SAAS,WAAW,GAAG;AACvB,EAAE,MAAM,KAAK,GAAGA,UAAM,CAACC,0BAAmB,CAAC,CAAC;AAC5C,EAAE,MAAM,KAAK,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AACrD,EAAE,MAAM,kBAAkB,GAAGC,YAAQ,CAAC,MAAM;AAC5C,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC;AACrD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAGA,YAAQ,CAAC,MAAM;AAC7C,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC;AACvD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAGA,YAAQ,CAAC,MAAM;AACxC,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;AAClD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAGA,YAAQ,CAAC,MAAM;AACzC,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC;AACvD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;AACjC,GAAG,CAAC;AACJ;;;;"}
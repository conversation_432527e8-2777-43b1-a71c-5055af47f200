{"version": 3, "file": "tabs.js", "sources": ["../../../../../../packages/components/tabs/src/tabs.tsx"], "sourcesContent": ["import {\n  computed,\n  createVNode,\n  defineComponent,\n  getCurrentInstance,\n  nextTick,\n  provide,\n  ref,\n  renderSlot,\n  watch,\n} from 'vue'\nimport {\n  buildProps,\n  definePropType,\n  isNumber,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElIcon from '@element-plus/components/icon'\nimport { Plus } from '@element-plus/icons-vue'\nimport { useNamespace, useOrderedChildren } from '@element-plus/hooks'\nimport { tabsRootContextKey } from './constants'\nimport TabNav from './tab-nav'\n\nimport type { TabNavInstance } from './tab-nav'\nimport type { TabsPaneContext } from './constants'\nimport type { ExtractPropTypes, FunctionalComponent, VNode } from 'vue'\nimport type { Awaitable } from '@element-plus/utils'\n\nexport type TabPaneName = string | number\n\nexport const tabsProps = buildProps({\n  /**\n   * @description type of Tab\n   */\n  type: {\n    type: String,\n    values: ['card', 'border-card', ''],\n    default: '',\n  },\n  /**\n   * @description whether Tab is closable\n   */\n  closable: Boolean,\n  /**\n   * @description whether Tab is addable\n   */\n  addable: Boolean,\n  /**\n   * @description binding value, name of the selected tab\n   */\n  modelValue: {\n    type: [String, Number],\n  },\n  /**\n   * @description whether Tab is addable and closable\n   */\n  editable: Boolean,\n  /**\n   * @description position of tabs\n   */\n  tabPosition: {\n    type: String,\n    values: ['top', 'right', 'bottom', 'left'],\n    default: 'top',\n  },\n  /**\n   * @description hook function before switching tab. If `false` is returned or a `Promise` is returned and then is rejected, switching will be prevented\n   */\n  beforeLeave: {\n    type: definePropType<\n      (newName: TabPaneName, oldName: TabPaneName) => Awaitable<void | boolean>\n    >(Function),\n    default: () => true,\n  },\n  /**\n   * @description whether width of tab automatically fits its container\n   */\n  stretch: Boolean,\n} as const)\nexport type TabsProps = ExtractPropTypes<typeof tabsProps>\n\nconst isPaneName = (value: unknown): value is string | number =>\n  isString(value) || isNumber(value)\n\nexport const tabsEmits = {\n  [UPDATE_MODEL_EVENT]: (name: TabPaneName) => isPaneName(name),\n  tabClick: (pane: TabsPaneContext, ev: Event) => ev instanceof Event,\n  tabChange: (name: TabPaneName) => isPaneName(name),\n  edit: (paneName: TabPaneName | undefined, action: 'remove' | 'add') =>\n    ['remove', 'add'].includes(action),\n  tabRemove: (name: TabPaneName) => isPaneName(name),\n  tabAdd: () => true,\n}\nexport type TabsEmits = typeof tabsEmits\n\nexport type TabsPanes = Record<number, TabsPaneContext>\n\nconst Tabs = defineComponent({\n  name: 'ElTabs',\n\n  props: tabsProps,\n  emits: tabsEmits,\n\n  setup(props, { emit, slots, expose }) {\n    const ns = useNamespace('tabs')\n\n    const isVertical = computed(() =>\n      ['left', 'right'].includes(props.tabPosition)\n    )\n\n    const {\n      children: panes,\n      addChild: sortPane,\n      removeChild: unregisterPane,\n    } = useOrderedChildren<TabsPaneContext>(getCurrentInstance()!, 'ElTabPane')\n\n    const nav$ = ref<TabNavInstance>()\n    const currentName = ref<TabPaneName>(props.modelValue ?? '0')\n\n    const setCurrentName = async (value?: TabPaneName, trigger = false) => {\n      // should do nothing.\n      if (currentName.value === value || isUndefined(value)) return\n\n      try {\n        let canLeave\n        if (props.beforeLeave) {\n          const result = props.beforeLeave(value, currentName.value)\n          canLeave = result instanceof Promise ? await result : result\n        } else {\n          canLeave = true\n        }\n\n        if (canLeave !== false) {\n          currentName.value = value\n          if (trigger) {\n            emit(UPDATE_MODEL_EVENT, value)\n            emit('tabChange', value)\n          }\n\n          nav$.value?.removeFocus?.()\n        }\n      } catch {}\n    }\n\n    const handleTabClick = (\n      tab: TabsPaneContext,\n      tabName: TabPaneName,\n      event: Event\n    ) => {\n      if (tab.props.disabled) return\n      emit('tabClick', tab, event)\n      setCurrentName(tabName, true)\n    }\n\n    const handleTabRemove = (pane: TabsPaneContext, ev: Event) => {\n      if (pane.props.disabled || isUndefined(pane.props.name)) return\n      ev.stopPropagation()\n      emit('edit', pane.props.name, 'remove')\n      emit('tabRemove', pane.props.name)\n    }\n\n    const handleTabAdd = () => {\n      emit('edit', undefined, 'add')\n      emit('tabAdd')\n    }\n\n    watch(\n      () => props.modelValue,\n      (modelValue) => setCurrentName(modelValue)\n    )\n\n    watch(currentName, async () => {\n      await nextTick()\n      nav$.value?.scrollToActiveTab()\n    })\n\n    provide(tabsRootContextKey, {\n      props,\n      currentName,\n      registerPane: (pane: TabsPaneContext) => {\n        panes.value.push(pane)\n      },\n      sortPane,\n      unregisterPane,\n    })\n\n    expose({\n      currentName,\n    })\n    const TabNavRenderer: FunctionalComponent<{ render: () => VNode }> = ({\n      render,\n    }) => {\n      return render()\n    }\n    return () => {\n      const addSlot = slots['add-icon']\n      const newButton =\n        props.editable || props.addable ? (\n          <div\n            class={[\n              ns.e('new-tab'),\n              isVertical.value && ns.e('new-tab-vertical'),\n            ]}\n            tabindex=\"0\"\n            onClick={handleTabAdd}\n            onKeydown={(ev: KeyboardEvent) => {\n              if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter].includes(ev.code))\n                handleTabAdd()\n            }}\n          >\n            {addSlot ? (\n              renderSlot(slots, 'add-icon')\n            ) : (\n              <ElIcon class={ns.is('icon-plus')}>\n                <Plus />\n              </ElIcon>\n            )}\n          </div>\n        ) : null\n\n      const header = (\n        <div\n          class={[\n            ns.e('header'),\n            isVertical.value && ns.e('header-vertical'),\n            ns.is(props.tabPosition),\n          ]}\n        >\n          <TabNavRenderer\n            render={() => {\n              const hasLabelSlot = panes.value.some((pane) => pane.slots.label)\n              return createVNode(\n                TabNav,\n                {\n                  ref: nav$,\n                  currentName: currentName.value,\n                  editable: props.editable,\n                  type: props.type,\n                  panes: panes.value,\n                  stretch: props.stretch,\n                  onTabClick: handleTabClick,\n                  onTabRemove: handleTabRemove,\n                },\n                { $stable: !hasLabelSlot }\n              )\n            }}\n          />\n          {newButton}\n        </div>\n      )\n\n      const panels = (\n        <div class={ns.e('content')}>{renderSlot(slots, 'default')}</div>\n      )\n\n      return (\n        <div\n          class={[\n            ns.b(),\n            ns.m(props.tabPosition),\n            {\n              [ns.m('card')]: props.type === 'card',\n              [ns.m('border-card')]: props.type === 'border-card',\n            },\n          ]}\n        >\n          {panels}\n          {header}\n        </div>\n      )\n    }\n  },\n})\n\nexport type TabsInstance = InstanceType<typeof Tabs> & {\n  currentName: TabPaneName\n}\n\nexport default Tabs\n"], "names": ["tabsProps", "buildProps", "type", "values", "default", "closable", "addable", "modelValue", "isString", "isNumber", "UPDATE_MODEL_EVENT", "editable", "defineComponent", "tabPosition", "useNamespace", "computed", "beforeLeave", "definePropType", "ref", "stretch", "Boolean", "isUndefined", "isPaneName", "name", "ev", "tabChange", "edit", "tabRemove", "tabAdd", "props", "emits", "expose", "ns", "children", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "unregisterPane", "getCurrentInstance", "nav$", "currentName", "setCurrentName", "value", "trigger", "result", "nextTick", "canLeave", "provide", "tabsRootContextKey", "emit", "handleTabClick", "tabName", "_createVNode", "EVENT_CODE", "pane", "handleTabAdd", "Plus", "watch", "createVNode", "TabNav", "panes", "TabNav<PERSON><PERSON><PERSON>", "render", "renderSlot", "newButton"], "mappings": ";;;;;;;;;;;;;;;;;AAgCaA,MAAAA,SAAS,GAAGC,kBAAU,CAAC;AAClC,EAAA,IAAA,EAAA;AACF,IAAA,IAAA,EAAA,MAAA;AACA,IAAA,MAAA,EAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA;AACEC,IAAAA,OAAM,EAAA,EAAA;AACJA,GAAAA;AACAC,EAAAA,QAAM,EAAE,OAAA;AACRC,EAAAA,OAAAA,EAAO,OAAE;EAHL,UAJ4B,EAAA;;AASlC,GAAA;AACF,EAAA,QAAA,EAAA,OAAA;AACA,EAAA,WAAA,EAAA;AACEC,IAAAA,IAAAA,EAAQ,MAZ0B;;AAalC,IAAA,OAAA,EAAA,KAAA;AACF,GAAA;AACA,EAAA,WAAA,EAAA;AACEC,IAAAA,IAAAA,wBAhBkC,CAAA,QAAA,CAAA;;AAiBlC,GAAA;AACF,EAAA,OAAA,EAAA,OAAA;AACA,CAAA,EAAA;AACEC,MAAAA,UAAY,GAAA,CAAA,KAAA,KAAAC,eAAA,CAAA,KAAA,CAAA,IAAAC,cAAA,CAAA,KAAA,CAAA,CAAA;AACJ,MAAA,SAAA,GAAA;EADI,CApBsBC,wBAAA,GAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;;AAuBlC,EAAA,SAAA,EAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;AACF,EAAA,IAAA,EAAA,CAAA,QAAA,EAAA,MAAA,KAAA,CAAA,QAAA,EAAA,KAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA;AACA,EAAA,SAAA,EAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,CAAA;AACEC,EAAAA,MAAAA,EAAQ,MA1B0B,IAAA;;AA2BlC,MAAA,IAAA,GAAAC,mBAAA,CAAA;AACF,EAAA,IAAA,EAAA,QAAA;AACA,EAAA,KAAA,EAAA,SAAA;AACEC,EAAAA,KAAAA,EAAAA,SAAa;AACXX,EAAAA,KAAAA,CAAI,KADO,EAAA;IAEXC,IAAM;AACNC,IAAAA,KAAAA;IAjCgC,MAAA;;AAmClC,IAAA,IAAA,EAAA,CAAA;AACF,IAAA,MAAA,EAAA,GAAAU,kBAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,MAAA,UAAA,GAAAC,YAAA,CAAA,MAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACEC,IAAAA,MAAAA;AACEd,MAAAA,QAAMe,EAAAA,KAAAA;AAGNb,MAAAA,QAAe,EAAA,QAAA;MA1CiB,WAAA,EAAA,cAAA;;AA4ClC,IAAA,MAAA,IAAA,GAAAc,OAAA,EAAA,CAAA;AACF,IAAA,MAAA,WAAA,GAAAA,OAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,GAAA,CAAA,CAAA;AACA,IAAA,MAAA,cAAA,GAAA,OAAA,KAAA,EAAA,OAAA,GAAA,KAAA,KAAA;AACEC,MAAAA,IAASC,GAAAA,EAAAA,EAAAA,CAAAA;AA/CyB,MAA7B,IAAA,WAAA,CAAA,KAAA,KAAA,KAAA,IAAAC,iBAAA,CAAA,KAAA,CAAA;;AAmDP,MAAMC,IAAU;;AAGhB,QAAO,IAAA,iBAAkB,EAAA;AACvB,sBAAuBC,GAAAA,iBAAgC,CAACA,KADjC,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA;UAEf,QAAE,GAAA,MAAsCC,mBAFzB,GAAA,MAAA,MAAA,GAAA,MAAA,CAAA;AAGvBC,SAAAA,MAAW;AACXC,UAAM,QAAA,OAAA,CACJ;AACFC,SAAAA;AACAC,QAAM,IAAQ,QAAA,KAAA,KAAA,EAAA;AAPS,UAAlB,WAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAaP,UAAU,IAAGhB,OAAAA,EAAAA;AACXW,YAD2B,IAAA,CAAAb,wBAAA,EAAA,KAAA,CAAA,CAAA;AAG3BmB,YAH2B,IAAA,CAAA,WAAA,EAAA,KAAA,CAAA,CAAA;AAI3BC,WAJ2B;;;OAMd,CAAA,OAAA,CAAA,EAAA;OAAA;AAAeC,KAAAA,CAAAA;AAAf,IAAyB,MAAA,cAAA,GAAA,CAAA,GAAA,EAAA,OAAA,EAAA,KAAA,KAAA;AACpC,MAAA,IAAMC,GAAE,CAAA,KAAe,CAAA,QAAC;AAExB,QAAA;MAIM,IAAA,CAAA,UAAA,EAAA,GAAA,EAAA,KAAA,CAAA,CAAA;AACJC,MAAAA,cADI,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AAEJC,KAAAA,CAAAA;AACAC,IAAAA,MAAAA,eAAaC,GAAAA,CAAAA,IAAAA,EAAAA,EAAAA,KAAAA;AAHT,MAAA,uBAIkCC,IAAAA,iBAAAA,CAAkB,IAAK,WAAzC,CAJtB;QAMMC,OAAOpB;MACPqB,EAAAA,CAAAA,eAAcrB,EAAG,CAAA;;MAEjBsB,IAAAA,CAAAA,WAAAA,EAAc,IAAUC,CAAAA,KAAAA,CAAAA,IAAP,EAA4BC;AACjD,KAAA,CAAA;UACIH,YAAYE,GAAAA,MAAUA;;MAE1B,IAAI,CAAA,QAAA,CAAA,CAAA;AACF,KAAA,CAAA;;aACIZ,CAAAA,WAAMb,EAAAA,YAAa;UACrB,GAAM2B,CAAAA;YACEC,YAAA;AACT,MAAA,CAAA,GAAM,GAAA,IAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,iBAAA,EAAA,CAAA;AACLC,KAAAA,CAAAA,CAAAA;AACD,IAAAC,WAAA,CAAAC,4BAAA,EAAA;;iBAEGF;kBACS,EAAA,CAAA,IAAX,KAAoBJ;;AACpB,OAAA;AACEO,MAAAA,QAAAA;AACAA,MAAAA,cAAK;AACN,KAAA,CAAA,CAAA;;iBAED;AACD,KAAA,CAAA,CAAA;wBACO,GAAA,CAAA;MAtBZ,MAAA;;MAyBMC,OAAAA,MAAAA,EAAAA,CAAAA;AAKJ,KAAA,CAAA;AACAD,IAAAA,OAAK,MAAD;AACJR,MAAAA,MAAAA,OAAAA,GAAeU,KAAAA,CAAD,UAAd,CAAA,CAAA;MAPF,MAAA,SAAA,GAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,OAAA,GAAAC,eAAA,CAAA,KAAA,EAAA;;AAUA,QAAA;AACE,QAAA,SAAI,EAAA,YAAuB9B;AAC3BG,QAAE,WAAF,EAAA,CAAA,EAAA,KAAA;UACI,IAAA,CAAA4B,qBAAoB7B,EAAAA,eAAM,CAA1B,WAAJ,CAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA;wBACkB8B,EAAAA,CAAAA;SAJpB;;QAOMC,OAAAA,EAAAA,EAAAA,CAAAA,EAAY,YAAS,CAAA;AACzBN,OAAAA,EAAAA;QACI,eAAJ,CAAAG,eAAA,CAAAI,aAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;OAFF,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA;;AAKAC,QAAAA,OACE,EAAW,CAAA,EAAA,CAAA,CAACjD,CADT,QAAA,CAAA,EAEFA,UAAD,CAAA,KAA8B,IAAA,EAAA,CAAA,CAAA,CAAA,iBAFhC,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA;OAKK,EAAA,CAACgC,eAAD,CAAc,cAAY,EAAA;AAC7B,QAAA,gBAAA;UACI,MAAJ,YAAA,GAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACD,UAHD,OAAAkB,eAAA,CAAAC,iBAAA,EAAA;YAKQX;YAAoB,WAAA,EAAA,WAAA,CAAA,KAAA;YAAA,QAAA,EAAA,KAAA,CAAA,QAAA;YAGd,IAAA,EAAA,MAAE,IAA2B;AACvCY,YAAAA,KAAMlB,EAAN,KAAA,CAAiBY,KAAjB;YAJwB,OAAA,EAAA,KAAA,CAAA,OAAA;YAAA,UAAA,EAAA,cAAA;AAO1BjB,YAAAA,WAAAA,EAAAA,eAAAA;AAP0B,WAA5B,EAAA;AAUAL,YAAO,OAAA,EAAA,CAAA,YAAA;AACLQ,WAAAA,CAAAA,CAAAA;AADK,SAAP;;MAGMqB,MAAAA,MAAAA,GAAAA,eAAgE,CAAA,KAAA,EAAA;AACpEC,QAAAA,OAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA;AADoE,OAEhE,EAAA,CAAAC,cAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACJ,MAAA,OAAOD,eAAP,CAAA,KAAA,EAAA;QAHF,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA;;AAKA,UAAA,CAAO,EAAM,CAAA,CAAA,CAAA,aAAA,CAAA,GAAA,KAAA,CAAA,IAAA,KAAA,aAAA;AACX,SAAA,CAAA;OACME,EAAAA,CAAAA,MAAAA,EAAAA,MACJlC,CAAK,CAAA,CAAA;AAAL,KAAA,CAAA;AAAA,GAAA;AAAA,CAAA,CAAA,CAAA;AAAA,aAAA,IAAA;;;;;;"}
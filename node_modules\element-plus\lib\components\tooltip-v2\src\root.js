'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const tooltipV2RootProps = runtime.buildProps({
  delayDuration: {
    type: Number,
    default: 300
  },
  defaultOpen: Boolean,
  open: {
    type: Boolean,
    default: void 0
  },
  onOpenChange: {
    type: runtime.definePropType(Function)
  },
  "onUpdate:open": {
    type: runtime.definePropType(Function)
  }
});

exports.tooltipV2RootProps = tooltipV2RootProps;
//# sourceMappingURL=root.js.map

import type { PropType } from 'vue';
export type CacheOption = {
    value: string | number | boolean | object;
    currentLabel: string | number;
    isDisabled: boolean;
};
declare const _default: import("vue").DefineComponent<{
    data: {
        type: PropType<CacheOption[]>;
        default: () => never[];
    };
}, () => undefined, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    data: {
        type: PropType<CacheOption[]>;
        default: () => never[];
    };
}>>, {
    data: CacheOption[];
}>;
export default _default;

{"version": 3, "file": "tree-node-content.js", "sources": ["../../../../../../packages/components/tree-v2/src/tree-node-content.ts"], "sourcesContent": ["import { defineComponent, h, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport ElText from '@element-plus/components/text'\nimport { ROOT_TREE_INJECTION_KEY, treeNodeContentProps } from './virtual-tree'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: treeNodeContentProps,\n  setup(props) {\n    const tree = inject(ROOT_TREE_INJECTION_KEY)\n    const ns = useNamespace('tree')\n    return () => {\n      const node = props.node\n      const { data } = node!\n      return tree?.ctx.slots.default\n        ? tree.ctx.slots.default({ node, data })\n        : h(\n            ElText,\n            { tag: 'span', truncated: true, class: ns.be('node', 'label') },\n            () => [node?.label]\n          )\n    }\n  },\n})\n"], "names": ["defineComponent", "treeNodeContentProps", "inject", "ROOT_TREE_INJECTION_KEY", "useNamespace", "h", "ElText"], "mappings": ";;;;;;;;;AAIA,oBAAeA,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,KAAK,EAAEC,gCAAoB;AAC7B,EAAE,KAAK,CAAC,KAAK,EAAE;AACf,IAAI,MAAM,IAAI,GAAGC,UAAM,CAACC,mCAAuB,CAAC,CAAC;AACjD,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,MAAM,CAAC,CAAC;AACpC,IAAI,OAAO,MAAM;AACjB,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC9B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAC5B,MAAM,OAAO,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAGC,KAAC,CAACC,cAAM,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAChO,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}
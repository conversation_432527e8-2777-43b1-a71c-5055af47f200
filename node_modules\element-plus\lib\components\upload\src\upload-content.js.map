{"version": 3, "file": "upload-content.js", "sources": ["../../../../../../packages/components/upload/src/upload-content.ts"], "sourcesContent": ["import { NOOP, buildProps, definePropType } from '@element-plus/utils'\nimport { uploadBaseProps } from './upload'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type {\n  UploadFile,\n  UploadHooks,\n  UploadProgressEvent,\n  UploadRawFile,\n} from './upload'\nimport type UploadContent from './upload-content.vue'\nimport type { UploadAjaxError } from './ajax'\n\nexport const uploadContentProps = buildProps({\n  ...uploadBaseProps,\n\n  beforeUpload: {\n    type: definePropType<UploadHooks['beforeUpload']>(Function),\n    default: NOOP,\n  },\n  onRemove: {\n    type: definePropType<\n      (file: UploadFile | UploadRawFile, rawFile?: UploadRawFile) => void\n    >(Function),\n    default: NOOP,\n  },\n  onStart: {\n    type: definePropType<(rawFile: UploadRawFile) => void>(Function),\n    default: NOOP,\n  },\n  onSuccess: {\n    type: definePropType<(response: any, rawFile: UploadRawFile) => unknown>(\n      Function\n    ),\n    default: NOOP,\n  },\n  onProgress: {\n    type: definePropType<\n      (evt: UploadProgressEvent, rawFile: UploadRawFile) => void\n    >(Function),\n    default: NOOP,\n  },\n  onError: {\n    type: definePropType<\n      (err: UploadAjaxError, rawFile: UploadRawFile) => void\n    >(Function),\n    default: NOOP,\n  },\n  onExceed: {\n    type: definePropType<UploadHooks['onExceed']>(Function),\n    default: NOOP,\n  },\n} as const)\n\nexport type UploadContentProps = ExtractPropTypes<typeof uploadContentProps>\n\nexport type UploadContentInstance = InstanceType<typeof UploadContent> & unknown\n"], "names": ["buildProps", "uploadBaseProps", "definePropType", "NOOP"], "mappings": ";;;;;;;;AAEY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,GAAGC,sBAAe;AACpB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAED,sBAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,CAAC;;;;"}
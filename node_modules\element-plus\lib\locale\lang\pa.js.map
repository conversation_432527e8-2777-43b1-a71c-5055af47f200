{"version": 3, "file": "pa.js", "sources": ["../../../../../packages/locale/lang/pa.ts"], "sourcesContent": ["export default {\n  name: 'pa',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'تایید',\n      clear: 'پاکول',\n    },\n    datepicker: {\n      now: 'اوس',\n      today: 'نن',\n      cancel: 'ردول',\n      clear: 'پاکول',\n      confirm: 'تایید',\n      selectDate: 'نیټه وټاکئ',\n      selectTime: 'وخت وټاکئ',\n      startDate: 'پیل نیټه',\n      startTime: 'د پيل وخت',\n      endDate: 'د پای نیټه',\n      endTime: 'د پای وخت',\n      prevYear: 'تیر کال',\n      nextYear: 'راتلونکی کال',\n      prevMonth: 'تیره میاشت',\n      nextMonth: 'راتلونکې میاشت',\n      year: 'کال',\n      month1: 'جنوري',\n      month2: 'فبروري',\n      month3: 'مارچ',\n      month4: 'اپریل',\n      month5: 'می',\n      month6: 'جون',\n      month7: 'جولای',\n      month8: 'اګست',\n      month9: 'سپتمبر',\n      month10: 'اکتوبر',\n      month11: 'نومبر',\n      month12: 'دسمبر',\n      // week: 'week',\n      weeks: {\n        sun: 'یکشنبه',\n        mon: 'دوشنبه',\n        tue: 'سه​ شنبه',\n        wed: 'چهارشنبه',\n        thu: 'پنج​شنبه',\n        fri: 'جمعه',\n        sat: 'شنبه',\n      },\n      months: {\n        jan: 'جنوري',\n        feb: 'فبروري',\n        mar: 'مارچ',\n        apr: 'اپریل',\n        may: 'می',\n        jun: 'جون',\n        jul: 'جولای',\n        aug: 'اګست',\n        sep: 'سپتمبر',\n        oct: 'اکتوبر',\n        nov: 'نومبر',\n        dec: 'دسمبر',\n      },\n    },\n    select: {\n      loading: 'بار کول',\n      noMatch: 'هیڅه ونه موندل شول',\n      noData: 'هیڅ معلومات نشته',\n      placeholder: 'ځای لرونکی',\n    },\n    mention: {\n      loading: 'بار کول',\n    },\n    cascader: {\n      noMatch: 'هیڅه ونه موندل شول',\n      loading: 'بار کول',\n      placeholder: 'ځای لرونکی',\n      noData: 'هیڅ معلومات نشته',\n    },\n    pagination: {\n      goto: 'ورتګ',\n      pagesize: '/د پاڼې اندازه',\n      total: 'مجموعه {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'عنوان',\n      confirm: 'تایید',\n      cancel: 'لغوه کول',\n      error: 'تيروتنه',\n    },\n    upload: {\n      deleteTip: 'د حذف کولو لپاره پاکه تڼۍ فشار کړئ',\n      delete: 'ړنګول',\n      preview: 'مخکتنه',\n      continue: 'ادامه',\n    },\n    table: {\n      emptyText: 'هیڅ معلومات ونه موندل شول',\n      confirmFilter: 'تایید',\n      resetFilter: 'پاکول',\n      clearFilter: 'ټول',\n      sumText: 'مجموعه',\n    },\n    tree: {\n      emptyText: 'هیڅ معلومات ونه موندل شول',\n    },\n    transfer: {\n      noMatch: 'هیڅه ونه موندل شول',\n      noData: 'هیڅ معلومات نشته',\n      titles: ['لیسټ 1', 'لیسټ 2'],\n      filterPlaceholder: 'د متن کلیمې دننه کړئ',\n      noCheckedFormat: '{total} توکي',\n      hasCheckedFormat: '{checked} توکي از {total} توکي ټاکل شوی دي',\n    },\n    image: {\n      error: 'د انځور پورته کولو کې ستونزه',\n    },\n    pageHeader: {\n      title: 'بیرته راتګ',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,UAAU,EAAE,yDAAyD;AAC3E,MAAM,UAAU,EAAE,mDAAmD;AACrE,MAAM,SAAS,EAAE,6CAA6C;AAC9D,MAAM,SAAS,EAAE,8CAA8C;AAC/D,MAAM,OAAO,EAAE,oDAAoD;AACnE,MAAM,OAAO,EAAE,8CAA8C;AAC7D,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,QAAQ,EAAE,qEAAqE;AACrF,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,SAAS,EAAE,iFAAiF;AAClG,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,6CAA6C;AAC1D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,kDAAkD;AAC/D,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,0BAA0B;AACvC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,0BAA0B;AACvC,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,sCAAsC;AACnD,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,QAAQ,GAAG,EAAE,gCAAgC;AAC7C,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,MAAM,EAAE,wFAAwF;AACtG,MAAM,WAAW,EAAE,yDAAyD;AAC5E,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,uCAAuC;AACtD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,WAAW,EAAE,yDAAyD;AAC5E,MAAM,MAAM,EAAE,wFAAwF;AACtG,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,0BAA0B;AACtC,MAAM,QAAQ,EAAE,uEAAuE;AACvF,MAAM,KAAK,EAAE,8CAA8C;AAC3D,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,6CAA6C;AAC3D,MAAM,KAAK,EAAE,4CAA4C;AACzD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,2KAA2K;AAC5L,MAAM,MAAM,EAAE,gCAAgC;AAC9C,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,QAAQ,EAAE,gCAAgC;AAChD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,oIAAoI;AACrJ,MAAM,aAAa,EAAE,gCAAgC;AACrD,MAAM,WAAW,EAAE,gCAAgC;AACnD,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,sCAAsC;AACrD,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,oIAAoI;AACrJ,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+FAA+F;AAC9G,MAAM,MAAM,EAAE,wFAAwF;AACtG,MAAM,MAAM,EAAE,CAAC,4BAA4B,EAAE,4BAA4B,CAAC;AAC1E,MAAM,iBAAiB,EAAE,sGAAsG;AAC/H,MAAM,eAAe,EAAE,kCAAkC;AACzD,MAAM,gBAAgB,EAAE,2IAA2I;AACnK,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,iJAAiJ;AAC9J,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,yDAAyD;AACtE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}
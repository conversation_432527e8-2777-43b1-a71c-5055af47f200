{"version": 3, "file": "browser.js", "sources": ["../../../../packages/utils/browser.ts"], "sourcesContent": ["import { isClient, isIOS } from '@vueuse/core'\n\nexport const isFirefox = (): boolean =>\n  isClient && /firefox/i.test(window.navigator.userAgent)\n\nexport { isClient, isIOS }\n"], "names": ["isClient"], "mappings": ";;;;;;AACY,MAAC,SAAS,GAAG,MAAMA,aAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS;;;;;;;;;;;;"}
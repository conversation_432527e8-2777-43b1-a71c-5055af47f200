'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var globalNode = require('./global-node.js');
var icon = require('./icon.js');
var install = require('./install.js');
var refs = require('./refs.js');
var size = require('./size.js');
var validator = require('./validator.js');
var vnode = require('./vnode.js');
var runtime = require('./props/runtime.js');



exports.changeGlobalNodesTarget = globalNode.changeGlobalNodesTarget;
exports.createGlobalNode = globalNode.createGlobalNode;
exports.removeGlobalNode = globalNode.removeGlobalNode;
exports.CloseComponents = icon.CloseComponents;
exports.TypeComponents = icon.TypeComponents;
exports.TypeComponentsMap = icon.TypeComponentsMap;
exports.ValidateComponentsMap = icon.ValidateComponentsMap;
exports.iconPropType = icon.iconPropType;
exports.withInstall = install.withInstall;
exports.withInstallDirective = install.withInstallDirective;
exports.withInstallFunction = install.withInstallFunction;
exports.withNoopInstall = install.withNoopInstall;
exports.composeRefs = refs.composeRefs;
exports.getComponentSize = size.getComponentSize;
exports.isValidComponentSize = validator.isValidComponentSize;
exports.isValidDatePickType = validator.isValidDatePickType;
exports.PatchFlags = vnode.PatchFlags;
exports.ensureOnlyChild = vnode.ensureOnlyChild;
exports.flattedChildren = vnode.flattedChildren;
exports.getFirstValidNode = vnode.getFirstValidNode;
exports.getNormalizedProps = vnode.getNormalizedProps;
exports.isComment = vnode.isComment;
exports.isFragment = vnode.isFragment;
exports.isTemplate = vnode.isTemplate;
exports.isText = vnode.isText;
exports.isValidElementNode = vnode.isValidElementNode;
exports.renderBlock = vnode.renderBlock;
exports.renderIf = vnode.renderIf;
exports.buildProp = runtime.buildProp;
exports.buildProps = runtime.buildProps;
exports.definePropType = runtime.definePropType;
exports.epPropKey = runtime.epPropKey;
exports.isEpProp = runtime.isEpProp;
//# sourceMappingURL=index.js.map

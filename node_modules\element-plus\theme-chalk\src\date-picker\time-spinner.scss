@use '../mixins/mixins' as *;
@use '../common/var' as *;

@include b(time-spinner) {
  &.has-seconds {
    .#{$namespace}-time-spinner__wrapper {
      width: 33.3%;
    }
  }

  @include e(wrapper) {
    max-height: 192px;
    overflow: auto;
    display: inline-block;
    width: 50%;
    vertical-align: top;
    position: relative;

    &.#{$namespace}-scrollbar__wrap:not(.#{$namespace}-scrollbar__wrap--hidden-default) {
      padding-bottom: 15px;
    }

    @include when(arrow) {
      box-sizing: border-box;
      text-align: center;
      overflow: hidden;

      .#{$namespace}-time-spinner__list {
        transform: translateY(-32px);
      }

      .#{$namespace}-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
        background: getCssVar('fill-color', 'light');
        cursor: default;
      }
    }
  }

  @include e(arrow) {
    font-size: 12px;
    color: getCssVar('text-color', 'secondary');
    position: absolute;
    left: 0;
    width: 100%;
    z-index: getCssVar('index-normal');
    text-align: center;
    height: 30px;
    line-height: 30px;
    cursor: pointer;

    &:hover {
      color: getCssVar('color-primary');
    }

    &.arrow-up {
      top: 10px;
    }

    &.arrow-down {
      bottom: 10px;
    }
  }

  @include e(input) {
    &.#{$namespace}-input {
      width: 70%;

      .#{$namespace}-input__inner {
        padding: 0;
        text-align: center;
      }
    }
  }

  @include e(list) {
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center;

    &::after,
    &::before {
      content: '';
      display: block;
      width: 100%;
      height: 80px;
    }
  }

  @include e(item) {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: getCssVar('text-color', 'regular');

    &:hover:not(.is-disabled):not(.is-active) {
      background: getCssVar('fill-color', 'light');
      cursor: pointer;
    }

    &.is-active:not(.is-disabled) {
      color: getCssVar('text-color', 'primary');
      font-weight: bold;
    }

    &.is-disabled {
      color: getCssVar('text-color', 'placeholder');
      cursor: not-allowed;
    }
  }
}
